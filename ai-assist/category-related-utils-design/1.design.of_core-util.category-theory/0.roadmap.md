## lets follow the roadmap in parent folder
- we will outline the progression of evolution to our goal os an fs system based on lingustic exutable
trees and transformer/functor drven domains
- first, we need to get the basic category theory terms established - so a core package. 
- its a nested compound package
- core will create basic concepts/types of morphism, functor, catTransformer,subCatTransformer
- but perhaps we need trees first - just follow the sequence of the prev chat, as i outlined thru comments
- at the bottom of each script
- implementation we will adjiust, but general progression, lets start with what i outlined
- follow careful thru the sequence, as i changed my mind and cotradicted my self thru the sequence
- but our fs should be time aware, as it will evolve in time
- i dont know if it should care about space yet
-  its a tree hierarchy and spaces come later, prbly mostly at runtime. but well decide as we go
- we should develop a space functor branch, as described, and leave it as a stub, to be used later


