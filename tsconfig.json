{
  "$schema": "https://json.schemastore.org/tsconfig",
  "compilerOptions": {
    "target": "ESNext",
    "module": "ESNext",
    "moduleResolution": "Bundler",
    "strict": true,
    "skipLibCheck": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "esModuleInterop": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "verbatimModuleSyntax": true,
    "jsx": "react-jsx",
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@root/*": ["./*"]
    },
    "composite": true,
    "incremental": true
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.tsx"
  ],
  "exclude": [
    "**/node_modules",
    "**/dist",
    "**/.turbo",
    "**/build",
    "**/coverage",
    "**/*.examples.ts",  // Exclude all example files
    "**/*.examples.tsx", // Exclude all example React files
    "**/examples/**",    // Exclude example directories
    "**/claude",
    "**/docs",
    "**/patches",
    "**/aggregated",
    "**/._*"
  ]
}