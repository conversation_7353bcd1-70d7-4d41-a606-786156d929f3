npm run build &&
rsync -r -v -e ssh \
    --exclude='node_modules' \
    --exclude='mongo-entrypoint' \
    --exclude='mongoconfig' \
    --exclude='mongodb' \
    --exclude=".git" \
    --exclude=".idea" \
    --exclude="public" \
    --exclude="data" \
    --exclude="frontend" \
     ./ root@185.245.107.53:/root/miniapp-backend &&
ssh root@185.245.107.53 "source ~/.nvm/nvm.sh && NODE_ENV=production pm2 restart mini --update-env && pm2 logs mini"