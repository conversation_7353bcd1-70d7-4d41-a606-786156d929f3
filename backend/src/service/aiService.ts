import { Anthropic } from '@anthropic-ai/sdk';
import { config } from '../config';
import OpenAI from 'openai';
import { sleep } from '@anthropic-ai/sdk/core';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { AILog } from '../models/AILog';

abstract class AIServiceBase {
  protected abstract model: string;
  protected abstract defaultParams: any;

  async ask(userId: string, prompt: string, system: string = ''): Promise<string> {
    const startTime = Date.now();
    try {
      const messages = this.buildMessages(prompt, system);
      const completion = await this.createCompletion(messages);
      const response = this.extractResponseContent(completion);

      await this.logInteraction(userId, `${system}\n\n${prompt}`, response, 'ask');
      return response;
    } catch (error) {
      console.error('AI Service error:', error);
      throw error;
    }
  }

  async askJSON(userId: string, prompt: string, system: string = '', options?: any): Promise<any> {
    if (options?.savedId) {
      return (await AILog.findById(options.savedId)).response;
    }

    const startTime = Date.now();
    try {
      const messages = this.buildMessages(prompt, system);
      const completion = await this.createCompletion(messages, {
        ...options,
        responseFormat: 'json',
      });
      const response = this.extractJSONResponse(completion);

      await this.logInteraction(userId, `${system}\n\n${prompt}`, JSON.stringify(response), 'askJSON');
      return response;
    } catch (error) {
      console.error('AI Service error:', error);
      throw error;
    }
  }

  buildMessages(prompt: string, system: string) {
    return [...(system ? [{ role: 'system', content: system }] : []), { role: 'user', content: prompt }];
  }

  private async logInteraction(userId: string, prompt: string, response: string, method: string) {
    await AILog.create({
      userId,
      prompt,
      response,
      aiModel: this.model,
      method,
    });
  }

  protected abstract createCompletion(messages: any[], options?: any): Promise<any>;
  protected abstract extractResponseContent(completion: any): string;
  protected abstract extractJSONResponse(completion: any): any;
}

class ClaudeService extends AIServiceBase {
  protected model = 'claude-3-5-sonnet-20240620';
  protected defaultParams = {
    max_tokens: 8000,
    temperature: 0.5,
  };
  private client: Anthropic;
  system = '';

  constructor(apiKey: string) {
    super();
    this.client = new Anthropic({ apiKey });
  }

  protected async createCompletion(messages: any[], options?: any, retryLimit: number = 5) {
    delete options.responseFormat;

    return this.client.messages
      .create({
        ...this.defaultParams,
        ...options,
        model: this.model,
        messages,
        system: this.system,
      })
      .catch(async (error: any) => {
        if (retryLimit <= 0) throw error;

        console.log('Error', error.message);

        if (error.headers['x-should-retry'] === 'true') {
          console.log('Retrying', retryLimit);
          await sleep(2000);

          return this.createCompletion(messages, options, retryLimit - 1);
        }
      });
  }

  buildMessages(prompt: string, system: string) {
    this.system = system;
    return [{ role: 'user', content: prompt }];
  }

  protected extractResponseContent(completion: any) {
    return completion.content[0].text;
  }

  protected extractJSONResponse(completion: any) {
    try {
      return JSON.parse(completion.content[0].text);
    } catch (error) {
      throw new Error('Failed to parse Claude response as JSON');
    }
  }
}

class OpenAIService extends AIServiceBase {
  protected model = 'o3-mini';
  protected defaultParams = {
    max_tokens: 8000,
    temperature: 0.5,
  };
  private client: OpenAI;

  constructor(apiKey: string) {
    super();
    this.client = new OpenAI({
      apiKey,
    });
  }

  protected async createCompletion(messages: any[], options?: any) {
    return this.client.chat.completions.create({
      ...this.defaultParams,
      ...options,
      model: this.model,
      messages,
      response_format: options?.responseFormat === 'json' ? { type: 'json_object' } : undefined,
    });
  }

  protected extractResponseContent(completion: any) {
    return completion.choices[0].message.content;
  }

  protected extractJSONResponse(completion: any) {
    try {
      return JSON.parse(completion.choices[0].message.content);
    } catch (error) {
      throw new Error('Failed to parse OpenAI response as JSON');
    }
  }
}

class GeminiService extends AIServiceBase {
  protected model = 'gemini-2.0-flash';
  protected defaultParams = {
    maxOutputTokens: 8000,
    temperature: 0.5,
  };
  private client: GoogleGenerativeAI;
  private generativeModel: any;

  constructor(apiKey: string) {
    super();
    this.client = new GoogleGenerativeAI(apiKey);
    this.generativeModel = this.client.getGenerativeModel({
      model: this.model,
      generationConfig: this.defaultParams,
    });
  }

  protected async createCompletion(messages: any[], options?: any, retryLimit = 3) {
    delete options.responseFormat;

    try {
      // Gemini uses different message structure, convert from standard format
      const converted = messages.map((msg) => ({
        role: msg.role === 'user' ? 'user' : 'model',
        parts: [{ text: msg.content }],
      }));

      const chat = this.generativeModel.startChat({
        history: converted,
        generationConfig: {
          ...this.defaultParams,
          ...options,
        },
      });

      const result = await chat.sendMessage(messages[messages.length - 1].content);
      return result;
    } catch (error: any) {
      if (retryLimit > 0 && error.message.includes('quota')) {
        await sleep(2000);
        return this.createCompletion(messages, options, retryLimit - 1);
      }
      throw error;
    }
  }

  protected extractResponseContent(completion: any) {
    return completion.response.text();
  }

  protected extractJSONResponse(completion: any) {
    try {
      const text = this.extractResponseContent(completion);
      const jsonString = text.replace(/^```json\s*|```$/g, '');
      return JSON.parse(jsonString);
    } catch (error) {
      throw new Error('Failed to parse Gemini response as JSON');
    }
  }

  buildMessages(prompt: string, system: string) {
    // Gemini doesn't support system messages directly, prepend to prompt
    return [{ role: 'user', content: system ? `${system}\n\n${prompt}` : prompt }];
  }
}

// Export both services
export const aiService = new ClaudeService(config.CLAUDE_API_KEY);
// export const aiService = new OpenAIService(config.OPENAI_API_KEY);
// export const aiService = new GeminiService(config.GEMINI_API_KEY);
