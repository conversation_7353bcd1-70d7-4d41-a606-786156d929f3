import data from '@emoji-mart/data';
import EmojiPicker from '@emoji-mart/react';
import { Button, Input, Popover } from 'antd';

import React, { FC } from 'react';

export const EmojiInput: FC<any> = (props) => {
  const onEmojiClick = (emojiData: any) => {
    props.onChange(emojiData.native);
  };

  return (
    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
      <Input {...props} style={{ width: '80px' }} />
      <Popover styles={{ body: { padding: 0 } }} placement='top' content={<EmojiPicker data={data} theme='light' onEmojiSelect={onEmojiClick} />}>
        <Button>Select Emoji</Button>
      </Popover>
    </div>
  );
};
