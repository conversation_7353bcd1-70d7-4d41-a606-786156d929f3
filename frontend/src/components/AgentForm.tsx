import { Button, Form, FormInstance, Input, InputNumber, Select } from 'antd';
import { observer } from 'mobx-react-lite';
import React, { useEffect } from 'react';
import { Entity } from '../agentsim/mods/entity.ts';
import { Serializable, serialize } from '../agentsim/mods/serializable.ts';
import { useObserve } from '../utils/use-observe.ts';
import { EmojiInput } from './EmojiInput';
import { ComponentSelectorFormItem } from './ComponentSelector.tsx';

const { Option } = Select;

export interface AgentFormValues {
  name: string;
  description: string;
  radius: number;
  icon: string;
  aiProvider: 'openai' | 'anthropic' | 'gemini' | 'ollama';
  components?: string[]; // IDs of additional components
}

export interface AddAgentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (values: AgentFormValues) => void;
}

export const AgentForm: React.FC<{
  entity?: Entity;
  form?: FormInstance<any>;
  onSubmit?: (values: AgentFormValues, form: FormInstance<any>) => void;
}> = observer(({ onSubmit, entity, form: baseForm }) => {
  const [initialValues, setInitialValues] = React.useState<any>(() => (entity ? serialize(entity) : undefined));
  const [form] = Form.useForm(baseForm);

  useEffect(() => {
    if (entity) {
      setInitialValues(serialize(entity));
      form.resetFields();
    }
  }, [entity, form]);

  const hasEntity = !!entity;

  return (
    <Form
      form={form}
      onFinish={(values) => {
        if (onSubmit) {
          // Map form values to AgentFormValues format if needed
          const formattedValues: AgentFormValues = {
            name: hasEntity ? values.Descriptive?.name : values.name,
            description: hasEntity ? values.Descriptive?.description : values.description, 
            radius: hasEntity ? values.Movable?.radius : values.radius,
            icon: hasEntity ? values.Descriptive?.icon : values.icon,
            aiProvider: hasEntity ? values.AIProvider?.aiProvider : values.aiProvider,
            components: values.components
          };
          
          onSubmit(formattedValues, form);
        } else if (entity) {
          entity?.as(Serializable).deserialize(values);
        }
      }}
      initialValues={initialValues || {}}
      layout="vertical"
    >
      {hasEntity ? (
        <>
          <Form.Item name={['Descriptive', 'name']} label='Name' rules={[{ required: true }]}>
            <Input />
          </Form.Item>
          <Form.Item name={['Descriptive', 'icon']} label='Icon' rules={[{ required: true }]}>
            <EmojiInput />
          </Form.Item>
          <Form.Item name={['Descriptive', 'description']} label='Description' rules={[{ required: true }]}>
            <Input.TextArea />
          </Form.Item>
          <Form.Item name={['Movable', 'radius']} label='Radius' rules={[{ required: true }]}>
            <InputNumber min={1} max={200} />
          </Form.Item>
          <Form.Item name={['AIProvider', 'aiProvider']} label='AI Provider' rules={[{ required: true }]}>
            <Select>
              <Option value='openai'>OpenAI</Option>
              <Option value='anthropic'>Anthropic</Option>
              <Option value='gemini'>Gemini</Option>
              <Option value='ollama'>Ollama</Option>
            </Select>
          </Form.Item>
        </>
      ) : (
        <>
          <Form.Item name='name' label='Name' rules={[{ required: true }]}>
            <Input />
          </Form.Item>
          <Form.Item name='icon' label='Icon' rules={[{ required: true }]}>
            <EmojiInput />
          </Form.Item>
          <Form.Item name='description' label='Description' rules={[{ required: true }]}>
            <Input.TextArea />
          </Form.Item>
          <Form.Item name='radius' label='Radius' rules={[{ required: true }]}>
            <InputNumber min={1} max={200} />
          </Form.Item>
          <Form.Item name='aiProvider' label='AI Provider' rules={[{ required: true }]} initialValue="openai">
            <Select>
              <Option value='openai'>OpenAI</Option>
              <Option value='anthropic'>Anthropic</Option>
              <Option value='gemini'>Gemini</Option>
              <Option value='ollama'>Ollama</Option>
            </Select>
          </Form.Item>
        </>
      )}

      {/* Component selector */}
      <ComponentSelectorFormItem entityType={entity?.$type || 'agent'} />

      {/* Only show the button when there's no baseForm and we're not in create mode */}
      {!baseForm && entity && (
        <Form.Item>
          <Button htmlType="submit" type="primary">
            Update
          </Button>
        </Form.Item>
      )}
    </Form>
  );
});
