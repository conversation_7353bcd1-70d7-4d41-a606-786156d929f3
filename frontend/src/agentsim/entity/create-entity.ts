import { Entity } from '../mods/entity';
import { Serializable } from '../mods/serializable';
import { entityTypeDb } from './entity-type-db';
import { availableComponents } from './component-registry';

export let lastId = 0;
export function setLastId(last: number) {
  lastId = last;
}

export function getLastId(): number {
  return lastId++;
}

export function makeId(name: string = 'entity'): string {
  const slug = name.toLowerCase().replace(' ', '-').replace(/[^\w-]/m, '');
  return slug + '-' + getLastId();
}

export const createEntity = (
  type: string, 
  data?: any, 
  customComponents?: string[] // IDs of additional components to add
): Entity => {
  const id = data?.$id || makeId(type);

  const comps = [...(entityTypeDb[type]?.components || [])];
  
  // Add custom components if specified
  if (customComponents && customComponents.length > 0) {
    // Get component definitions by ID
    const additionalComponents = customComponents
      .map(compId => availableComponents.find(comp => comp.id === compId)?.component)
      .filter(Boolean) as any[];
    
    // Add only components that aren't already included
    additionalComponents.forEach(comp => {
      if (!comps.includes(comp)) {
        comps.push(comp);
      }
    });
  }

  if (!comps.length) throw new Error(`Entity type ${type} does not exist and no components were provided`);

  const entity = new Entity(type, id, comps);

  if (data) entity.get(Serializable).deserialize(data);

  return entity;
}
