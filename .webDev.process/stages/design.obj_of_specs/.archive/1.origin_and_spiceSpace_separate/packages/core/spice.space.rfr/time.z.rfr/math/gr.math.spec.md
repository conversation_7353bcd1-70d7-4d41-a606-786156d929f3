# General Relativity Mathematics Specification

## Overview

This specification defines the mathematical machinery for general relativity calculations in the time.rfr package. These advanced mathematical tools are tucked away from the main API but can be accessed when needed for complex calculations.

## Type Definition

```typescript
/**
 * Namespace for general relativity mathematics
 */
export namespace GRMath {
  /**
   * Tensor operations
   */
  export namespace Tensor {
    /**
     * Creates a tensor of specified rank and dimension
     * 
     * @param rank Tensor rank
     * @param dimension Tensor dimension
     * @param initializer Function to initialize components
     * @returns A new tensor
     */
    export function create(
      rank: number,
      dimension: number,
      initializer?: (indices: number[]) => number
    ): Tensor;
    
    /**
     * Adds two tensors
     * 
     * @param a First tensor
     * @param b Second tensor
     * @returns The sum tensor
     */
    export function add(a: Tensor, b: Tensor): Tensor;
    
    /**
     * Subtracts one tensor from another
     * 
     * @param a First tensor
     * @param b Second tensor
     * @returns The difference tensor
     */
    export function subtract(a: Tensor, b: Tensor): Tensor;
    
    /**
     * Multiplies a tensor by a scalar
     * 
     * @param tensor Tensor
     * @param scalar Scalar
     * @returns The scaled tensor
     */
    export function multiplyByScalar(tensor: Tensor, scalar: number): Tensor;
    
    /**
     * Contracts a tensor on two indices
     * 
     * @param tensor Tensor
     * @param index1 First index
     * @param index2 Second index
     * @returns The contracted tensor
     */
    export function contract(tensor: Tensor, index1: number, index2: number): Tensor;
    
    /**
     * Performs tensor product of two tensors
     * 
     * @param a First tensor
     * @param b Second tensor
     * @returns The tensor product
     */
    export function product(a: Tensor, b: Tensor): Tensor;
    
    /**
     * Raises an index of a tensor
     * 
     * @param tensor Tensor
     * @param index Index to raise
     * @param metric Metric tensor
     * @returns The tensor with the index raised
     */
    export function raiseIndex(tensor: Tensor, index: number, metric: Tensor): Tensor;
    
    /**
     * Lowers an index of a tensor
     * 
     * @param tensor Tensor
     * @param index Index to lower
     * @param metric Metric tensor
     * @returns The tensor with the index lowered
     */
    export function lowerIndex(tensor: Tensor, index: number, metric: Tensor): Tensor;
  }
  
  /**
   * Metric operations
   */
  export namespace Metric {
    /**
     * Creates a Minkowski metric
     * 
     * @param dimension Dimension (default: 4)
     * @returns A Minkowski metric tensor
     */
    export function createMinkowski(dimension: number = 4): Tensor;
    
    /**
     * Creates a Schwarzschild metric
     * 
     * @param mass Central mass
     * @returns A Schwarzschild metric tensor
     */
    export function createSchwarzschild(mass: number): Tensor;
    
    /**
     * Creates a Friedmann-Lemaître-Robertson-Walker metric
     * 
     * @param scaleFactor Scale factor function
     * @param curvature Spatial curvature
     * @returns An FLRW metric tensor
     */
    export function createFLRW(
      scaleFactor: (time: number) => number,
      curvature: number
    ): Tensor;
    
    /**
     * Creates a custom metric
     * 
     * @param components Components of the metric tensor
     * @returns A custom metric tensor
     */
    export function createCustom(components: number[][]): Tensor;
    
    /**
     * Calculates the inverse metric
     * 
     * @param metric Metric tensor
     * @returns The inverse metric tensor
     */
    export function inverse(metric: Tensor): Tensor;
    
    /**
     * Calculates the determinant of the metric
     * 
     * @param metric Metric tensor
     * @returns The determinant
     */
    export function determinant(metric: Tensor): number;
  }
  
  /**
   * Curvature operations
   */
  export namespace Curvature {
    /**
     * Calculates the Christoffel symbols for a metric
     * 
     * @param metric Metric tensor
     * @returns The Christoffel symbols
     */
    export function christoffelSymbols(metric: Tensor): Tensor;
    
    /**
     * Calculates the Riemann curvature tensor
     * 
     * @param christoffel Christoffel symbols
     * @param metric Metric tensor
     * @returns The Riemann tensor
     */
    export function riemannTensor(christoffel: Tensor, metric: Tensor): Tensor;
    
    /**
     * Calculates the Ricci tensor
     * 
     * @param riemann Riemann tensor
     * @returns The Ricci tensor
     */
    export function ricciTensor(riemann: Tensor): Tensor;
    
    /**
     * Calculates the Ricci scalar
     * 
     * @param ricci Ricci tensor
     * @param metric Metric tensor
     * @returns The Ricci scalar
     */
    export function ricciScalar(ricci: Tensor, metric: Tensor): number;
    
    /**
     * Calculates the Einstein tensor
     * 
     * @param ricci Ricci tensor
     * @param ricciScalar Ricci scalar
     * @param metric Metric tensor
     * @returns The Einstein tensor
     */
    export function einsteinTensor(
      ricci: Tensor,
      ricciScalar: number,
      metric: Tensor
    ): Tensor;
    
    /**
     * Calculates the Weyl tensor
     * 
     * @param riemann Riemann tensor
     * @param ricci Ricci tensor
     * @param ricciScalar Ricci scalar
     * @param metric Metric tensor
     * @param dimension Dimension
     * @returns The Weyl tensor
     */
    export function weylTensor(
      riemann: Tensor,
      ricci: Tensor,
      ricciScalar: number,
      metric: Tensor,
      dimension: number
    ): Tensor;
  }
  
  /**
   * Geodesic operations
   */
  export namespace Geodesic {
    /**
     * Calculates the geodesic equation
     * 
     * @param christoffel Christoffel symbols
     * @returns A function that gives the acceleration for a given position and velocity
     */
    export function equation(
      christoffel: Tensor
    ): (position: number[], velocity: number[]) => number[];
    
    /**
     * Solves the geodesic equation
     * 
     * @param equation Geodesic equation
     * @param initialPosition Initial position
     * @param initialVelocity Initial velocity
     * @param steps Number of steps
     * @param stepSize Step size
     * @returns The geodesic path
     */
    export function solve(
      equation: (position: number[], velocity: number[]) => number[],
      initialPosition: number[],
      initialVelocity: number[],
      steps: number,
      stepSize: number
    ): Array<{ position: number[], velocity: number[] }>;
    
    /**
     * Calculates the proper length of a path
     * 
     * @param path Array of positions
     * @param metric Metric tensor
     * @returns The proper length
     */
    export function properLength(path: number[][], metric: Tensor): number;
    
    /**
     * Calculates the proper time along a path
     * 
     * @param path Array of positions
     * @param metric Metric tensor
     * @returns The proper time
     */
    export function properTime(path: number[][], metric: Tensor): number;
  }
  
  /**
   * Gravity operations
   */
  export namespace Gravity {
    /**
     * Calculates the gravitational potential
     * 
     * @param mass Mass
     * @param distance Distance
     * @returns The gravitational potential
     */
    export function potential(mass: number, distance: number): number;
    
    /**
     * Calculates the gravitational field
     * 
     * @param mass Mass
     * @param position Position relative to the mass
     * @returns The gravitational field vector
     */
    export function field(mass: number, position: number[]): number[];
    
    /**
     * Calculates the gravitational force between two masses
     * 
     * @param mass1 First mass
     * @param mass2 Second mass
     * @param distance Distance between the masses
     * @returns The gravitational force
     */
    export function force(mass1: number, mass2: number, distance: number): number;
    
    /**
     * Calculates the escape velocity
     * 
     * @param mass Mass
     * @param distance Distance from the center of the mass
     * @returns The escape velocity
     */
    export function escapeVelocity(mass: number, distance: number): number;
    
    /**
     * Calculates the time dilation factor
     * 
     * @param mass Mass
     * @param distance Distance from the center of the mass
     * @returns The time dilation factor
     */
    export function timeDilation(mass: number, distance: number): number;
  }
  
  /**
   * Relativistic operations
   */
  export namespace Relativity {
    /**
     * Calculates the Lorentz transformation matrix
     * 
     * @param velocity Velocity vector
     * @returns The Lorentz transformation matrix
     */
    export function lorentzTransformation(velocity: number[]): Tensor;
    
    /**
     * Transforms coordinates between reference frames
     * 
     * @param coordinates Coordinates in the original frame
     * @param transformation Lorentz transformation matrix
     * @returns Coordinates in the new frame
     */
    export function transformCoordinates(
      coordinates: number[],
      transformation: Tensor
    ): number[];
    
    /**
     * Calculates the length contraction factor
     * 
     * @param velocity Velocity
     * @returns The length contraction factor
     */
    export function lengthContraction(velocity: number): number;
    
    /**
     * Calculates the time dilation factor
     * 
     * @param velocity Velocity
     * @returns The time dilation factor
     */
    export function timeDilation(velocity: number): number;
    
    /**
     * Calculates the relativistic addition of velocities
     * 
     * @param v1 First velocity
     * @param v2 Second velocity
     * @returns The combined velocity
     */
    export function addVelocities(v1: number, v2: number): number;
  }
}
```

## Usage

The GRMath namespace provides advanced mathematical tools for general relativity calculations. These tools are tucked away from the main API but can be accessed when needed for complex calculations.

For example, to calculate the curvature of spacetime around a massive object:

```typescript
// Get the underlying model
const nodeModel = nodeAPI.getModel();

// Create a Schwarzschild metric
const metric = GRMath.Metric.createSchwarzschild(nodeModel.mass);

// Calculate the Christoffel symbols
const christoffel = GRMath.Curvature.christoffelSymbols(metric);

// Calculate the Riemann tensor
const riemann = GRMath.Curvature.riemannTensor(christoffel, metric);

// Calculate the Ricci tensor
const ricci = GRMath.Curvature.ricciTensor(riemann);

// Calculate the Ricci scalar
const ricciScalar = GRMath.Curvature.ricciScalar(ricci, metric);

// Calculate the Einstein tensor
const einstein = GRMath.Curvature.einsteinTensor(ricci, ricciScalar, metric);
```

This mathematical machinery is not needed for common operations, which are handled by the simpler API, but it's available when needed for advanced calculations.

## Implementation Approach

The implementation of this mathematical machinery should:

1. **Focus on Correctness**: Ensure that the calculations are mathematically correct
2. **Optimize for Performance**: Use efficient algorithms for tensor calculations
3. **Support Numerical Stability**: Handle numerical issues that can arise in tensor calculations
4. **Provide Clear Documentation**: Document the mathematical basis for each function

The implementation can leverage existing libraries for numerical calculations, but it should provide a consistent interface that aligns with the concepts of general relativity.

## File Location

This mathematical machinery should be placed in:
```
spicetime-architecture/packages/utils/origin.rfr/time.rfr/math/gr.math.ts
```

## Related Files

- `math/tensor.ts`: Implementation of tensor operations
- `math/metric.ts`: Implementation of metric operations
- `math/curvature.ts`: Implementation of curvature operations
- `math/geodesic.ts`: Implementation of geodesic operations
- `math/gravity.ts`: Implementation of gravity operations
- `math/relativity.ts`: Implementation of relativistic operations
