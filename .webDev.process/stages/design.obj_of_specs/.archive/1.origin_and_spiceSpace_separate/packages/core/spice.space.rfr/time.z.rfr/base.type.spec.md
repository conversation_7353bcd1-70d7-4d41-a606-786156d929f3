# time.rfr Base Types Specification

## Overview

This specification defines the base types for the time.rfr package. These types form the foundation for representing spacetime in the SpiceTime architecture.

## Type Definitions

```typescript
/**
 * Represents spacetime coordinates
 */
export interface SpacetimeCoordinates {
  /**
   * Spatial coordinates (3D)
   */
  readonly space: [number, number, number];
  
  /**
   * Temporal coordinate
   */
  readonly time: number;
}

/**
 * Type of metric tensor
 */
export enum MetricType {
  /**
   * Flat spacetime (Minkowski metric)
   */
  MINKOWSKI,
  
  /**
   * Schwarzschild metric (spherically symmetric gravitational field)
   */
  SCHWARZSCHILD,
  
  /**
   * <PERSON><PERSON>-<PERSON>-<PERSON>-<PERSON> metric (expanding universe)
   */
  FLRW,
  
  /**
   * Custom metric defined by components
   */
  CUSTOM
}

/**
 * Type of spacetime path
 */
export enum PathType {
  /**
   * Timelike path (can be traversed by massive objects)
   */
  TIMELIKE,
  
  /**
   * Lightlike path (can be traversed by massless objects)
   */
  LIGHTLIKE,
  
  /**
   * Spacelike path (cannot be traversed by physical objects)
   */
  SPACELIKE,
  
  /**
   * Geodesic path (shortest path between two points)
   */
  GEODESIC
}

/**
 * Represents a tensor of arbitrary rank
 */
export interface Tensor {
  /**
   * The components of the tensor
   */
  readonly components: any;
  
  /**
   * The rank of the tensor
   */
  readonly rank: number;
  
  /**
   * The dimension of the tensor
   */
  readonly dimension: number;
  
  /**
   * Gets a component of the tensor
   * 
   * @param indices Indices
   * @returns The component value
   */
  getComponent(...indices: number[]): number;
  
  /**
   * Sets a component of the tensor
   * 
   * @param value Value to set
   * @param indices Indices
   * @returns The updated tensor
   */
  setComponent(value: number, ...indices: number[]): Tensor;
  
  /**
   * Contracts the tensor
   * 
   * @param index1 First index
   * @param index2 Second index
   * @returns The contracted tensor
   */
  contract(index1: number, index2: number): Tensor;
  
  /**
   * Raises an index of the tensor
   * 
   * @param index Index to raise
   * @param metric Metric tensor
   * @returns The tensor with the index raised
   */
  raiseIndex(index: number, metric: Tensor): Tensor;
  
  /**
   * Lowers an index of the tensor
   * 
   * @param index Index to lower
   * @param metric Metric tensor
   * @returns The tensor with the index lowered
   */
  lowerIndex(index: number, metric: Tensor): Tensor;
}

/**
 * Creates spacetime coordinates
 * 
 * @param space Spatial coordinates
 * @param time Temporal coordinate
 * @returns Spacetime coordinates
 */
export function createSpacetimeCoordinates(
  space: [number, number, number],
  time: number
): SpacetimeCoordinates {
  return { space, time };
}

/**
 * Creates a tensor
 * 
 * @param components Components of the tensor
 * @param rank Rank of the tensor
 * @param dimension Dimension of the tensor
 * @returns A tensor
 */
export function createTensor(
  components: any,
  rank: number,
  dimension: number
): Tensor {
  // Implementation details...
}
```

## Usage

These base types provide the fundamental building blocks for representing spacetime:

1. **SpacetimeCoordinates**: Represents a point in spacetime with spatial and temporal coordinates.

2. **MetricType**: Defines different types of metric tensors that can be used to measure distances in spacetime.

3. **PathType**: Defines different types of paths through spacetime, based on their causal properties.

4. **Tensor**: Represents a mathematical tensor of arbitrary rank, used for various spacetime calculations.

## File Location

This type definition should be placed in:
```
spicetime-architecture/packages/utils/origin.rfr/time.rfr/base.type.ts
```

## Related Files

- `manifold/manifold.type.ts`: Uses these base types to define the spacetime manifold
- `metrics/metric.type.ts`: Uses the tensor type to define metric tensors
- `nodes/node.type.ts`: Uses spacetime coordinates to define nodes
- `paths/path.type.ts`: Uses path types to define paths
