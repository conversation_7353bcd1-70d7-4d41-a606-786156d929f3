# Spacetime Observer Type Specification

## Overview

This specification defines the type structure for Spacetime Observers in the time.rfr package. A Spacetime Observer represents an entity that observes and measures spacetime from a particular reference frame, modeling how different perspectives can lead to different measurements of the same spacetime events.

## Type Definition

```typescript
/**
 * Represents a reference frame in spacetime
 */
export interface ReferenceFrame {
  /**
   * The basis vectors of the frame
   */
  readonly basis: Tensor;
  
  /**
   * The origin of the frame
   */
  readonly origin: SpacetimeCoordinates;
  
  /**
   * The velocity of the frame relative to some standard
   */
  readonly velocity: number[];
  
  /**
   * Transforms coordinates from this frame to another
   * 
   * @param coordinates Coordinates in this frame
   * @param targetFrame Target frame
   * @returns Coordinates in the target frame
   */
  transformTo(coordinates: SpacetimeCoordinates, targetFrame: ReferenceFrame): SpacetimeCoordinates;
  
  /**
   * Transforms coordinates from another frame to this one
   * 
   * @param coordinates Coordinates in the other frame
   * @param sourceFrame Source frame
   * @returns Coordinates in this frame
   */
  transformFrom(coordinates: SpacetimeCoordinates, sourceFrame: ReferenceFrame): SpacetimeCoordinates;
  
  /**
   * Gets the Lorentz transformation matrix for this frame
   * 
   * @returns The Lorentz transformation matrix
   */
  getLorentzTransformation(): Tensor;
  
  /**
   * Checks if this frame is inertial
   * 
   * @returns True if the frame is inertial
   */
  isInertial(): boolean;
  
  /**
   * Gets the acceleration of this frame
   * 
   * @returns The acceleration vector
   */
  getAcceleration(): number[];
  
  /**
   * Gets the rotation of this frame
   * 
   * @returns The rotation tensor
   */
  getRotation(): Tensor;
}

/**
 * Represents an observer in spacetime
 * 
 * @typeParam D - Type of data associated with nodes
 */
export interface SpacetimeObserver<D = any> {
  /**
   * Unique identifier for the observer
   */
  readonly id: symbol;
  
  /**
   * The node representing the observer's position
   */
  readonly position: SpacetimeNode<D>;
  
  /**
   * The observer's reference frame
   */
  readonly referenceFrame: ReferenceFrame;
  
  /**
   * Gets the observer's proper time
   * 
   * @returns The proper time
   */
  getProperTime(): number;
  
  /**
   * Transforms coordinates from this observer's frame to another
   * 
   * @param coordinates Coordinates in this frame
   * @param targetObserverId Target observer ID
   * @param manifold The spacetime manifold
   * @returns Coordinates in the target frame
   */
  transformCoordinates(
    coordinates: SpacetimeCoordinates,
    targetObserverId: symbol,
    manifold: SpacetimeManifold<D>
  ): SpacetimeCoordinates;
  
  /**
   * Measures the time interval between two events
   * 
   * @param event1Id First event ID
   * @param event2Id Second event ID
   * @param manifold The spacetime manifold
   * @returns The time interval
   */
  measureTimeInterval(
    event1Id: symbol,
    event2Id: symbol,
    manifold: SpacetimeManifold<D>
  ): number;
  
  /**
   * Measures the spatial distance between two events
   * 
   * @param event1Id First event ID
   * @param event2Id Second event ID
   * @param manifold The spacetime manifold
   * @returns The spatial distance
   */
  measureSpatialDistance(
    event1Id: symbol,
    event2Id: symbol,
    manifold: SpacetimeManifold<D>
  ): number;
  
  /**
   * Determines if two events are simultaneous in this frame
   * 
   * @param event1Id First event ID
   * @param event2Id Second event ID
   * @param manifold The spacetime manifold
   * @returns True if the events are simultaneous
   */
  areSimultaneous(
    event1Id: symbol,
    event2Id: symbol,
    manifold: SpacetimeManifold<D>
  ): boolean;
  
  /**
   * Moves the observer to a new position
   * 
   * @param newPositionId New position node ID
   * @param manifold The spacetime manifold
   * @returns The updated observer
   */
  moveTo(
    newPositionId: symbol,
    manifold: SpacetimeManifold<D>
  ): SpacetimeObserver<D>;
  
  /**
   * Gets the observed worldline of an entity
   * 
   * @param worldlineId Worldline ID
   * @param manifold The spacetime manifold
   * @returns The observed worldline
   */
  observeWorldline(
    worldlineId: symbol,
    manifold: SpacetimeManifold<D>
  ): SpacetimeCoordinates[];
  
  /**
   * Gets the observed length contraction factor
   * 
   * @param direction Direction vector
   * @returns The length contraction factor
   */
  getLengthContractionFactor(direction: number[]): number;
  
  /**
   * Gets the observed time dilation factor
   * 
   * @returns The time dilation factor
   */
  getTimeDilationFactor(): number;
  
  /**
   * Observes the curvature of spacetime
   * 
   * @param coordinates Coordinates
   * @param manifold The spacetime manifold
   * @returns The observed curvature tensor
   */
  observeCurvature(
    coordinates: SpacetimeCoordinates,
    manifold: SpacetimeManifold<D>
  ): Tensor;
}

/**
 * Represents a team observer in development
 * 
 * @typeParam D - Type of data associated with nodes
 */
export interface TeamObserver<D = any> extends SpacetimeObserver<D> {
  /**
   * The team members
   */
  readonly teamMembers: Set<symbol>;
  
  /**
   * Gets the team's shared reference frame
   * 
   * @returns The shared reference frame
   */
  getSharedReferenceFrame(): ReferenceFrame;
  
  /**
   * Gets the team's consensus on an event
   * 
   * @param eventId Event ID
   * @param manifold The spacetime manifold
   * @returns The consensus coordinates
   */
  getConsensus(
    eventId: symbol,
    manifold: SpacetimeManifold<D>
  ): SpacetimeCoordinates;
  
  /**
   * Measures the team's synchronization
   * 
   * @param manifold The spacetime manifold
   * @returns A measure of synchronization (0-1)
   */
  measureSynchronization(manifold: SpacetimeManifold<D>): number;
  
  /**
   * Adds a team member
   * 
   * @param observerId Observer ID
   * @returns The updated team observer
   */
  addTeamMember(observerId: symbol): TeamObserver<D>;
  
  /**
   * Removes a team member
   * 
   * @param observerId Observer ID
   * @returns The updated team observer
   */
  removeTeamMember(observerId: symbol): TeamObserver<D>;
  
  /**
   * Gets the relative velocities between team members
   * 
   * @param manifold The spacetime manifold
   * @returns A map of observer pairs to relative velocities
   */
  getRelativeVelocities(
    manifold: SpacetimeManifold<D>
  ): Map<string, number[]>;
}
```

## Dependencies

- `SpacetimeNode`: Represents a node in spacetime
- `SpacetimeCoordinates`: Represents coordinates in spacetime
- `SpacetimeManifold`: Represents the overall spacetime structure
- `Tensor`: Represents a mathematical tensor

## Usage

The `ReferenceFrame` interface represents a coordinate system in spacetime. It provides methods for:

1. **Coordinate Transformation**: Converting coordinates between reference frames
2. **Lorentz Transformation**: Getting the Lorentz transformation matrix
3. **Frame Properties**: Determining if a frame is inertial and getting its acceleration and rotation

The `SpacetimeObserver` interface represents an entity that observes and measures spacetime from a particular reference frame. It provides methods for:

1. **Time Measurement**: Getting proper time and measuring time intervals
2. **Distance Measurement**: Measuring spatial distances
3. **Simultaneity Analysis**: Determining if events are simultaneous
4. **Observer Movement**: Moving the observer to a new position
5. **Relativistic Effects**: Calculating length contraction and time dilation
6. **Curvature Observation**: Observing the curvature of spacetime

The `TeamObserver` interface extends `SpacetimeObserver` to represent a team of observers. It provides additional methods for:

1. **Team Management**: Adding and removing team members
2. **Consensus Building**: Getting the team's consensus on events
3. **Synchronization Measurement**: Measuring how synchronized the team is
4. **Relative Motion**: Analyzing the relative velocities between team members

## Implementation Requirements

Implementations of the `ReferenceFrame` interface must:

1. Store basis vectors, origin, and velocity
2. Transform coordinates between reference frames
3. Calculate Lorentz transformations
4. Determine frame properties like inertial status, acceleration, and rotation

Implementations of the `SpacetimeObserver` interface must:

1. Store a position node and reference frame
2. Calculate proper time
3. Transform coordinates between observers
4. Measure time intervals and spatial distances
5. Determine simultaneity of events
6. Support observer movement
7. Calculate relativistic effects
8. Observe spacetime curvature

Implementations of the `TeamObserver` interface must:

1. Implement all methods from `SpacetimeObserver`
2. Store a set of team members
3. Calculate a shared reference frame
4. Build consensus on events
5. Measure team synchronization
6. Support adding and removing team members
7. Analyze relative velocities between team members

The implementation should reflect our understanding of how different observers can have different perspectives on the same spacetime events, how teams can build consensus despite these differences, and how relative motion affects observations.

## File Location

This type definition should be placed in:
```
spicetime-architecture/packages/utils/origin.rfr/time.rfr/observer/observer.type.ts
```

## Related Files

- `observer/observer.ts`: Implementation of spacetime observers
- `observer/reference-frame.ts`: Implementation of reference frames
- `observer/team-observer.ts`: Implementation of team observers
- `nodes/node.type.ts`: Definition of spacetime nodes
- `manifold/manifold.type.ts`: Definition of the spacetime manifold
