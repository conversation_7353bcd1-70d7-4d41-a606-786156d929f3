# Process Space Origin Concept

## Overview

The Process Space Origin concept defines how processes exist in a space with coordinates and transformations. It establishes the mathematical foundation for process creation, interaction, and evolution in the SpiceTime architecture.

## Core Concepts

### Origin Reference Frame

The Origin Reference Frame (origin.rfr) sets the axes/stages for the process space. It defines:

1. **Coordinate System**: The coordinate system for processes in space.
2. **Axes**: The fundamental dimensions along which processes can move or transform.
3. **Morphisms**: The transformations between points in each axis.

As a functor, the origin reference frame maps between different categories in the process space.

### Process Space

The Process Space is the mathematical space in which processes exist and evolve. It has the following properties:

1. **Coordinates**: Each process has a position in the space defined by coordinates.
2. **Trajectories**: Processes follow trajectories in the space as they execute.
3. **Transformations**: Processes can transform through translations and rotations in the space.

### Child Processes

Child processes are formed through transformations in the process space:

1. **Translation**: Moving along an axis to a new coordinate point.
2. **Rotation of Origin**: Changing the orientation of the axes, where new axes are compositions of parent axis morphisms.

The rotation of origin is a linear, matrix-like operation - a real rotational matrix. The angles of rotation may be arbitrary, but the constraint of having a finite number of degrees in a full circle limits the number of possibilities. This constraint is unitarity.

### Unitarity

Unitarity is a key constraint in the process space:

1. **Unitary Vectors**: The origin is a set of unitary vectors - one-step transforms that generate each axis.
2. **Conservation**: Unitary transformations preserve certain properties of processes.
3. **Finite Possibilities**: Unitarity constrains the number of possible transformations.

This concept maps to Lee algebras in mathematics, where infinitesimal generators create transformations.

## Mathematical Formulation

### Process Space Coordinates

A process P exists at a point in the process space with coordinates:

```
P = (x, y, z)
```

where x, y, and z represent positions along the three fundamental axes.

### Translation

A translation T moves a process from one point to another:

```
T(P) = P + (Δx, Δy, Δz)
```

### Rotation

A rotation R changes the orientation of the axes:

```
R(P) = M × P
```

where M is a rotation matrix:

```
M = [
  [m11, m12, m13],
  [m21, m22, m23],
  [m31, m32, m33]
]
```

### Child Process Formation

A child process C is formed from a parent process P through translation and rotation:

```
C = R(T(P))
```

The child process has its own coordinate system, which is a transformation of the parent's coordinate system.

## Implementation

### Process Space

```typescript
/**
 * Process space interface
 */
interface ProcessSpace {
  /**
   * Get the coordinates of a process
   */
  getCoordinates(processId: string): Coordinates;
  
  /**
   * Set the coordinates of a process
   */
  setCoordinates(processId: string, coordinates: Coordinates): void;
  
  /**
   * Translate a process
   */
  translate(processId: string, translation: Translation): void;
  
  /**
   * Rotate a process
   */
  rotate(processId: string, rotation: Rotation): void;
  
  /**
   * Create a child process
   */
  createChild(parentId: string, translation: Translation, rotation: Rotation): string;
  
  /**
   * Get the parent of a process
   */
  getParent(processId: string): string | null;
  
  /**
   * Get the children of a process
   */
  getChildren(processId: string): string[];
  
  /**
   * Get the trajectory of a process
   */
  getTrajectory(processId: string): Coordinates[];
}

/**
 * Coordinates in the process space
 */
interface Coordinates {
  x: number;
  y: number;
  z: number;
}

/**
 * Translation in the process space
 */
interface Translation {
  dx: number;
  dy: number;
  dz: number;
}

/**
 * Rotation in the process space
 */
interface Rotation {
  matrix: [
    [number, number, number],
    [number, number, number],
    [number, number, number]
  ];
}
```

### Unitary Vectors

```typescript
/**
 * Unitary vector interface
 */
interface UnitaryVector {
  /**
   * Vector components
   */
  components: [number, number, number];
  
  /**
   * Apply the vector to a point
   */
  apply(point: Coordinates): Coordinates;
  
  /**
   * Compose with another unitary vector
   */
  compose(other: UnitaryVector): UnitaryVector;
}

/**
 * Unitary vector implementation
 */
class UnitaryVectorImpl implements UnitaryVector {
  components: [number, number, number];
  
  constructor(components: [number, number, number]) {
    // Normalize the components to ensure unitarity
    const magnitude = Math.sqrt(
      components[0] * components[0] +
      components[1] * components[1] +
      components[2] * components[2]
    );
    
    this.components = [
      components[0] / magnitude,
      components[1] / magnitude,
      components[2] / magnitude
    ];
  }
  
  apply(point: Coordinates): Coordinates {
    return {
      x: point.x + this.components[0],
      y: point.y + this.components[1],
      z: point.z + this.components[2]
    };
  }
  
  compose(other: UnitaryVector): UnitaryVector {
    // Compose two unitary vectors
    const [a1, a2, a3] = this.components;
    const [b1, b2, b3] = other.components;
    
    return new UnitaryVectorImpl([
      a1 * b1 - a2 * b2 - a3 * b3,
      a1 * b2 + a2 * b1 + a3 * b3,
      a1 * b3 + a3 * b1 - a2 * b2
    ]);
  }
}
```

### Process Space Implementation

```typescript
/**
 * Process space implementation
 */
class ProcessSpaceImpl implements ProcessSpace {
  private processes: Map<string, {
    coordinates: Coordinates;
    parent: string | null;
    children: string[];
    trajectory: Coordinates[];
  }>;
  
  constructor() {
    this.processes = new Map();
  }
  
  getCoordinates(processId: string): Coordinates {
    const process = this.getProcess(processId);
    return { ...process.coordinates };
  }
  
  setCoordinates(processId: string, coordinates: Coordinates): void {
    const process = this.getProcess(processId);
    process.coordinates = { ...coordinates };
    process.trajectory.push({ ...coordinates });
  }
  
  translate(processId: string, translation: Translation): void {
    const process = this.getProcess(processId);
    const newCoordinates = {
      x: process.coordinates.x + translation.dx,
      y: process.coordinates.y + translation.dy,
      z: process.coordinates.z + translation.dz
    };
    
    this.setCoordinates(processId, newCoordinates);
  }
  
  rotate(processId: string, rotation: Rotation): void {
    const process = this.getProcess(processId);
    const { x, y, z } = process.coordinates;
    const [[m11, m12, m13], [m21, m22, m23], [m31, m32, m33]] = rotation.matrix;
    
    const newCoordinates = {
      x: m11 * x + m12 * y + m13 * z,
      y: m21 * x + m22 * y + m23 * z,
      z: m31 * x + m32 * y + m33 * z
    };
    
    this.setCoordinates(processId, newCoordinates);
  }
  
  createChild(parentId: string, translation: Translation, rotation: Rotation): string {
    const parent = this.getProcess(parentId);
    const childId = generateId();
    
    // Apply translation and rotation to parent coordinates
    const parentCoords = { ...parent.coordinates };
    const translatedCoords = {
      x: parentCoords.x + translation.dx,
      y: parentCoords.y + translation.dy,
      z: parentCoords.z + translation.dz
    };
    
    const [[m11, m12, m13], [m21, m22, m23], [m31, m32, m33]] = rotation.matrix;
    const rotatedCoords = {
      x: m11 * translatedCoords.x + m12 * translatedCoords.y + m13 * translatedCoords.z,
      y: m21 * translatedCoords.x + m22 * translatedCoords.y + m23 * translatedCoords.z,
      z: m31 * translatedCoords.x + m32 * translatedCoords.y + m33 * translatedCoords.z
    };
    
    // Create the child process
    this.processes.set(childId, {
      coordinates: rotatedCoords,
      parent: parentId,
      children: [],
      trajectory: [rotatedCoords]
    });
    
    // Update parent's children
    parent.children.push(childId);
    
    return childId;
  }
  
  getParent(processId: string): string | null {
    const process = this.getProcess(processId);
    return process.parent;
  }
  
  getChildren(processId: string): string[] {
    const process = this.getProcess(processId);
    return [...process.children];
  }
  
  getTrajectory(processId: string): Coordinates[] {
    const process = this.getProcess(processId);
    return [...process.trajectory];
  }
  
  private getProcess(processId: string): {
    coordinates: Coordinates;
    parent: string | null;
    children: string[];
    trajectory: Coordinates[];
  } {
    const process = this.processes.get(processId);
    
    if (!process) {
      throw new Error(`Process not found: ${processId}`);
    }
    
    return process;
  }
}
```

## Relationship to Tripodic Process

While the origin process is a basic extension of the Node.js process, it lays the foundation for the tripodic process model that comes later in spice.space. The process space origin concept establishes the mathematical framework that will be used to define the tripodic process.

The key differences are:

1. **Origin Process**: Focuses on extending the Node.js process model with basic capabilities.
2. **Tripodic Process**: Builds on the origin process to create a more complex process model with three interconnected dimensions.

## Relationship to Pragmas

In the context of scopes, each term in a scope is defined by the public API of the node it represents. If the API is an object (like a schema), it's a namespace. The type of the API defines the term, and that type is the pragma of a node.

The pragma of a node sets its type, which determines how it interacts with other nodes in the process space.

## Usage Examples

### Creating a Process Space

```typescript
import { ProcessSpace, ProcessSpaceImpl } from '@spicetime/core/origin.rfr/process.rfr';

// Create a process space
const space = new ProcessSpaceImpl();

// Create a root process
const rootId = 'root';
space.processes.set(rootId, {
  coordinates: { x: 0, y: 0, z: 0 },
  parent: null,
  children: [],
  trajectory: [{ x: 0, y: 0, z: 0 }]
});

// Create a child process through translation
const translation = { dx: 1, dy: 0, dz: 0 };
const rotation = {
  matrix: [
    [1, 0, 0],
    [0, 1, 0],
    [0, 0, 1]
  ]
};

const childId = space.createChild(rootId, translation, rotation);

// Get the coordinates of the child process
const childCoords = space.getCoordinates(childId);
console.log('Child coordinates:', childCoords); // { x: 1, y: 0, z: 0 }

// Translate the child process
space.translate(childId, { dx: 0, dy: 1, dz: 0 });
const newChildCoords = space.getCoordinates(childId);
console.log('New child coordinates:', newChildCoords); // { x: 1, y: 1, z: 0 }

// Rotate the child process
const rotationMatrix = {
  matrix: [
    [0, -1, 0],
    [1, 0, 0],
    [0, 0, 1]
  ]
};

space.rotate(childId, rotationMatrix);
const rotatedChildCoords = space.getCoordinates(childId);
console.log('Rotated child coordinates:', rotatedChildCoords); // { x: -1, y: 1, z: 0 }

// Get the trajectory of the child process
const trajectory = space.getTrajectory(childId);
console.log('Child trajectory:', trajectory);
// [
//   { x: 1, y: 0, z: 0 },
//   { x: 1, y: 1, z: 0 },
//   { x: -1, y: 1, z: 0 }
// ]
```

### Creating Unitary Vectors

```typescript
import { UnitaryVector, UnitaryVectorImpl } from '@spicetime/core/origin.rfr/process.rfr';

// Create unitary vectors for each axis
const xAxis = new UnitaryVectorImpl([1, 0, 0]);
const yAxis = new UnitaryVectorImpl([0, 1, 0]);
const zAxis = new UnitaryVectorImpl([0, 0, 1]);

// Apply a unitary vector to a point
const point = { x: 0, y: 0, z: 0 };
const newPoint = xAxis.apply(point);
console.log('New point:', newPoint); // { x: 1, y: 0, z: 0 }

// Compose unitary vectors
const composed = xAxis.compose(yAxis);
const composedPoint = composed.apply(point);
console.log('Composed point:', composedPoint); // { x: 0, y: 1, z: 0 }
```

## Next Steps

1. **Implement Process Space**: Create the process space implementation.
2. **Implement Unitary Vectors**: Create the unitary vector implementation.
3. **Integrate with Process.rfr**: Integrate the process space concept with the process.rfr package.
4. **Create Tests**: Create comprehensive tests for the process space concept.
5. **Create Documentation**: Create detailed documentation for the process space concept.
6. **Prepare for Tripodic Process**: Lay the groundwork for the tripodic process model in spice.space.
