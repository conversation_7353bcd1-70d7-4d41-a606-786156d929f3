# Spacetime Node Type Specification

## Overview

This specification defines the type structure for Spacetime Nodes in the time.rfr package. A Spacetime Node represents a point in spacetime with associated data and properties.

## Type Definition

```typescript
/**
 * Represents a node in spacetime
 * 
 * @typeParam D - Type of data associated with the node
 */
export interface SpacetimeNode<D = any> {
  /**
   * Unique identifier for the node
   */
  readonly id: symbol;
  
  /**
   * Spacetime coordinates
   */
  readonly coordinates: SpacetimeCoordinates;
  
  /**
   * Data associated with the node
   */
  readonly data: D;
  
  /**
   * Conceptual mass of the node
   */
  readonly mass: number;
  
  /**
   * Incoming paths to this node
   */
  readonly incomingPaths: Set<symbol>;
  
  /**
   * Outgoing paths from this node
   */
  readonly outgoingPaths: Set<symbol>;
  
  /**
   * Gets the proper time experienced by this node
   * 
   * @returns The proper time
   */
  getProperTime(): number;
  
  /**
   * Gets the gravitational potential at this node
   * 
   * @returns The gravitational potential
   */
  getGravitationalPotential(): number;
  
  /**
   * Gets the time dilation factor at this node
   * 
   * @returns The time dilation factor
   */
  getTimeDilationFactor(): number;
  
  /**
   * Updates the node's coordinates
   * 
   * @param newCoordinates New coordinates
   * @returns The updated node
   */
  updateCoordinates(newCoordinates: SpacetimeCoordinates): SpacetimeNode<D>;
  
  /**
   * Updates the node's mass
   * 
   * @param newMass New mass
   * @returns The updated node
   */
  updateMass(newMass: number): SpacetimeNode<D>;
  
  /**
   * Updates the node's data
   * 
   * @param newData New data
   * @returns The updated node
   */
  updateData(newData: D): SpacetimeNode<D>;
  
  /**
   * Gets all paths connected to this node
   * 
   * @returns All connected paths
   */
  getAllPaths(): Set<symbol>;
  
  /**
   * Gets all nodes directly connected to this node
   * 
   * @param manifold The spacetime manifold
   * @returns Connected nodes
   */
  getConnectedNodes(manifold: SpacetimeManifold<D>): SpacetimeNode<D>[];
  
  /**
   * Checks if this node is causally connected to another node
   * 
   * @param otherNode Other node
   * @param manifold The spacetime manifold
   * @returns True if the nodes are causally connected
   */
  isCausallyConnectedTo(otherNode: SpacetimeNode<D>, manifold: SpacetimeManifold<D>): boolean;
  
  /**
   * Gets the spacetime interval to another node
   * 
   * @param otherNode Other node
   * @param manifold The spacetime manifold
   * @returns The spacetime interval
   */
  getIntervalTo(otherNode: SpacetimeNode<D>, manifold: SpacetimeManifold<D>): number;
}

/**
 * Represents an event in spacetime
 * 
 * @typeParam D - Type of data associated with the event
 */
export interface SpacetimeEvent<D = any> {
  /**
   * Unique identifier for the event
   */
  readonly id: symbol;
  
  /**
   * Spacetime coordinates of the event
   */
  readonly coordinates: SpacetimeCoordinates;
  
  /**
   * Data associated with the event
   */
  readonly data: D;
  
  /**
   * Nodes affected by the event
   */
  readonly affectedNodes: Set<symbol>;
  
  /**
   * Gets the proper time of the event
   * 
   * @param manifold The spacetime manifold
   * @returns The proper time
   */
  getProperTime(manifold: SpacetimeManifold<D>): number;
  
  /**
   * Gets the causal future of the event
   * 
   * @param manifold The spacetime manifold
   * @returns Nodes in the causal future
   */
  getCausalFuture(manifold: SpacetimeManifold<D>): Set<symbol>;
  
  /**
   * Gets the causal past of the event
   * 
   * @param manifold The spacetime manifold
   * @returns Nodes in the causal past
   */
  getCausalPast(manifold: SpacetimeManifold<D>): Set<symbol>;
  
  /**
   * Checks if this event can causally affect another event
   * 
   * @param otherEvent Other event
   * @param manifold The spacetime manifold
   * @returns True if this event can causally affect the other
   */
  canAffect(otherEvent: SpacetimeEvent<D>, manifold: SpacetimeManifold<D>): boolean;
  
  /**
   * Applies the event to affected nodes
   * 
   * @param manifold The spacetime manifold
   * @param effectFunction Function that applies the effect to a node
   * @returns Affected nodes after applying the effect
   */
  applyToNodes(
    manifold: SpacetimeManifold<D>,
    effectFunction: (node: SpacetimeNode<D>) => SpacetimeNode<D>
  ): SpacetimeNode<D>[];
}
```

## Dependencies

- `SpacetimeCoordinates`: Represents coordinates in spacetime
- `SpacetimeManifold`: Represents the overall spacetime structure

## Usage

The `SpacetimeNode` interface represents a point in spacetime with associated data and properties. It provides methods for:

1. **Coordinate Management**: Updating and accessing spacetime coordinates
2. **Mass Properties**: Managing the conceptual mass of the node
3. **Time Effects**: Calculating proper time and time dilation
4. **Connectivity**: Analyzing connections to other nodes
5. **Causal Analysis**: Determining causal relationships

The `SpacetimeEvent` interface represents an event that occurs at a specific point in spacetime and affects one or more nodes. It provides methods for:

1. **Causal Analysis**: Determining the causal future and past
2. **Effect Application**: Applying effects to affected nodes
3. **Event Relationships**: Analyzing relationships between events

## Implementation Requirements

Implementations of the `SpacetimeNode` interface must:

1. Store spacetime coordinates, data, and mass
2. Track incoming and outgoing paths
3. Calculate time-related properties
4. Support coordinate and mass updates
5. Enable causal analysis

Implementations of the `SpacetimeEvent` interface must:

1. Store spacetime coordinates, data, and affected nodes
2. Calculate causal relationships
3. Apply effects to affected nodes

The implementation should reflect our understanding of how nodes in spacetime represent conceptual entities in development, how their mass curves spacetime, and how events propagate through the causal structure.

## File Location

This type definition should be placed in:
```
spicetime-architecture/packages/utils/origin.rfr/time.rfr/nodes/node.type.ts
```

## Related Files

- `nodes/node.ts`: Implementation of spacetime nodes
- `nodes/event.ts`: Implementation of spacetime events
- `manifold/manifold.type.ts`: Definition of the spacetime manifold
- `paths/path.type.ts`: Definition of spacetime paths
