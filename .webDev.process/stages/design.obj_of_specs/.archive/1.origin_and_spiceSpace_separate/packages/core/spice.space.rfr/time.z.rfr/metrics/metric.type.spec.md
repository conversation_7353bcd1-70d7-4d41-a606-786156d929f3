# Metric Tensor Type Specification

## Overview

This specification defines the type structure for Metric Tensors in the time.rfr package. A Metric Tensor defines how to measure distances in spacetime and is fundamental to understanding the geometry of spacetime.

## Type Definition

```typescript
/**
 * Represents a metric tensor for spacetime
 */
export interface MetricTensor {
  /**
   * The components of the metric tensor
   * 
   * A 4x4 matrix representing the metric at each point
   */
  readonly components: Tensor;
  
  /**
   * The type of metric
   */
  readonly type: MetricType;
  
  /**
   * Calculates the spacetime interval between two points
   * 
   * @param p1 First point
   * @param p2 Second point
   * @returns The spacetime interval
   */
  calculateInterval(p1: SpacetimeCoordinates, p2: SpacetimeCoordinates): number;
  
  /**
   * Raises an index of a tensor
   * 
   * @param tensor Tensor with lower indices
   * @returns Tensor with one index raised
   */
  raiseIndex(tensor: Tensor): Tensor;
  
  /**
   * Lowers an index of a tensor
   * 
   * @param tensor Tensor with upper indices
   * @returns Tensor with one index lowered
   */
  lowerIndex(tensor: Tensor): Tensor;
  
  /**
   * Calculates the Christoffel symbols for the metric
   * 
   * @returns The Christoffel symbols
   */
  calculateChristoffelSymbols(): Tensor;
  
  /**
   * Calculates the Riemann curvature tensor
   * 
   * @returns The Riemann tensor
   */
  calculateRiemannTensor(): Tensor;
  
  /**
   * Calculates the Ricci tensor
   * 
   * @returns The Ricci tensor
   */
  calculateRicciTensor(): Tensor;
  
  /**
   * Calculates the Ricci scalar
   * 
   * @returns The Ricci scalar
   */
  calculateRicciScalar(): number;
  
  /**
   * Updates the metric based on a mass distribution
   * 
   * @param massDistribution Function that gives the mass at each point
   */
  updateFromMassDistribution(massDistribution: (coords: SpacetimeCoordinates) => number): void;
  
  /**
   * Gets the metric at a specific point
   * 
   * @param coordinates Coordinates
   * @returns The metric tensor at the given point
   */
  getMetricAtPoint(coordinates: SpacetimeCoordinates): Tensor;
  
  /**
   * Calculates the proper time along a path
   * 
   * @param path Array of coordinates representing the path
   * @returns The proper time
   */
  calculateProperTime(path: SpacetimeCoordinates[]): number;
  
  /**
   * Calculates the proper distance along a path
   * 
   * @param path Array of coordinates representing the path
   * @returns The proper distance
   */
  calculateProperDistance(path: SpacetimeCoordinates[]): number;
  
  /**
   * Checks if a path is timelike
   * 
   * @param path Array of coordinates representing the path
   * @returns True if the path is timelike
   */
  isTimelike(path: SpacetimeCoordinates[]): boolean;
  
  /**
   * Checks if a path is lightlike
   * 
   * @param path Array of coordinates representing the path
   * @returns True if the path is lightlike
   */
  isLightlike(path: SpacetimeCoordinates[]): boolean;
  
  /**
   * Checks if a path is spacelike
   * 
   * @param path Array of coordinates representing the path
   * @returns True if the path is spacelike
   */
  isSpacelike(path: SpacetimeCoordinates[]): boolean;
}

/**
 * Factory functions for creating different types of metrics
 */
export interface MetricFactory {
  /**
   * Creates a Minkowski metric (flat spacetime)
   * 
   * @returns A Minkowski metric tensor
   */
  createMinkowskiMetric(): MetricTensor;
  
  /**
   * Creates a Schwarzschild metric (spherically symmetric gravitational field)
   * 
   * @param mass Central mass
   * @returns A Schwarzschild metric tensor
   */
  createSchwarzschildMetric(mass: number): MetricTensor;
  
  /**
   * Creates a Friedmann-Lemaître-Robertson-Walker metric (expanding universe)
   * 
   * @param scaleFactor Scale factor function
   * @param curvature Spatial curvature
   * @returns An FLRW metric tensor
   */
  createFLRWMetric(
    scaleFactor: (time: number) => number,
    curvature: number
  ): MetricTensor;
  
  /**
   * Creates a custom metric from components
   * 
   * @param components Components of the metric tensor
   * @returns A custom metric tensor
   */
  createCustomMetric(components: number[][]): MetricTensor;
  
  /**
   * Creates a metric that represents conceptual mass in development
   * 
   * @param massDistribution Function that gives the conceptual mass at each point
   * @returns A development metric tensor
   */
  createDevelopmentMetric(
    massDistribution: (coords: SpacetimeCoordinates) => number
  ): MetricTensor;
}
```

## Dependencies

- `Tensor`: Represents a mathematical tensor
- `MetricType`: Defines different types of metrics
- `SpacetimeCoordinates`: Represents coordinates in spacetime

## Usage

The `MetricTensor` interface is used to define how distances are measured in spacetime. It provides methods for:

1. **Calculating Intervals**: Determining the spacetime interval between points
2. **Index Manipulation**: Raising and lowering tensor indices
3. **Curvature Analysis**: Calculating various curvature tensors
4. **Path Analysis**: Determining the nature of paths (timelike, lightlike, spacelike)
5. **Proper Time/Distance**: Calculating proper time and distance along paths

The `MetricFactory` interface provides factory methods for creating different types of metrics, including:

1. **Minkowski Metric**: Represents flat spacetime
2. **Schwarzschild Metric**: Represents a spherically symmetric gravitational field
3. **FLRW Metric**: Represents an expanding universe
4. **Custom Metric**: Allows for custom metric definitions
5. **Development Metric**: Represents conceptual mass in development

## Implementation Requirements

Implementations of the `MetricTensor` interface must:

1. Store the components of the metric tensor
2. Provide methods for calculating spacetime intervals
3. Implement tensor index manipulation
4. Calculate various curvature tensors
5. Analyze paths through spacetime
6. Update based on mass distributions

The implementation should reflect our understanding of how conceptual mass curves spacetime, creating gravitational effects that shape the structure of development.

## File Location

This type definition should be placed in:
```
spicetime-architecture/packages/utils/origin.rfr/time.rfr/metrics/metric.type.ts
```

## Related Files

- `metrics/metric.ts`: Implementation of metric tensors
- `metrics/minkowski.ts`: Implementation of the Minkowski metric
- `metrics/schwarzschild.ts`: Implementation of the Schwarzschild metric
- `metrics/flrw.ts`: Implementation of the FLRW metric
- `metrics/development.ts`: Implementation of the development metric
