# Spacetime Path Type Specification

## Overview

This specification defines the type structure for Spacetime Paths in the time.rfr package. A Spacetime Path represents a trajectory through spacetime, connecting nodes and defining how entities move through spacetime.

## Type Definition

```typescript
/**
 * Represents a path through spacetime
 * 
 * @typeParam D - Type of data associated with nodes
 */
export interface SpacetimePath<D = any> {
  /**
   * Unique identifier for the path
   */
  readonly id: symbol;
  
  /**
   * Source node
   */
  readonly source: SpacetimeNode<D>;
  
  /**
   * Target node
   */
  readonly target: SpacetimeNode<D>;
  
  /**
   * Path type
   */
  readonly type: PathType;
  
  /**
   * Intermediate points along the path
   */
  readonly points: SpacetimeCoordinates[];
  
  /**
   * Gets the proper length of the path
   * 
   * @param manifold The spacetime manifold
   * @returns The proper length
   */
  getProperLength(manifold: SpacetimeManifold<D>): number;
  
  /**
   * Gets the coordinate length of the path
   * 
   * @returns The coordinate length
   */
  getCoordinateLength(): number;
  
  /**
   * Checks if the path is traversable
   * 
   * @param manifold The spacetime manifold
   * @returns True if the path is traversable
   */
  isTraversable(manifold: SpacetimeManifold<D>): boolean;
  
  /**
   * Gets the action along the path
   * 
   * @param manifold The spacetime manifold
   * @returns The action
   */
  getAction(manifold: SpacetimeManifold<D>): number;
  
  /**
   * Parameterizes the path
   * 
   * @param parameter Parameter value between 0 and 1
   * @returns The coordinates at the given parameter value
   */
  parameterize(parameter: number): SpacetimeCoordinates;
  
  /**
   * Checks if the path is timelike
   * 
   * @param manifold The spacetime manifold
   * @returns True if the path is timelike
   */
  isTimelike(manifold: SpacetimeManifold<D>): boolean;
  
  /**
   * Checks if the path is lightlike
   * 
   * @param manifold The spacetime manifold
   * @returns True if the path is lightlike
   */
  isLightlike(manifold: SpacetimeManifold<D>): boolean;
  
  /**
   * Checks if the path is spacelike
   * 
   * @param manifold The spacetime manifold
   * @returns True if the path is spacelike
   */
  isSpacelike(manifold: SpacetimeManifold<D>): boolean;
  
  /**
   * Gets the proper time along the path
   * 
   * @param manifold The spacetime manifold
   * @returns The proper time
   */
  getProperTime(manifold: SpacetimeManifold<D>): number;
  
  /**
   * Reverses the path
   * 
   * @returns The reversed path
   */
  reverse(): SpacetimePath<D>;
  
  /**
   * Concatenates this path with another path
   * 
   * @param otherPath Other path
   * @returns The concatenated path
   */
  concat(otherPath: SpacetimePath<D>): SpacetimePath<D>;
  
  /**
   * Gets a subpath
   * 
   * @param startParameter Start parameter (0-1)
   * @param endParameter End parameter (0-1)
   * @returns The subpath
   */
  getSubpath(startParameter: number, endParameter: number): SpacetimePath<D>;
}

/**
 * Represents a geodesic path through spacetime
 * 
 * @typeParam D - Type of data associated with nodes
 */
export interface GeodesicPath<D = any> extends SpacetimePath<D> {
  /**
   * Checks if the path is a true geodesic
   * 
   * @param manifold The spacetime manifold
   * @param tolerance Tolerance for numerical calculations
   * @returns True if the path is a true geodesic
   */
  isGeodesic(manifold: SpacetimeManifold<D>, tolerance?: number): boolean;
  
  /**
   * Gets the curvature along the geodesic
   * 
   * @param manifold The spacetime manifold
   * @returns The curvature at each point
   */
  getCurvature(manifold: SpacetimeManifold<D>): number[];
  
  /**
   * Gets the acceleration required to deviate from the geodesic
   * 
   * @param manifold The spacetime manifold
   * @param deviationPath The deviation path
   * @returns The acceleration at each point
   */
  getDeviationAcceleration(
    manifold: SpacetimeManifold<D>,
    deviationPath: SpacetimePath<D>
  ): number[];
}

/**
 * Represents a worldline of an entity through spacetime
 * 
 * @typeParam D - Type of data associated with nodes
 */
export interface Worldline<D = any> {
  /**
   * Unique identifier for the worldline
   */
  readonly id: symbol;
  
  /**
   * The entity associated with this worldline
   */
  readonly entity: any;
  
  /**
   * The paths that make up the worldline
   */
  readonly paths: SpacetimePath<D>[];
  
  /**
   * Gets the total proper time along the worldline
   * 
   * @param manifold The spacetime manifold
   * @returns The total proper time
   */
  getTotalProperTime(manifold: SpacetimeManifold<D>): number;
  
  /**
   * Gets the total action along the worldline
   * 
   * @param manifold The spacetime manifold
   * @returns The total action
   */
  getTotalAction(manifold: SpacetimeManifold<D>): number;
  
  /**
   * Adds a path to the worldline
   * 
   * @param path Path to add
   * @returns The updated worldline
   */
  addPath(path: SpacetimePath<D>): Worldline<D>;
  
  /**
   * Gets the position at a given proper time
   * 
   * @param properTime Proper time
   * @param manifold The spacetime manifold
   * @returns The position at the given proper time
   */
  getPositionAtProperTime(
    properTime: number,
    manifold: SpacetimeManifold<D>
  ): SpacetimeCoordinates;
  
  /**
   * Checks if the worldline is timelike
   * 
   * @param manifold The spacetime manifold
   * @returns True if the worldline is timelike
   */
  isTimelike(manifold: SpacetimeManifold<D>): boolean;
}
```

## Dependencies

- `SpacetimeNode`: Represents a node in spacetime
- `SpacetimeCoordinates`: Represents coordinates in spacetime
- `SpacetimeManifold`: Represents the overall spacetime structure
- `PathType`: Defines different types of paths

## Usage

The `SpacetimePath` interface represents a trajectory through spacetime, connecting nodes and defining how entities move through spacetime. It provides methods for:

1. **Path Analysis**: Calculating lengths, actions, and proper times
2. **Causal Classification**: Determining if a path is timelike, lightlike, or spacelike
3. **Parameterization**: Getting coordinates at any point along the path
4. **Path Operations**: Reversing, concatenating, and extracting subpaths

The `GeodesicPath` interface extends `SpacetimePath` to represent the shortest path between two points in curved spacetime. It provides additional methods for:

1. **Geodesic Verification**: Checking if a path is a true geodesic
2. **Curvature Analysis**: Calculating the curvature along the geodesic
3. **Deviation Analysis**: Calculating the acceleration required to deviate from the geodesic

The `Worldline` interface represents the complete history of an entity through spacetime. It provides methods for:

1. **Time Analysis**: Calculating total proper time and action
2. **Path Management**: Adding paths to the worldline
3. **Position Lookup**: Finding the position at a given proper time
4. **Causal Analysis**: Checking if the worldline is timelike

## Implementation Requirements

Implementations of the `SpacetimePath` interface must:

1. Store source and target nodes, path type, and intermediate points
2. Calculate proper and coordinate lengths
3. Determine if a path is traversable
4. Calculate the action along the path
5. Support parameterization and causal classification

Implementations of the `GeodesicPath` interface must:

1. Implement all methods from `SpacetimePath`
2. Verify if a path is a true geodesic
3. Calculate curvature along the geodesic
4. Determine acceleration required for deviation

Implementations of the `Worldline` interface must:

1. Store the entity and its paths through spacetime
2. Calculate total proper time and action
3. Support adding paths to the worldline
4. Determine position at a given proper time
5. Check if the worldline is timelike

The implementation should reflect our understanding of how entities move through spacetime, how geodesics represent optimal paths, and how worldlines represent the complete history of an entity.

## File Location

This type definition should be placed in:
```
spicetime-architecture/packages/utils/origin.rfr/time.rfr/paths/path.type.ts
```

## Related Files

- `paths/path.ts`: Implementation of spacetime paths
- `paths/geodesic.ts`: Implementation of geodesic paths
- `paths/worldline.ts`: Implementation of worldlines
- `nodes/node.type.ts`: Definition of spacetime nodes
- `manifold/manifold.type.ts`: Definition of the spacetime manifold
