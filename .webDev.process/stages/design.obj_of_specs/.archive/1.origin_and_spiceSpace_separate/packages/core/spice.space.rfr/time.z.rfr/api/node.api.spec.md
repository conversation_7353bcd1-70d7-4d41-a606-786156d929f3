# Spacetime Node API Specification

## Overview

This specification defines the API for Spacetime Nodes in the time.rfr package. It provides a clean, simple interface for common operations while abstracting away the complex mathematical machinery of general relativity.

## API Definition

```typescript
/**
 * Represents the API for a spacetime node
 * 
 * @typeParam D - Type of data associated with the node
 */
export interface SpacetimeNodeAPI<D = any> {
  /**
   * Unique identifier for the node
   */
  readonly id: symbol;
  
  /**
   * Data associated with the node
   */
  readonly data: D;
  
  /**
   * Conceptual mass of the node
   */
  readonly mass: number;
  
  /**
   * Position of the node in spacetime
   * (simplified representation of spacetime coordinates)
   */
  readonly position: {
    x: number;
    y: number;
    z: number;
    t: number;
  };
  
  /**
   * Connected nodes
   */
  readonly connections: {
    /**
     * Nodes that this node connects to
     */
    outgoing: SpacetimeNodeAPI<D>[];
    
    /**
     * Nodes that connect to this node
     */
    incoming: SpacetimeNodeAPI<D>[];
  };
  
  /**
   * Time properties of the node
   */
  readonly time: {
    /**
     * Local time at the node
     */
    local: number;
    
    /**
     * Time dilation factor relative to the global reference frame
     */
    dilationFactor: number;
    
    /**
     * Advances the node's local time
     * 
     * @param amount Amount to advance
     * @returns The updated node
     */
    advance(amount: number): SpacetimeNodeAPI<D>;
  };
  
  /**
   * Gravity properties of the node
   */
  readonly gravity: {
    /**
     * Gravitational potential at the node
     */
    potential: number;
    
    /**
     * Gravitational influence on other nodes
     * 
     * @param otherNode Other node
     * @returns The gravitational influence (0-1)
     */
    influenceOn(otherNode: SpacetimeNodeAPI<D>): number;
    
    /**
     * Nodes influenced by this node, sorted by influence
     * 
     * @param threshold Minimum influence threshold (0-1)
     * @returns Influenced nodes
     */
    influencedNodes(threshold: number): Array<{
      node: SpacetimeNodeAPI<D>;
      influence: number;
    }>;
  };
  
  /**
   * Causal properties of the node
   */
  readonly causality: {
    /**
     * Gets nodes in the causal future of this node
     * 
     * @param maxDistance Maximum causal distance
     * @returns Nodes in the causal future
     */
    future(maxDistance?: number): SpacetimeNodeAPI<D>[];
    
    /**
     * Gets nodes in the causal past of this node
     * 
     * @param maxDistance Maximum causal distance
     * @returns Nodes in the causal past
     */
    past(maxDistance?: number): SpacetimeNodeAPI<D>[];
    
    /**
     * Checks if this node can causally affect another node
     * 
     * @param otherNode Other node
     * @returns True if this node can causally affect the other
     */
    canAffect(otherNode: SpacetimeNodeAPI<D>): boolean;
    
    /**
     * Gets the causal distance to another node
     * 
     * @param otherNode Other node
     * @returns The causal distance (steps), or Infinity if not causally connected
     */
    distanceTo(otherNode: SpacetimeNodeAPI<D>): number;
  };
  
  /**
   * Path properties of the node
   */
  readonly paths: {
    /**
     * Gets the shortest path to another node
     * 
     * @param otherNode Other node
     * @returns The shortest path, or null if no path exists
     */
    shortestTo(otherNode: SpacetimeNodeAPI<D>): SpacetimePathAPI<D> | null;
    
    /**
     * Gets all paths from this node to another node
     * 
     * @param otherNode Other node
     * @returns All paths
     */
    allTo(otherNode: SpacetimeNodeAPI<D>): SpacetimePathAPI<D>[];
    
    /**
     * Creates a path from this node to another node
     * 
     * @param otherNode Other node
     * @param type Path type
     * @returns The new path
     */
    createTo(otherNode: SpacetimeNodeAPI<D>, type?: PathTypeAPI): SpacetimePathAPI<D>;
  };
  
  /**
   * Updates the node's data
   * 
   * @param newData New data
   * @returns The updated node
   */
  updateData(newData: D): SpacetimeNodeAPI<D>;
  
  /**
   * Updates the node's mass
   * 
   * @param newMass New mass
   * @returns The updated node
   */
  updateMass(newMass: number): SpacetimeNodeAPI<D>;
  
  /**
   * Updates the node's position
   * 
   * @param newPosition New position
   * @returns The updated node
   */
  updatePosition(newPosition: {
    x?: number;
    y?: number;
    z?: number;
    t?: number;
  }): SpacetimeNodeAPI<D>;
  
  /**
   * Gets the node's perspective
   * 
   * @returns An observer at this node's position
   */
  getPerspective(): SpacetimeObserverAPI<D>;
  
  /**
   * Accesses the underlying mathematical model
   * (for advanced operations that need the full GR machinery)
   * 
   * @returns The underlying node model
   */
  getModel(): SpacetimeNode<D>;
}

/**
 * Path type (simplified version of PathType)
 */
export enum PathTypeAPI {
  /**
   * Normal path
   */
  NORMAL,
  
  /**
   * Fast path
   */
  FAST,
  
  /**
   * Slow path
   */
  SLOW,
  
  /**
   * Optimal path
   */
  OPTIMAL
}

/**
 * Represents the API for a spacetime path
 * 
 * @typeParam D - Type of data associated with nodes
 */
export interface SpacetimePathAPI<D = any> {
  /**
   * Unique identifier for the path
   */
  readonly id: symbol;
  
  /**
   * Source node
   */
  readonly source: SpacetimeNodeAPI<D>;
  
  /**
   * Target node
   */
  readonly target: SpacetimeNodeAPI<D>;
  
  /**
   * Path type
   */
  readonly type: PathTypeAPI;
  
  /**
   * Length of the path
   */
  readonly length: number;
  
  /**
   * Time to traverse the path
   */
  readonly traversalTime: number;
  
  /**
   * Traverses the path
   * 
   * @param observer Observer to move along the path
   * @returns The observer at the new position
   */
  traverse(observer: SpacetimeObserverAPI<D>): SpacetimeObserverAPI<D>;
  
  /**
   * Reverses the path
   * 
   * @returns The reversed path
   */
  reverse(): SpacetimePathAPI<D>;
  
  /**
   * Accesses the underlying mathematical model
   * (for advanced operations that need the full GR machinery)
   * 
   * @returns The underlying path model
   */
  getModel(): SpacetimePath<D>;
}

/**
 * Represents the API for a spacetime observer
 * 
 * @typeParam D - Type of data associated with nodes
 */
export interface SpacetimeObserverAPI<D = any> {
  /**
   * Unique identifier for the observer
   */
  readonly id: symbol;
  
  /**
   * The node representing the observer's position
   */
  readonly position: SpacetimeNodeAPI<D>;
  
  /**
   * The observer's local time
   */
  readonly localTime: number;
  
  /**
   * Observes a node from this observer's perspective
   * 
   * @param node Node to observe
   * @returns The observed node properties
   */
  observe(node: SpacetimeNodeAPI<D>): {
    position: {
      x: number;
      y: number;
      z: number;
      t: number;
    };
    timeDilation: number;
    apparentMass: number;
  };
  
  /**
   * Moves the observer to a new position
   * 
   * @param newPosition New position node
   * @returns The updated observer
   */
  moveTo(newPosition: SpacetimeNodeAPI<D>): SpacetimeObserverAPI<D>;
  
  /**
   * Advances the observer's local time
   * 
   * @param amount Amount to advance
   * @returns The updated observer
   */
  advanceTime(amount: number): SpacetimeObserverAPI<D>;
  
  /**
   * Accesses the underlying mathematical model
   * (for advanced operations that need the full GR machinery)
   * 
   * @returns The underlying observer model
   */
  getModel(): SpacetimeObserver<D>;
}
```

## Usage

The Spacetime Node API provides a clean, simple interface for common operations on spacetime nodes. It abstracts away the complex mathematical machinery of general relativity while still providing access to the underlying model for advanced operations.

Key features include:

1. **Simple Position Representation**: Uses a straightforward {x, y, z, t} object instead of complex spacetime coordinates
2. **Intuitive Time Handling**: Provides methods for advancing time and accessing time dilation
3. **Practical Gravity Interface**: Focuses on influence between nodes rather than tensor calculations
4. **Causal Analysis**: Enables exploration of causal relationships between nodes
5. **Path Management**: Provides methods for finding and creating paths between nodes
6. **Observer Perspective**: Allows for observing nodes from different perspectives

For advanced operations that require the full mathematical machinery of general relativity, each API object provides a `getModel()` method that returns the underlying model with access to all the tensor calculations and GR machinery.

## Implementation Approach

The implementation of this API should:

1. **Provide a Facade**: Create a simple facade over the complex underlying model
2. **Use Lazy Calculation**: Calculate complex properties only when needed
3. **Cache Results**: Cache results of expensive calculations
4. **Maintain Consistency**: Ensure that the API and the underlying model stay in sync

The API should be implemented as a wrapper around the underlying model, delegating to the model for complex calculations while providing a simpler interface for common operations.

## File Location

This API definition should be placed in:
```
spicetime-architecture/packages/utils/origin.rfr/time.rfr/api/node.api.ts
```

## Related Files

- `api/path.api.ts`: API for spacetime paths
- `api/observer.api.ts`: API for spacetime observers
- `api/manifold.api.ts`: API for the spacetime manifold
