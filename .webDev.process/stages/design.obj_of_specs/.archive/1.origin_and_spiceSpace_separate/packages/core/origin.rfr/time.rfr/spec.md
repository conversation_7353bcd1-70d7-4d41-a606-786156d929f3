# Time Reference Frame (time.rfr) Specification

## Overview

The Time Reference Frame (time.rfr) is a fundamental component of the Origin Reference Frame (origin.rfr) in the SpiceTime architecture. It provides an archiving service for storing and retrieving state, enabling the system to stow away the present and install the future as a restructured present. Time.rfr serves as a tool for restructuring and managing the temporal aspects of the system.

Unlike the more complex spacetime model that comes later in spice.space.rfr, the origin time.rfr is focused on basic archiving and rotation functionality, providing a foundation for more advanced temporal operations.

## Core Concepts

### Time as an Archiving Service

Time in the SpiceTime architecture is conceptualized as an archiving service that:

1. **Archives Present State**: Stores the current state of the system for future reference.
2. **Rotates Time**: Moves the present into the past and installs the future as the new present.
3. **Manages Temporal Relationships**: Tracks causal relationships between events.
4. **Provides Persistence**: Abstracts storage mechanisms for persistent data.

### Time Rotation

Time rotation is the process of moving the present state into the past and installing a new state as the present. This enables the system to evolve over time while maintaining a history of previous states:

```
Before Rotation:
Past <- Present -> Future

After Rotation:
Past <- (Old Present) <- Present -> Future
```

### Storage Abstraction

Time.rfr provides an abstraction layer over various storage mechanisms, including:

1. **Git**: For version control and history tracking.
2. **Graph Databases**: For storing complex relationships.
3. **MongoDB**: For document-based storage.
4. **Vector Databases**: For similarity-based retrieval.
5. **Blockchain Elements**: For immutable, distributed storage.

## Architecture

```
time.rfr/
├── src/                # Source code
│   ├── time.ts         # Main time implementation
│   ├── archive.ts      # Archiving functionality
│   ├── rotation.ts     # Time rotation
│   ├── storage/        # Storage adapters
│   │   ├── git.ts      # Git adapter
│   │   ├── graph.ts    # Graph database adapter
│   │   ├── mongo.ts    # MongoDB adapter
│   │   ├── vector.ts   # Vector database adapter
│   │   ├── blockchain.ts # Blockchain adapter
│   │   └── index.ts    # Storage exports
│   ├── timeline.ts     # Timeline management
│   ├── events.ts       # Temporal events
│   └── metrics.ts      # Time metrics
├── types/              # Type definitions
│   └── index.type.ts   # Type exports
└── tests/              # Unit tests
    ├── time.test.ts
    ├── archive.test.ts
    ├── rotation.test.ts
    └── storage.test.ts
```

## Core Interfaces

### TimeInterface

```typescript
/**
 * Interface for the SpiceTime Time module
 */
export interface TimeInterface {
  /**
   * Archive the current state
   * @param name Name of the archive
   * @param data Data to archive
   * @returns Archive ID
   */
  archive(name: string, data: any): Promise<string>;
  
  /**
   * Retrieve an archived state
   * @param id Archive ID
   * @returns Archived data
   */
  retrieve(id: string): Promise<any>;
  
  /**
   * Rotate time (move present to past, install future as present)
   * @param futureState Future state to install as present
   * @returns New present state
   */
  rotate(futureState: any): Promise<any>;
  
  /**
   * Get the current time
   * @returns Current time
   */
  now(): TimePoint;
  
  /**
   * Get the timeline
   * @returns Timeline
   */
  getTimeline(): Timeline;
  
  /**
   * Create a marker at the current time
   * @param name Marker name
   * @returns Marker
   */
  createMarker(name: string): TimeMarker;
  
  /**
   * Get a marker by name
   * @param name Marker name
   * @returns Marker or null if not found
   */
  getMarker(name: string): TimeMarker | null;
  
  /**
   * Schedule an event
   * @param event Event to schedule
   * @returns Event ID
   */
  schedule(event: ScheduledEvent): string;
  
  /**
   * Cancel a scheduled event
   * @param eventId Event ID
   * @returns Whether the event was cancelled
   */
  cancelEvent(eventId: string): boolean;
  
  /**
   * Get time metrics
   * @returns Time metrics
   */
  getMetrics(): TimeMetrics;
}
```

### TimePoint

```typescript
/**
 * Represents a point in time
 */
export interface TimePoint {
  /**
   * Timestamp in milliseconds
   */
  timestamp: number;
  
  /**
   * Logical clock value
   */
  logical: number;
  
  /**
   * Vector clock values
   */
  vector: Record<string, number>;
  
  /**
   * Compare this time point with another
   * @param other Other time point
   * @returns -1 if this is before other, 0 if equal, 1 if after
   */
  compare(other: TimePoint): -1 | 0 | 1;
  
  /**
   * Check if this time point is before another
   * @param other Other time point
   * @returns Whether this is before other
   */
  isBefore(other: TimePoint): boolean;
  
  /**
   * Check if this time point is after another
   * @param other Other time point
   * @returns Whether this is after other
   */
  isAfter(other: TimePoint): boolean;
  
  /**
   * Check if this time point is equal to another
   * @param other Other time point
   * @returns Whether this is equal to other
   */
  isEqual(other: TimePoint): boolean;
  
  /**
   * Add a duration to this time point
   * @param duration Duration to add
   * @returns New time point
   */
  add(duration: Duration): TimePoint;
  
  /**
   * Subtract a duration from this time point
   * @param duration Duration to subtract
   * @returns New time point
   */
  subtract(duration: Duration): TimePoint;
  
  /**
   * Format this time point as a string
   * @param format Format string
   * @returns Formatted string
   */
  format(format?: string): string;
}
```

### Timeline

```typescript
/**
 * Represents a timeline
 */
export interface Timeline {
  /**
   * Events in the timeline
   */
  events: TimeEvent[];
  
  /**
   * Markers in the timeline
   */
  markers: Map<string, TimeMarker>;
  
  /**
   * Branches of the timeline
   */
  branches: Map<string, Timeline>;
  
  /**
   * Add an event to the timeline
   * @param event Event to add
   */
  addEvent(event: TimeEvent): void;
  
  /**
   * Add a marker to the timeline
   * @param marker Marker to add
   */
  addMarker(marker: TimeMarker): void;
  
  /**
   * Create a branch of the timeline
   * @param name Branch name
   * @returns New timeline branch
   */
  createBranch(name: string): Timeline;
  
  /**
   * Get events between two time points
   * @param start Start time
   * @param end End time
   * @returns Events between start and end
   */
  getEventsBetween(start: TimePoint, end: TimePoint): TimeEvent[];
  
  /**
   * Get events before a time point
   * @param time Time point
   * @param limit Maximum number of events to return
   * @returns Events before time
   */
  getEventsBefore(time: TimePoint, limit?: number): TimeEvent[];
  
  /**
   * Get events after a time point
   * @param time Time point
   * @param limit Maximum number of events to return
   * @returns Events after time
   */
  getEventsAfter(time: TimePoint, limit?: number): TimeEvent[];
}
```

### StorageAdapter

```typescript
/**
 * Interface for storage adapters
 */
export interface StorageAdapter {
  /**
   * Store data
   * @param key Storage key
   * @param data Data to store
   * @returns Storage ID
   */
  store(key: string, data: any): Promise<string>;
  
  /**
   * Retrieve data
   * @param id Storage ID
   * @returns Retrieved data
   */
  retrieve(id: string): Promise<any>;
  
  /**
   * Delete data
   * @param id Storage ID
   * @returns Whether the data was deleted
   */
  delete(id: string): Promise<boolean>;
  
  /**
   * List stored data
   * @param prefix Key prefix
   * @returns List of storage IDs
   */
  list(prefix?: string): Promise<string[]>;
  
  /**
   * Check if data exists
   * @param id Storage ID
   * @returns Whether the data exists
   */
  exists(id: string): Promise<boolean>;
}
```

## Implementation

### Time

```typescript
/**
 * Create a time module
 * @param options Time options
 * @returns Time interface
 */
export function createTime(options: TimeOptions = {}): TimeInterface {
  // Storage adapter
  const storage = options.storage || createDefaultStorage();
  
  // Timeline
  const timeline = options.timeline || createTimeline();
  
  // Current time
  let currentTime = createTimePoint();
  
  // Scheduled events
  const scheduledEvents = new Map<string, ScheduledEvent>();
  
  // Metrics
  const metrics: TimeMetrics = {
    archiveCount: 0,
    retrieveCount: 0,
    rotationCount: 0,
    eventCount: 0,
    averageArchiveSize: 0,
    averageRetrieveTime: 0,
    averageRotationTime: 0
  };
  
  // Update metrics
  const updateMetrics = (metric: keyof TimeMetrics, value: number) => {
    if (typeof metrics[metric] === 'number') {
      metrics[metric] = (metrics[metric] as number) + value;
    }
  };
  
  return {
    async archive(name: string, data: any): Promise<string> {
      const startTime = Date.now();
      
      // Create archive object
      const archive = {
        name,
        data,
        timestamp: currentTime.timestamp,
        logical: currentTime.logical,
        vector: { ...currentTime.vector }
      };
      
      // Store in storage
      const id = await storage.store(`archive:${name}:${currentTime.timestamp}`, archive);
      
      // Create event
      const event: TimeEvent = {
        id: generateUniqueId(),
        type: 'archive',
        time: { ...currentTime },
        data: { archiveId: id, name }
      };
      
      // Add event to timeline
      timeline.addEvent(event);
      
      // Update metrics
      updateMetrics('archiveCount', 1);
      updateMetrics('averageArchiveSize', JSON.stringify(data).length);
      
      return id;
    },
    
    async retrieve(id: string): Promise<any> {
      const startTime = Date.now();
      
      // Retrieve from storage
      const archive = await storage.retrieve(id);
      
      // Create event
      const event: TimeEvent = {
        id: generateUniqueId(),
        type: 'retrieve',
        time: { ...currentTime },
        data: { archiveId: id }
      };
      
      // Add event to timeline
      timeline.addEvent(event);
      
      // Update metrics
      updateMetrics('retrieveCount', 1);
      updateMetrics('averageRetrieveTime', Date.now() - startTime);
      
      return archive.data;
    },
    
    async rotate(futureState: any): Promise<any> {
      const startTime = Date.now();
      
      // Archive current state
      const currentStateId = await this.archive('present', getCurrentState());
      
      // Install future state as present
      setCurrentState(futureState);
      
      // Create event
      const event: TimeEvent = {
        id: generateUniqueId(),
        type: 'rotation',
        time: { ...currentTime },
        data: { previousStateId: currentStateId }
      };
      
      // Add event to timeline
      timeline.addEvent(event);
      
      // Update metrics
      updateMetrics('rotationCount', 1);
      updateMetrics('averageRotationTime', Date.now() - startTime);
      
      return futureState;
    },
    
    now(): TimePoint {
      return { ...currentTime };
    },
    
    getTimeline(): Timeline {
      return timeline;
    },
    
    createMarker(name: string): TimeMarker {
      const marker: TimeMarker = {
        name,
        time: { ...currentTime }
      };
      
      // Add marker to timeline
      timeline.addMarker(marker);
      
      return marker;
    },
    
    getMarker(name: string): TimeMarker | null {
      return timeline.markers.get(name) || null;
    },
    
    schedule(event: ScheduledEvent): string {
      const id = event.id || generateUniqueId();
      
      // Add ID if not provided
      event.id = id;
      
      // Schedule event
      scheduledEvents.set(id, event);
      
      // Update metrics
      updateMetrics('eventCount', 1);
      
      return id;
    },
    
    cancelEvent(eventId: string): boolean {
      return scheduledEvents.delete(eventId);
    },
    
    getMetrics(): TimeMetrics {
      return { ...metrics };
    }
  };
}
```

### Storage Adapters

```typescript
/**
 * Create a Git storage adapter
 * @param options Git options
 * @returns Storage adapter
 */
export function createGitStorage(options: GitStorageOptions = {}): StorageAdapter {
  // Git repository path
  const repoPath = options.repoPath || '.git';
  
  // Git branch
  const branch = options.branch || 'main';
  
  return {
    async store(key: string, data: any): Promise<string> {
      // Generate a unique ID for the data
      const id = generateUniqueId();
      
      // Create a file path for the data
      const filePath = `${repoPath}/objects/${id}`;
      
      // Write data to file
      await fs.writeFile(filePath, JSON.stringify(data));
      
      // Add file to Git
      await git.add(filePath);
      
      // Commit changes
      await git.commit(`Store ${key}`);
      
      return id;
    },
    
    async retrieve(id: string): Promise<any> {
      // Create a file path for the data
      const filePath = `${repoPath}/objects/${id}`;
      
      // Read data from file
      const data = await fs.readFile(filePath, 'utf-8');
      
      // Parse and return data
      return JSON.parse(data);
    },
    
    async delete(id: string): Promise<boolean> {
      // Create a file path for the data
      const filePath = `${repoPath}/objects/${id}`;
      
      // Check if file exists
      if (!await fs.exists(filePath)) {
        return false;
      }
      
      // Remove file
      await fs.unlink(filePath);
      
      // Add removal to Git
      await git.add(filePath);
      
      // Commit changes
      await git.commit(`Delete ${id}`);
      
      return true;
    },
    
    async list(prefix?: string): Promise<string[]> {
      // List all files in the objects directory
      const files = await fs.readdir(`${repoPath}/objects`);
      
      // Filter by prefix if provided
      if (prefix) {
        return files.filter(file => file.startsWith(prefix));
      }
      
      return files;
    },
    
    async exists(id: string): Promise<boolean> {
      // Create a file path for the data
      const filePath = `${repoPath}/objects/${id}`;
      
      // Check if file exists
      return await fs.exists(filePath);
    }
  };
}
```

## Usage

### Basic Usage

```typescript
import { createTime } from '@spicetime/core/origin.rfr/time.rfr';

// Create a time module
const time = createTime();

// Archive some data
const archiveId = await time.archive('user-data', { name: 'John', age: 30 });

// Retrieve the data later
const userData = await time.retrieve(archiveId);
console.log(userData); // { name: 'John', age: 30 }

// Create a marker
const marker = time.createMarker('important-point');

// Get the current time
const now = time.now();
console.log(`Current time: ${now.format()}`);

// Schedule an event
const eventId = time.schedule({
  time: now.add({ minutes: 5 }),
  action: () => console.log('Event triggered!'),
  data: { type: 'reminder' }
});

// Rotate time
const futureState = { name: 'John', age: 31 };
await time.rotate(futureState);
```

### Using Custom Storage

```typescript
import { createTime, createGitStorage } from '@spicetime/core/origin.rfr/time.rfr';

// Create a Git storage adapter
const gitStorage = createGitStorage({
  repoPath: '/path/to/repo',
  branch: 'time-data'
});

// Create a time module with custom storage
const time = createTime({
  storage: gitStorage
});

// Use the time module
const archiveId = await time.archive('config', { theme: 'dark', language: 'en' });
const config = await time.retrieve(archiveId);
```

### Working with Timelines

```typescript
import { createTime } from '@spicetime/core/origin.rfr/time.rfr';

// Create a time module
const time = createTime();

// Get the timeline
const timeline = time.getTimeline();

// Create a branch
const experimentBranch = timeline.createBranch('experiment');

// Add events to the branch
experimentBranch.addEvent({
  id: 'event1',
  type: 'experiment',
  time: time.now(),
  data: { result: 'success' }
});

// Get events from the branch
const events = experimentBranch.getEventsBefore(time.now(), 10);
console.log(`Found ${events.length} events`);
```

## Integration with Other Modules

The time.rfr module integrates with other modules in the following ways:

1. **Kernel Reference Frame**: Provides time-based resources to the kernel.
2. **Process Reference Frame**: Enables processes to archive and retrieve state.
3. **Categorical Types**: Uses categorical types for type-safe operations.

## Next Steps

1. **Implement Core Time Module**: Develop the core time implementation with archiving and rotation.
2. **Create Storage Adapters**: Implement adapters for different storage mechanisms.
3. **Develop Timeline Management**: Build the timeline functionality for tracking events and markers.
4. **Implement Event Scheduling**: Create the event scheduling system.
5. **Create Testing Framework**: Develop a comprehensive testing framework for the time module.

## Conclusion

The Time Reference Frame (time.rfr) provides a powerful foundation for temporal operations in the SpiceTime architecture. By conceptualizing time as an archiving service, we create a flexible system for storing, retrieving, and manipulating state over time. The abstraction over various storage mechanisms enables the system to adapt to different persistence requirements, while the timeline functionality provides a rich model for tracking and analyzing temporal relationships.
