# Process Reference Frame (stProcess.rfr) Specification

## Overview

The Process Reference Frame (stProcess.rfr) provides an extension of the Node.js process model, serving as the foundation for process-based programming in the SpiceTime architecture. It defines processes that operate on types, transforming them through space and time.

In this specification, we focus on the basic extension of the Node.js process model and integrate with the kernel for task scheduling based on process priority. The more advanced tripodic structure will be implemented later in spice.space.rfr.

Processes in SpiceTime are designed to work with the Resource Quota System, requesting resources based on their needs and priorities. They also integrate with the Pragma system for declaring dependencies, resource requirements, and other metadata.

## Core Concepts

### Process as an Extension of Node Process

A process in SpiceTime extends the Node.js process model with additional capabilities:

1. **Generator-Based Execution**: Processes are defined as generator functions that can pause and resume execution across multiple tics.
2. **State Management**: Processes maintain state between executions, allowing for complex, stateful operations.
3. **Event System**: Processes can emit and listen for events, enabling communication between processes.
4. **Hierarchical Structure**: Processes can have parent-child relationships, forming a tree structure similar to the DOM.
5. **Priority-Based Scheduling**: Processes have a priority value (0-1) that determines their position in the kernel's task queue.

### Process Priority

Each process has a priority value between 0 and 1, with higher values indicating higher priority. The kernel uses this priority to determine the order in which processes receive tics. The priority can be influenced by the behavioral CSS system, which defines organizational behaviors through a selector-based syntax.

```typescript
/**
 * Process priority
 *
 * A value between 0 and 1, with higher values indicating higher priority.
 * The kernel uses this priority to determine the order in which processes
 * receive tics.
 *
 * Priority can be influenced by the behavioral CSS system, which defines
 * organizational behaviors through a selector-based syntax.
 */
export type ProcessPriority = number;

/**
 * Priority sequence
 *
 * Defines the order in which different types of tasks are prioritized.
 * For example, "TIDM" prioritizes Time-sensitive tasks, then Important tasks,
 * then Deadline-driven tasks, and finally Maintenance tasks.
 */
export type PrioritySequence = string;
```

### Process Lifecycle

Processes go through the following lifecycle states:

```typescript
/**
 * Process lifecycle states
 */
export enum ProcessState {
  CREATED = 'created',   // Process has been created but not started
  STARTING = 'starting', // Process is starting
  RUNNING = 'running',   // Process is running
  PAUSED = 'paused',     // Process is paused
  STOPPED = 'stopped',   // Process has been stopped
  COMPLETED = 'completed', // Process has completed successfully
  ERROR = 'error'        // Process has encountered an error
}
```

## Architecture

```
stProcess.rfr/
├── src/                # Source code
│   ├── process.ts      # Main process implementation
│   ├── context.ts      # Process context
│   ├── events.ts       # Event system
│   ├── tic.ts          # Tic management
│   ├── helpers.ts      # Helper functions
│   ├── middleware.ts   # Middleware system
│   ├── selector.ts     # Process selection
│   ├── filesystem.ts   # Filesystem integration
│   ├── logger.ts       # Logging system
│   ├── behavioral.ts   # Behavioral CSS system
│   ├── pragma.ts      # Pragma integration
│   ├── quota.ts       # Resource quota integration
│   └── kernel/         # Kernel integration
│       ├── index.ts    # Kernel exports
│       ├── core.ts     # Kernel core
│       ├── node.ts     # Kernel node
│       ├── priority.ts # Priority system
│       ├── geodesic.ts # Geodesic calculator
│       ├── holog.ts    # HoloG implementation
│       ├── react.ts    # React adapter
│       ├── request.ts  # Request system
│       ├── api.ts      # API system
│       └── types.ts    # Kernel types
├── types/              # Type definitions
│   └── index.type.ts   # Type exports
└── tests/              # Unit tests
    ├── process.test.ts
    ├── context.test.ts
    ├── events.test.ts
    ├── behavioral.test.ts
    ├── pragma.test.ts
    ├── quota.test.ts
    └── kernel.test.ts
```

## Core Interfaces

### ProcessInterface

```typescript
/**
 * Interface for a SpiceTime process
 *
 * @template Props - Type of process properties
 * @template Result - Type of process result
 */
export interface ProcessInterface<Props = any, Result = any> {
  /**
   * Unique process identifier
   */
  readonly id: string;

  /**
   * Path in the filesystem
   */
  path: string;

  /**
   * Parent process ID (if any)
   */
  parentId: string | null;

  /**
   * Process properties
   */
  readonly props: Props;

  /**
   * Child processes
   */
  readonly children: ProcessInterface[];

  /**
   * Current lifecycle state
   */
  state: ProcessState;

  /**
   * Current tic assigned to the process
   */
  currentTic: TicInfo | null;

  /**
   * Process result (if completed)
   */
  result: Result | null;

  /**
   * Error that occurred (if in error state)
   */
  error: Error | null;

  /**
   * Process priority (0-1, higher is more important)
   */
  priority: ProcessPriority;

  /**
   * Start the process
   */
  start(): Promise<void>;

  /**
   * Stop the process
   */
  stop(): Promise<void>;

  /**
   * Pause the process
   */
  pause(): Promise<void>;

  /**
   * Resume the process
   */
  resume(): Promise<void>;

  /**
   * Request a tic from the kernel
   *
   * @param priority - Priority of the tic request
   * @returns Whether a tic was allocated
   */
  requestTic(priority?: ProcessPriority): Promise<boolean>;

  /**
   * Subscribe to an event
   *
   * @param event - Event to subscribe to
   * @param handler - Event handler
   * @returns Unsubscribe function
   */
  subscribe<T = any>(event: string, handler: (data: T) => void): () => void;

  /**
   * Emit an event
   *
   * @param event - Event to emit
   * @param data - Event data
   */
  emit<T = any>(event: string, data?: T): void;
}
```

### ProcessContext

```typescript
/**
 * Process context
 */
export interface ProcessContext {
  /**
   * Kernel interface
   */
  kernel: KernelInterface;

  /**
   * Parent process (if any)
   */
  parent: ProcessInterface | null;

  /**
   * Logger
   */
  logger: Logger;

  /**
   * Filesystem
   */
  fs: FileSystem;

  /**
   * Environment variables
   */
  env: Record<string, string>;

  /**
   * Current working directory
   */
  cwd: string;

  /**
   * Pragma processor
   */
  pragma: PragmaProcessor;

  /**
   * Resource quota manager
   */
  quota: ResourceQuotaManager;

  /**
   * Behavioral CSS system
   */
  behavioral: BehavioralCSSSystem;
}
```

### TicInfo

```typescript
/**
 * Tic information
 */
export interface TicInfo {
  /**
   * Unique tic identifier
   */
  id: string;

  /**
   * ID of the process that received the tic
   */
  processId: string;

  /**
   * Priority of the tic (0-1, higher is more important)
   */
  priority: ProcessPriority;

  /**
   * Start timestamp
   */
  startTime: number;

  /**
   * Duration of the tic in milliseconds
   */
  duration: number;
}
```

## Implementation

### Process

```typescript
/**
 * Process class that wraps a generator function
 * @template Props - Type of process properties
 * @template Result - Type of process result
 */
export class Process<Props = any, Result = any> extends EventEmitter implements ProcessInterface<Props, Result> {
  /** Unique process identifier */
  public readonly id: string;

  /** Path in the filesystem */
  public path: string;

  /** Parent process ID (if any) */
  public parentId: string | null;

  /** Process properties */
  public readonly props: Props;

  /** Child processes */
  public readonly children: ProcessInterface[];

  /** Current lifecycle state */
  public state: ProcessState;

  /** Generator function that defines the process */
  private readonly generatorFn: (props: Props) => Generator<any, Result, any>;

  /** Generator instance */
  private generator: Generator<any, Result, any> | null;

  /** Process context */
  private context: ProcessContext;

  /** Current tic */
  public currentTic: TicInfo | null;

  /** Process result */
  public result: Result | null;

  /** Error that occurred */
  public error: Error | null;

  /** Process priority */
  public priority: ProcessPriority;

  /**
   * Create a new Process
   *
   * @param generatorFn - Generator function that defines the process
   * @param props - Properties to pass to the generator function
   * @param context - Context to pass to the process
   * @param priority - Process priority (0-1, higher is more important)
   */
  constructor(
    generatorFn: (props: Props) => Generator<any, Result, any>,
    props: Props = {} as Props,
    context: ProcessContext | null = null,
    priority: ProcessPriority = 0.5
  ) {
    super();

    this.id = uuidv4();
    this.path = '';
    this.parentId = null;
    this.props = props;
    this.children = [];
    this.state = ProcessState.CREATED;
    this.generatorFn = generatorFn;
    this.generator = null;
    this.context = context || createDefaultContext(this);
    this.currentTic = null;
    this.result = null;
    this.error = null;
    this.priority = priority;

    // Register with kernel
    if (this.context.kernel) {
      this.context.kernel.registerProcess(this);
    }
  }

  /**
   * Start the process
   */
  public async start(): Promise<void> {
    if (this.state !== ProcessState.CREATED && this.state !== ProcessState.STOPPED) {
      throw new Error(`Cannot start process in state: ${this.state}`);
    }

    this._setState(ProcessState.STARTING);

    try {
      // Initialize the generator
      this.generator = this.generatorFn(this.props);

      // Emit start event
      this.emit('start');

      // Set state to running
      this._setState(ProcessState.RUNNING);

      // Request first tic
      await this.requestTic();
    } catch (error) {
      this.error = error as Error;
      this._setState(ProcessState.ERROR);
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Stop the process
   */
  public async stop(): Promise<void> {
    if (this.state !== ProcessState.RUNNING && this.state !== ProcessState.PAUSED) {
      throw new Error(`Cannot stop process in state: ${this.state}`);
    }

    // Release current tic if any
    if (this.currentTic) {
      this.context.kernel.releaseTic(this.currentTic.id);
      this.currentTic = null;
    }

    // Set state to stopped
    this._setState(ProcessState.STOPPED);

    // Emit stop event
    this.emit('stop');
  }

  /**
   * Pause the process
   */
  public async pause(): Promise<void> {
    if (this.state !== ProcessState.RUNNING) {
      throw new Error(`Cannot pause process in state: ${this.state}`);
    }

    // Release current tic if any
    if (this.currentTic) {
      this.context.kernel.releaseTic(this.currentTic.id);
      this.currentTic = null;
    }

    // Set state to paused
    this._setState(ProcessState.PAUSED);

    // Emit pause event
    this.emit('pause');
  }

  /**
   * Resume the process
   */
  public async resume(): Promise<void> {
    if (this.state !== ProcessState.PAUSED) {
      throw new Error(`Cannot resume process in state: ${this.state}`);
    }

    // Set state to running
    this._setState(ProcessState.RUNNING);

    // Emit resume event
    this.emit('resume');

    // Request next tic
    await this.requestTic();
  }

  /**
   * Request a tic from the kernel
   *
   * @param priority - Priority of the tic request
   * @returns Whether a tic was allocated
   */
  public async requestTic(priority: ProcessPriority = this.priority): Promise<boolean> {
    if (this.state !== ProcessState.RUNNING) {
      return false;
    }

    this.emit('ticRequest', priority);

    try {
      // Request tic from kernel
      const tic = await this.context.kernel.requestTic(this.id, priority);

      if (!tic) {
        return false;
      }

      this.currentTic = tic;
      this.emit('ticAllocated', tic);

      // Execute the process for this tic
      await this._executeTic(tic);

      return true;
    } catch (error) {
      this.error = error as Error;
      this._setState(ProcessState.ERROR);
      this.emit('error', error);
      return false;
    }
  }

  /**
   * Execute the process for a tic
   *
   * @param tic - Tic information
   */
  private async _executeTic(tic: TicInfo): Promise<void> {
    if (!this.generator) {
      throw new Error('Generator not initialized');
    }

    try {
      // Execute the generator until it yields or completes
      const startTime = Date.now();
      const result = this.generator.next();

      // Release the tic
      this.context.kernel.releaseTic(tic.id);
      this.currentTic = null;

      if (result.done) {
        // Process completed
        this.result = result.value;
        this._setState(ProcessState.COMPLETED);
        this.emit('complete', result.value);
      } else {
        // Process yielded, request next tic
        await this.requestTic();
      }
    } catch (error) {
      // Release the tic
      this.context.kernel.releaseTic(tic.id);
      this.currentTic = null;

      // Set error state
      this.error = error as Error;
      this._setState(ProcessState.ERROR);
      this.emit('error', error);
    }
  }

  /**
   * Set the process state
   *
   * @param state - New state
   */
  private _setState(state: ProcessState): void {
    const oldState = this.state;
    this.state = state;
    this.emit('stateChange', { oldState, newState: state });
  }
}
```

### Process Creation

```typescript
/**
 * Create a new process
 *
 * @param generatorFn - Generator function that defines the process
 * @param props - Properties to pass to the generator function
 * @param context - Context to pass to the process
 * @param priority - Process priority (0-1, higher is more important)
 * @returns A new process
 */
export function createProcess<Props = any, Result = any>(
  generatorFn: (props: Props) => Generator<any, Result, any>,
  props: Props = {} as Props,
  context: ProcessContext | null = null,
  priority: ProcessPriority = 0.5
): Process<Props, Result> {
  return new Process(generatorFn, props, context, priority);
}
```

## Usage

### Creating and Starting a Process

```typescript
import { createProcess } from '@spicetime/core/origin.rfr/stProcess.rfr';

// Define a process
function* myProcess(props: { name: string }) {
  console.log(`Starting process: ${props.name}`);

  // Do some work
  for (let i = 0; i < 10; i++) {
    console.log(`${props.name}: Step ${i}`);
    yield; // Pause execution and wait for next tic
  }

  console.log(`Completed process: ${props.name}`);
  return 'success';
}

// Create a process
const process = createProcess(myProcess, { name: 'MyProcess' });

// Subscribe to events
process.subscribe('complete', (result) => {
  console.log(`Process completed with result: ${result}`);
});

// Start the process
await process.start();
```

### Creating a Process with Custom Priority

```typescript
import { createProcess } from '@spicetime/core/origin.rfr/stProcess.rfr';

// Define a high-priority process
function* highPriorityProcess(props: {}) {
  console.log('Starting high-priority process');

  // Do some work
  for (let i = 0; i < 5; i++) {
    console.log(`High-priority step ${i}`);
    yield; // Pause execution and wait for next tic
  }

  console.log('Completed high-priority process');
  return 'success';
}

// Create a process with high priority
const process = createProcess(highPriorityProcess, {}, null, 0.9);

// Start the process
await process.start();
```

### Creating a Process with Parent-Child Relationship

```typescript
import { createProcess } from '@spicetime/core/origin.rfr/stProcess.rfr';

// Define a parent process
function* parentProcess(props: {}) {
  console.log('Starting parent process');

  // Create child processes
  const child1 = createProcess(childProcess, { name: 'Child1' });
  const child2 = createProcess(childProcess, { name: 'Child2' });

  // Set parent-child relationship
  child1.parentId = this.id;
  child2.parentId = this.id;

  // Start child processes
  await child1.start();
  await child2.start();

  // Wait for child processes to complete
  while (child1.state !== 'completed' || child2.state !== 'completed') {
    yield; // Pause execution and wait for next tic
  }

  console.log('All child processes completed');
  return 'success';
}

// Define a child process
function* childProcess(props: { name: string }) {
  console.log(`Starting child process: ${props.name}`);

  // Do some work
  for (let i = 0; i < 3; i++) {
    console.log(`${props.name}: Step ${i}`);
    yield; // Pause execution and wait for next tic
  }

  console.log(`Completed child process: ${props.name}`);
  return 'success';
}

// Create and start the parent process
const parent = createProcess(parentProcess, {});
await parent.start();
```

## Additional Components

### Behavioral CSS System

```typescript
/**
 * Behavioral CSS System
 */
export interface BehavioralCSSSystem {
  /**
   * Apply a behavioral theme to a process
   */
  applyTheme(processId: string, theme: BehavioralTheme): void;

  /**
   * Get the current behavioral theme for a process
   */
  getTheme(processId: string): BehavioralTheme | null;

  /**
   * Create a new behavioral theme
   */
  createTheme(name: string, rules: BehavioralRule[]): BehavioralTheme;

  /**
   * Register a selector
   */
  registerSelector(name: string, matcher: SelectorMatcher): void;

  /**
   * Register a property
   */
  registerProperty(name: string, handler: PropertyHandler): void;

  /**
   * Register middleware
   */
  registerMiddleware(name: string, middleware: Middleware): void;
}

/**
 * Behavioral theme
 */
export interface BehavioralTheme {
  /**
   * Theme name
   */
  name: string;

  /**
   * Behavioral rules
   */
  rules: BehavioralRule[];
}

/**
 * Behavioral rule
 */
export interface BehavioralRule {
  /**
   * Selector
   */
  selector: string;

  /**
   * Properties
   */
  properties: Record<string, any>;

  /**
   * Middleware
   */
  middleware?: string[];
}
```

## Integration with Other Modules

The stProcess.rfr module integrates with other modules in the following ways:

1. **Kernel Reference Frame**: Requests tics from the kernel for execution.
2. **Time Reference Frame**: Uses the time module for archiving and rotation.
3. **Categorical Types**: Uses categorical types for type-safe operations.
4. **Pragma System**: Uses the pragma system for declaring dependencies and resource requirements.
5. **Resource Quota System**: Requests resource quotas based on declared requirements.
6. **Behavioral CSS System**: Applies behavioral themes to processes for organizational behavior.

## Next Steps

1. **Implement Process Priority**: Add priority-based scheduling to the process implementation.
2. **Integrate with Kernel**: Ensure proper integration with the kernel for tic allocation.
3. **Implement Behavioral CSS**: Add support for the behavioral CSS system.
4. **Integrate with Pragma**: Ensure proper integration with the pragma system.
5. **Integrate with Resource Quota System**: Add support for resource quota management.
6. **Create Testing Framework**: Develop a comprehensive testing framework for processes.
7. **Optimize Performance**: Optimize the process implementation for better performance.
8. **Document API**: Create detailed API documentation with usage examples.

## Conclusion

The Process Reference Frame (stProcess.rfr) provides a powerful foundation for process-based programming in the SpiceTime architecture. By extending the Node.js process model with additional capabilities like generator-based execution, state management, event system, hierarchical structure, and priority-based scheduling, we create a flexible and powerful system for defining and executing processes.

The integration with the Behavioral CSS system allows for defining organizational behaviors through a familiar selector-based syntax, creating a powerful, intuitive system for defining behaviors that diffuse naturally through hierarchical structures. The Pragma system provides a mechanism for declaring dependencies, resource requirements, and other metadata, while the Resource Quota System ensures efficient resource allocation.

This implementation serves as the basis for the more advanced tripodic structure that will be implemented later in spice.space.rfr, providing a solid foundation for the SpiceTime architecture.
