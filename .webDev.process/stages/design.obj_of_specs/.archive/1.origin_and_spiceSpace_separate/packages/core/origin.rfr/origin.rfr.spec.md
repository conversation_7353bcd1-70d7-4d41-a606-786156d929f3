# Origin Root Functor Implementation Specification

## Overview

This specification defines the implementation details for the Origin Root Functor in the cat_types package. The Origin Root Functor is the root of all root functors, representing the origin of the SpiceTime architecture. It generates root functors that in turn generate trees of endofunctors.

## Dependencies

```typescript
import { Category } from '../atomic/cat';
import { CatObject } from '../atomic/catObject';
import { Morphism } from '../atomic/morphism';
import { RootFunctor } from '../__types/rfr.type';
import { OriginRootFunctor } from '../__types/origin.rfr.type';
import { BaseRootFunctor } from './rfr.fr';
```

## Class Definition

```typescript
/**
 * Base implementation of the Origin Root Functor
 * 
 * @typeParam T - Type of objects in the target category
 * @typeParam TM - Type of morphisms in the target category
 */
export class BaseOriginRootFunctor<
  T extends CatObject,
  TM extends Morphism<T>
> extends BaseRootFunctor<CatObject, Morphism<CatObject>, T, TM> implements OriginRootFunctor<T, TM> {
  /**
   * Kernel object
   */
  private readonly kernel: CatObject;

  /**
   * Process object
   */
  private readonly process: CatObject;

  /**
   * Time object
   */
  private readonly time: CatObject;

  /**
   * Creates a new Origin Root Functor
   * 
   * @param sourceCategory Source category
   * @param targetCategory Target category
   * @param objectMap Function that maps objects from source to target
   * @param morphismMap Function that maps morphisms from source to target
   * @param kernel Kernel object
   * @param process Process object
   * @param time Time object
   */
  constructor(
    sourceCategory: Category<CatObject, Morphism<CatObject>>,
    targetCategory: Category<T, TM>,
    objectMap: (obj: CatObject) => T,
    morphismMap: (morphism: Morphism<CatObject>) => TM,
    kernel: CatObject,
    process: CatObject,
    time: CatObject
  ) {
    super(sourceCategory, targetCategory, objectMap, morphismMap);
    this.kernel = kernel;
    this.process = process;
    this.time = time;
  }

  /**
   * Creates a root functor
   * 
   * @param targetCategory Target category
   * @param objectMap Function that maps objects to the target category
   * @param morphismMap Function that maps morphisms to the target category
   * @returns A new root functor
   */
  createRootFunctor<
    RT extends CatObject,
    RTM extends Morphism<RT>
  >(
    targetCategory: Category<RT, RTM>,
    objectMap: (obj: CatObject) => RT,
    morphismMap: (morphism: Morphism<CatObject>) => RTM
  ): RootFunctor<CatObject, Morphism<CatObject>, RT, RTM> {
    return new BaseRootFunctor(
      this.sourceCategory,
      targetCategory,
      objectMap,
      morphismMap,
      this
    );
  }

  /**
   * Gets the kernel associated with this origin
   * 
   * @returns The kernel object
   */
  getKernel(): CatObject {
    return this.kernel;
  }

  /**
   * Gets the process associated with this origin
   * 
   * @returns The process object
   */
  getProcess(): CatObject {
    return this.process;
  }

  /**
   * Gets the time associated with this origin
   * 
   * @returns The time object
   */
  getTime(): CatObject {
    return this.time;
  }
}
```

## Utility Function

```typescript
/**
 * Creates an origin root functor
 * 
 * @param targetCategory Target category
 * @param objectMap Function that maps objects to the target category
 * @param morphismMap Function that maps morphisms to the target category
 * @param kernel Kernel object
 * @param process Process object
 * @param time Time object
 * @returns A new origin root functor
 */
export function createOriginRootFunctor<
  T extends CatObject,
  TM extends Morphism<T>
>(
  targetCategory: Category<T, TM>,
  objectMap: (obj: CatObject) => T,
  morphismMap: (morphism: Morphism<CatObject>) => TM,
  kernel: CatObject,
  process: CatObject,
  time: CatObject
): OriginRootFunctor<T, TM> {
  // Create a source category for the origin
  const sourceCategory = new ConcreteCategory<CatObject, Morphism<CatObject>>();
  
  // Add the kernel, process, and time objects to the source category
  sourceCategory.addObject(kernel);
  sourceCategory.addObject(process);
  sourceCategory.addObject(time);
  
  // Create the origin root functor
  return new BaseOriginRootFunctor(
    sourceCategory,
    targetCategory,
    objectMap,
    morphismMap,
    kernel,
    process,
    time
  );
}
```

## Usage Example

```typescript
// Create a target category
const targetCategory = new ConcreteCategory();

// Create kernel, process, and time objects
const kernel = createCatObject({ type: 'kernel' }, 'kernel');
const process = createCatObject({ type: 'process' }, 'process');
const time = createCatObject({ type: 'time' }, 'time');

// Create an origin root functor
const origin = createOriginRootFunctor(
  targetCategory,
  (obj) => createCatObject(obj.value, obj.id.name),
  (morphism) => new BaseMorphism(
    createCatObject(morphism.source.value, morphism.source.id.name),
    createCatObject(morphism.target.value, morphism.target.id.name),
    morphism.apply
  ),
  kernel,
  process,
  time
);

// Create a root functor
const rootFunctor = origin.createRootFunctor(
  targetCategory,
  (obj) => createCatObject(obj.value, obj.id.name),
  (morphism) => new BaseMorphism(
    createCatObject(morphism.source.value, morphism.source.id.name),
    createCatObject(morphism.target.value, morphism.target.id.name),
    morphism.apply
  )
);

// Get the kernel
const k = origin.getKernel();
console.log('Kernel:', k.value);

// Get the process
const p = origin.getProcess();
console.log('Process:', p.value);

// Get the time
const t = origin.getTime();
console.log('Time:', t.value);
```

## File Location

This implementation should be placed in:
```
packages/core/origin.rfr/cat_types.obj/functors/origin.rfr.fr.ts
```

## Related Implementations

- `BaseRootFunctor`: The base implementation of the root functor
- `KernelRootFunctor`: Implementation of the kernel root functor
- `ProcessRootFunctor`: Implementation of the process root functor
- `TimeRootFunctor`: Implementation of the time root functor
