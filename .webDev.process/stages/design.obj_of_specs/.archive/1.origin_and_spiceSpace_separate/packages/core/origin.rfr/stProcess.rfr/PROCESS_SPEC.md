# Process Reference Frame (process.rfr) Specification

## Overview

The Process Reference Frame (process.rfr) provides an extension of the Node.js process model, serving as the foundation for process-based programming in the SpiceTime architecture. It defines processes that operate on types, transforming them through space and time.

Unlike the tripodic process model (which comes later in spice.space), the origin process is a basic extension of the Node.js process, focusing on runtime scripts and CLI integration.

## Core Concepts

### Process as an Extension of Node Process

A process in SpiceTime extends the Node.js process model with additional capabilities:

1. **Enhanced Event System**: Extends the Node.js event system with typed events and event hierarchies.
2. **State Management**: Adds state management capabilities to processes.
3. **Inter-Process Communication**: Provides mechanisms for communication between processes.
4. **Resource Management**: Manages resources used by processes.
5. **CLI Integration**: Provides CLI access to any node/process.

### CLI Integration

The CLI integration allows for:

1. **Process Creation**: Creating new processes through the CLI.
2. **Process Monitoring**: Monitoring the state and events of processes.
3. **Process Control**: Controlling processes (start, stop, pause, resume).
4. **Process Communication**: Sending messages to processes.
5. **Process Inspection**: Inspecting the internal state of processes.

## Architecture

```
process.rfr
├── src                 # Source code
│   ├── atomic          # Atomic processes
│   │   ├── base.ts     # Base process implementation
│   │   ├── node.ts     # Node process extension
│   │   ├── event.ts    # Event system
│   │   └── state.ts    # State management
│   ├── composite       # Composite processes
│   │   ├── parallel.ts # Parallel processes
│   │   ├── serial.ts   # Serial processes
│   │   └── pipeline.ts # Process pipelines
│   ├── cli             # CLI integration
│   │   ├── commands.ts # CLI commands
│   │   ├── parser.ts   # Command parser
│   │   └── runner.ts   # Command runner
│   └── utils           # Utilities
│       ├── ipc.ts      # Inter-process communication
│       ├── resource.ts # Resource management
│       └── logger.ts   # Logging
├── test                # Tests
│   ├── atomic          # Atomic process tests
│   ├── composite       # Composite process tests
│   └── cli             # CLI integration tests
└── docs                # Documentation
    └── spec_of_package # Package specifications
        ├── fundamentals.md # Fundamental concepts
        └── cli_integration.md # CLI integration specification
```

## API

### Base Process

```typescript
/**
 * Base process interface
 */
interface BaseProcess {
  /**
   * Process ID
   */
  id: string;
  
  /**
   * Process name
   */
  name: string;
  
  /**
   * Process state
   */
  state: ProcessState;
  
  /**
   * Start the process
   */
  start(): Promise<void>;
  
  /**
   * Stop the process
   */
  stop(): Promise<void>;
  
  /**
   * Pause the process
   */
  pause(): Promise<void>;
  
  /**
   * Resume the process
   */
  resume(): Promise<void>;
  
  /**
   * Send a message to the process
   */
  send(message: any): Promise<void>;
  
  /**
   * Receive a message from the process
   */
  receive(): Promise<any>;
  
  /**
   * Subscribe to process events
   */
  subscribe(event: string, handler: (data: any) => void): void;
  
  /**
   * Unsubscribe from process events
   */
  unsubscribe(event: string, handler: (data: any) => void): void;
  
  /**
   * Get process information
   */
  info(): ProcessInfo;
}

/**
 * Process state
 */
enum ProcessState {
  CREATED = 'created',
  STARTING = 'starting',
  RUNNING = 'running',
  PAUSING = 'pausing',
  PAUSED = 'paused',
  RESUMING = 'resuming',
  STOPPING = 'stopping',
  STOPPED = 'stopped',
  ERROR = 'error'
}

/**
 * Process information
 */
interface ProcessInfo {
  id: string;
  name: string;
  state: ProcessState;
  startTime?: Date;
  endTime?: Date;
  memory?: {
    rss: number;
    heapTotal: number;
    heapUsed: number;
    external: number;
  };
  cpu?: {
    user: number;
    system: number;
  };
}
```

### Node Process Extension

```typescript
/**
 * Node process extension
 */
interface NodeProcess extends BaseProcess {
  /**
   * Node.js process
   */
  nodeProcess: NodeJS.Process;
  
  /**
   * Environment variables
   */
  env: Record<string, string>;
  
  /**
   * Command line arguments
   */
  args: string[];
  
  /**
   * Working directory
   */
  cwd: string;
  
  /**
   * Process exit code
   */
  exitCode?: number;
  
  /**
   * Set environment variable
   */
  setEnv(key: string, value: string): void;
  
  /**
   * Get environment variable
   */
  getEnv(key: string): string | undefined;
  
  /**
   * Change working directory
   */
  chdir(directory: string): void;
  
  /**
   * Exit the process
   */
  exit(code?: number): never;
}
```

### CLI Integration

```typescript
/**
 * CLI command
 */
interface CliCommand {
  /**
   * Command name
   */
  name: string;
  
  /**
   * Command description
   */
  description: string;
  
  /**
   * Command options
   */
  options: CliOption[];
  
  /**
   * Command arguments
   */
  args: CliArgument[];
  
  /**
   * Execute the command
   */
  execute(options: Record<string, any>, args: any[]): Promise<void>;
}

/**
 * CLI option
 */
interface CliOption {
  /**
   * Option name
   */
  name: string;
  
  /**
   * Option alias
   */
  alias?: string;
  
  /**
   * Option description
   */
  description: string;
  
  /**
   * Option type
   */
  type: 'string' | 'number' | 'boolean' | 'array';
  
  /**
   * Whether the option is required
   */
  required?: boolean;
  
  /**
   * Default value
   */
  default?: any;
}

/**
 * CLI argument
 */
interface CliArgument {
  /**
   * Argument name
   */
  name: string;
  
  /**
   * Argument description
   */
  description: string;
  
  /**
   * Argument type
   */
  type: 'string' | 'number' | 'boolean' | 'array';
  
  /**
   * Whether the argument is required
   */
  required?: boolean;
  
  /**
   * Default value
   */
  default?: any;
}

/**
 * CLI runner
 */
interface CliRunner {
  /**
   * Register a command
   */
  registerCommand(command: CliCommand): void;
  
  /**
   * Parse command line arguments
   */
  parse(args: string[]): {
    command: string;
    options: Record<string, any>;
    args: any[];
  };
  
  /**
   * Execute a command
   */
  execute(command: string, options: Record<string, any>, args: any[]): Promise<void>;
  
  /**
   * Run the CLI
   */
  run(args: string[]): Promise<void>;
}
```

## Implementation

### Base Process Implementation

```typescript
/**
 * Base process implementation
 */
class BaseProcessImpl implements BaseProcess {
  id: string;
  name: string;
  state: ProcessState;
  private eventEmitter: EventEmitter;
  
  constructor(name: string) {
    this.id = generateId();
    this.name = name;
    this.state = ProcessState.CREATED;
    this.eventEmitter = new EventEmitter();
  }
  
  async start(): Promise<void> {
    this.state = ProcessState.STARTING;
    this.emit('starting');
    
    try {
      await this.onStart();
      this.state = ProcessState.RUNNING;
      this.emit('started');
    } catch (error) {
      this.state = ProcessState.ERROR;
      this.emit('error', error);
      throw error;
    }
  }
  
  async stop(): Promise<void> {
    this.state = ProcessState.STOPPING;
    this.emit('stopping');
    
    try {
      await this.onStop();
      this.state = ProcessState.STOPPED;
      this.emit('stopped');
    } catch (error) {
      this.state = ProcessState.ERROR;
      this.emit('error', error);
      throw error;
    }
  }
  
  async pause(): Promise<void> {
    this.state = ProcessState.PAUSING;
    this.emit('pausing');
    
    try {
      await this.onPause();
      this.state = ProcessState.PAUSED;
      this.emit('paused');
    } catch (error) {
      this.state = ProcessState.ERROR;
      this.emit('error', error);
      throw error;
    }
  }
  
  async resume(): Promise<void> {
    this.state = ProcessState.RESUMING;
    this.emit('resuming');
    
    try {
      await this.onResume();
      this.state = ProcessState.RUNNING;
      this.emit('resumed');
    } catch (error) {
      this.state = ProcessState.ERROR;
      this.emit('error', error);
      throw error;
    }
  }
  
  async send(message: any): Promise<void> {
    this.emit('message', message);
  }
  
  async receive(): Promise<any> {
    return new Promise((resolve) => {
      this.once('message', resolve);
    });
  }
  
  subscribe(event: string, handler: (data: any) => void): void {
    this.eventEmitter.on(event, handler);
  }
  
  unsubscribe(event: string, handler: (data: any) => void): void {
    this.eventEmitter.off(event, handler);
  }
  
  info(): ProcessInfo {
    return {
      id: this.id,
      name: this.name,
      state: this.state
    };
  }
  
  protected emit(event: string, data?: any): void {
    this.eventEmitter.emit(event, data);
  }
  
  protected once(event: string, handler: (data: any) => void): void {
    this.eventEmitter.once(event, handler);
  }
  
  protected async onStart(): Promise<void> {
    // Override in subclasses
  }
  
  protected async onStop(): Promise<void> {
    // Override in subclasses
  }
  
  protected async onPause(): Promise<void> {
    // Override in subclasses
  }
  
  protected async onResume(): Promise<void> {
    // Override in subclasses
  }
}
```

### Node Process Extension Implementation

```typescript
/**
 * Node process extension implementation
 */
class NodeProcessImpl extends BaseProcessImpl implements NodeProcess {
  nodeProcess: NodeJS.Process;
  env: Record<string, string>;
  args: string[];
  cwd: string;
  exitCode?: number;
  
  constructor(name: string) {
    super(name);
    this.nodeProcess = process;
    this.env = { ...process.env };
    this.args = process.argv.slice(2);
    this.cwd = process.cwd();
    
    // Handle process events
    process.on('exit', (code) => {
      this.exitCode = code;
      this.state = ProcessState.STOPPED;
      this.emit('exit', code);
    });
    
    process.on('uncaughtException', (error) => {
      this.state = ProcessState.ERROR;
      this.emit('error', error);
    });
    
    process.on('unhandledRejection', (reason) => {
      this.state = ProcessState.ERROR;
      this.emit('unhandledRejection', reason);
    });
    
    process.on('SIGINT', () => {
      this.emit('sigint');
      this.stop().catch((error) => {
        console.error('Error stopping process:', error);
      });
    });
    
    process.on('SIGTERM', () => {
      this.emit('sigterm');
      this.stop().catch((error) => {
        console.error('Error stopping process:', error);
      });
    });
  }
  
  setEnv(key: string, value: string): void {
    this.env[key] = value;
    process.env[key] = value;
  }
  
  getEnv(key: string): string | undefined {
    return this.env[key];
  }
  
  chdir(directory: string): void {
    process.chdir(directory);
    this.cwd = process.cwd();
  }
  
  exit(code?: number): never {
    process.exit(code);
  }
  
  override info(): ProcessInfo {
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    
    return {
      ...super.info(),
      memory: {
        rss: memoryUsage.rss,
        heapTotal: memoryUsage.heapTotal,
        heapUsed: memoryUsage.heapUsed,
        external: memoryUsage.external
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system
      }
    };
  }
}
```

### CLI Integration Implementation

```typescript
/**
 * CLI command implementation
 */
class CliCommandImpl implements CliCommand {
  name: string;
  description: string;
  options: CliOption[];
  args: CliArgument[];
  private handler: (options: Record<string, any>, args: any[]) => Promise<void>;
  
  constructor(
    name: string,
    description: string,
    options: CliOption[],
    args: CliArgument[],
    handler: (options: Record<string, any>, args: any[]) => Promise<void>
  ) {
    this.name = name;
    this.description = description;
    this.options = options;
    this.args = args;
    this.handler = handler;
  }
  
  async execute(options: Record<string, any>, args: any[]): Promise<void> {
    await this.handler(options, args);
  }
}

/**
 * CLI runner implementation
 */
class CliRunnerImpl implements CliRunner {
  private commands: Map<string, CliCommand>;
  
  constructor() {
    this.commands = new Map();
  }
  
  registerCommand(command: CliCommand): void {
    this.commands.set(command.name, command);
  }
  
  parse(args: string[]): {
    command: string;
    options: Record<string, any>;
    args: any[];
  } {
    // Simple parsing logic for demonstration
    const command = args[0] || 'help';
    const options: Record<string, any> = {};
    const positionalArgs: any[] = [];
    
    for (let i = 1; i < args.length; i++) {
      const arg = args[i];
      
      if (arg.startsWith('--')) {
        const option = arg.slice(2);
        const value = args[i + 1] && !args[i + 1].startsWith('-') ? args[++i] : true;
        options[option] = value;
      } else if (arg.startsWith('-')) {
        const option = arg.slice(1);
        const value = args[i + 1] && !args[i + 1].startsWith('-') ? args[++i] : true;
        options[option] = value;
      } else {
        positionalArgs.push(arg);
      }
    }
    
    return {
      command,
      options,
      args: positionalArgs
    };
  }
  
  async execute(command: string, options: Record<string, any>, args: any[]): Promise<void> {
    const cmd = this.commands.get(command);
    
    if (!cmd) {
      throw new Error(`Unknown command: ${command}`);
    }
    
    await cmd.execute(options, args);
  }
  
  async run(args: string[]): Promise<void> {
    const { command, options, args: positionalArgs } = this.parse(args);
    
    try {
      await this.execute(command, options, positionalArgs);
    } catch (error) {
      console.error(`Error executing command ${command}:`, error);
      process.exit(1);
    }
  }
}
```

## CLI Commands

The process.rfr package provides the following CLI commands:

### Process Management

- **create**: Create a new process
- **start**: Start a process
- **stop**: Stop a process
- **pause**: Pause a process
- **resume**: Resume a process
- **list**: List all processes
- **info**: Get information about a process
- **kill**: Kill a process

### Process Communication

- **send**: Send a message to a process
- **receive**: Receive messages from a process
- **subscribe**: Subscribe to process events
- **unsubscribe**: Unsubscribe from process events

### Process Inspection

- **inspect**: Inspect the internal state of a process
- **logs**: View process logs
- **stats**: View process statistics

## Usage Examples

### Creating and Starting a Process

```typescript
import { NodeProcess } from '@spicetime/core/origin.rfr/process.rfr';

// Create a new process
const process = new NodeProcess('my-process');

// Start the process
await process.start();

// Do some work
// ...

// Stop the process
await process.stop();
```

### Using the CLI

```bash
# Create a new process
spicetime process create my-process

# Start the process
spicetime process start my-process

# Get process information
spicetime process info my-process

# Send a message to the process
spicetime process send my-process '{"type":"command","action":"doSomething"}'

# Stop the process
spicetime process stop my-process
```

### Creating a Custom Process

```typescript
import { BaseProcess } from '@spicetime/core/origin.rfr/process.rfr';

class CustomProcess extends BaseProcess {
  private data: any;
  
  constructor(name: string, data: any) {
    super(name);
    this.data = data;
  }
  
  protected async onStart(): Promise<void> {
    console.log(`Starting process ${this.name} with data:`, this.data);
    // Initialize resources
  }
  
  protected async onStop(): Promise<void> {
    console.log(`Stopping process ${this.name}`);
    // Clean up resources
  }
  
  async processData(): Promise<void> {
    console.log(`Processing data in ${this.name}`);
    // Process data
    this.emit('dataProcessed', { result: 'success' });
  }
}

// Create and use the custom process
const process = new CustomProcess('data-processor', { source: 'file.txt' });

// Subscribe to events
process.subscribe('dataProcessed', (data) => {
  console.log('Data processed:', data);
});

// Start the process
await process.start();

// Process data
await process.processData();

// Stop the process
await process.stop();
```

## Integration with Node.js

The process.rfr package integrates with Node.js in the following ways:

1. **Process Events**: Extends the Node.js process events with additional events.
2. **Environment Variables**: Provides access to and manipulation of environment variables.
3. **Command Line Arguments**: Provides access to command line arguments.
4. **Working Directory**: Provides access to and manipulation of the working directory.
5. **Exit Codes**: Provides access to and manipulation of exit codes.
6. **Signals**: Handles process signals (SIGINT, SIGTERM, etc.).
7. **Standard I/O**: Provides access to standard input, output, and error streams.

## Next Steps

1. **Implement the Base Process**: Create the base process implementation.
2. **Implement the Node Process Extension**: Create the Node.js process extension.
3. **Implement the CLI Integration**: Create the CLI integration.
4. **Create Tests**: Create comprehensive tests for the process.rfr package.
5. **Create Documentation**: Create detailed documentation for the process.rfr package.
6. **Integrate with Other Packages**: Ensure that the process.rfr package integrates well with other packages in the SpiceTime architecture.
