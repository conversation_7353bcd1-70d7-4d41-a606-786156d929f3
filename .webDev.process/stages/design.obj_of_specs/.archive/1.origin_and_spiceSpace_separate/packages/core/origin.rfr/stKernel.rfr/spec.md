# SpiceTime Kernel Specification

## Overview

The SpiceTime Kernel is the top-level process in the SpiceTime architecture. It represents the origin node (0,0,0 coordinates) in the SpiceTime space and is responsible for distributing computational resources (tics) to other processes. The kernel acts as the interface between the underlying runtime (JavaScript, Rust/WASM in the future) and the SpiceTime processes.

Each node in the system (including the kernel) acts as a kernel for its children, creating a symmetrical, fractal-like structure. Resources flow from the origin node through the hierarchy, with prioritization happening at each level. The system naturally forms geodesics (optimal paths) for resource distribution based on the current "space metric tensor," while HoloG (Holographic Gravity) directs curvature toward specific goals, independent of geodesic formation.

## Core Responsibilities

1. **Tic Management**: Allocate and distribute computational time slices (tics) to processes
2. **Process Registration**: Maintain a registry of all active processes
3. **Resource Scheduling**: Prioritize and schedule processes based on their needs and priorities
4. **System Metrics**: Provide metrics about system load, uptime, and resource usage
5. **Resource Quota System**: Implement hierarchical quota distribution for efficient resource allocation
6. **API Request Handling**: Process prioritized API requests between nodes
7. **Geodesic Optimization**: Form optimal paths for resource distribution based on the space metric tensor
8. **Curvature Direction**: Use HoloG to direct curvature toward specific goals
9. **Hierarchical Structure**: Maintain a fractal-like structure where each node acts as a kernel for its children

## Architecture

The kernel is designed with Rust-compatible patterns to facilitate future conversion to Rust/WASM:

1. **Immutable by Default**: Properties are immutable unless explicitly marked as mutable
2. **Ownership Model**: Clear ownership of resources with explicit transfers
3. **No Shared Mutable State**: Avoid shared mutable state to prevent race conditions
4. **Error Handling**: Use Result-like patterns for error handling
5. **Memory Management**: Explicit resource allocation and deallocation

## Components

### Kernel Core

The kernel core is responsible for the basic functionality of the kernel:

```typescript
interface KernelCore {
  /** Queue a process for tic allocation */
  queueProcess(processId: string): void;

  /** Distribute tics to queued processes */
  distributeTics(): void;

  /** Get metrics about kernel performance */
  getMetrics(): KernelMetrics;
}
```

### Node Structure

Each node in the system (including the kernel) acts as a kernel for its children:

```typescript
interface NodeInterface {
  /** Unique node identifier */
  id: string;

  /** Parent node ID (if any) */
  parentId: string | null;

  /** Request resource from parent */
  requestResource(type: ResourceType, amount: number, priority: number): Promise<Resource | null>;

  /** Distribute resource to children */
  distributeResource(resource: Resource): void;

  /** Add child node */
  addChild(node: NodeInterface): void;

  /** Remove child node */
  removeChild(nodeId: string): void;

  /** Update priority of child */
  updateChildPriority(childId: string, priority: number): void;

  /** Get metrics about node */
  getMetrics(): KernelMetrics;

  /** Handle API request */
  handleApiRequest(request: ApiRequest): Promise<ApiResponse>;

  /** Send API request to another node */
  sendApiRequest(request: ApiRequest): Promise<ApiResponse>;
}
```

### Resource Quota System

The Resource Quota System manages resource allocation through hierarchical quotas, implementing a multi-layered approach to resource allocation:

```typescript
interface QuotaSystem {
  /** Request a quota for a resource type */
  requestQuota(nodeId: string, resourceType: ResourceType, amount: number, priority?: number): Promise<Quota | null>;

  /** Release a quota */
  releaseQuota(quotaId: string): void;

  /** Adjust quota amount */
  adjustQuota(quotaId: string, newAmount: number): Promise<boolean>;

  /** Get current quota usage */
  getQuotaUsage(quotaId: string): number;

  /** Get available quota for a node and resource type */
  getAvailableQuota(nodeId: string, resourceType: ResourceType): number;

  /** Set quota renewal rate */
  setRenewalRate(nodeId: string, resourceType: ResourceType, rate: number): void;

  /** Set boundary conditions for a quota */
  setBoundaryConditions(nodeId: string, resourceType: ResourceType, boundaries: QuotaBoundaries): void;

  /** Borrow resources from siblings */
  borrowFromSiblings(nodeId: string, resourceType: ResourceType, amount: number): Promise<Quota | null>;

  /** Adjust internal allocation to prioritize critical components */
  adjustInternalAllocation(nodeId: string, priorityMap: Record<string, number>): Promise<boolean>;

  /** Throttle non-essential operations */
  throttleNonEssentialOperations(nodeId: string): Promise<void>;
}
```

### API Request System

The API Request System handles communication between nodes:

```typescript
interface ApiRequestSystem {
  /** Handle an API request */
  handleRequest(request: ApiRequest): Promise<ApiResponse>;

  /** Send an API request */
  sendRequest(request: ApiRequest): Promise<ApiResponse>;

  /** Register a request handler */
  registerHandler(type: string, handler: RequestHandler): void;

  /** Unregister a request handler */
  unregisterHandler(type: string): void;
}
```

## Resource Types

The kernel manages various types of resources, each coming from different specialized providers:

```typescript
enum ResourceType {
  CPU = 'cpu',         // Allocated from the kernel (upscope)
  MEMORY = 'memory',   // Comes from memory management components
  STORAGE = 'storage', // Comes from specialized storage layer components
  NETWORK = 'network', // Comes from network interface components
  TIME = 'time',       // Comes from the time module (for archiving and rotation)
  DISCOVERY = 'discovery' // Discovery services consume CPU resources while providing discovery capabilities
}
```

Each resource type has its own allocation strategy, renewal rate, and constraints.

## Quota Management

Quotas are allocated hierarchically:

1. The kernel holds the total available resources
2. Each level receives quotas from its parent
3. Each node distributes sub-quotas to its children based on:
   - Priority
   - Historical usage patterns
   - Current system state
   - Declared requirements

Quotas have several boundary conditions:

1. **Hard Limits**: Absolute maximum resource allocation
2. **Soft Limits**: Preferred maximum that can be exceeded if necessary
3. **Minimum Guarantees**: Resources that must be provided
4. **Target Utilization**: Optimal resource usage level

When a node reaches its limits, it can:
- Request more resources from its parent
- Borrow resources from siblings
- Adjust internal allocation to prioritize critical components
- Throttle non-essential operations

## API Request Priorities

API requests are prioritized to ensure critical operations get resources first:

```typescript
enum ApiPriority {
  CRITICAL = 0,   // Highest priority
  HIGH = 10,
  NORMAL = 50,
  LOW = 100,
  BACKGROUND = 200 // Lowest priority
}
```

## Kernel Interface

The kernel exposes the following interface:

```typescript
interface KernelInterface {
  /** Request a tic for a process */
  requestTic(processId: string, priority?: number): Promise<TicInfo | null>;

  /** Release a tic */
  releaseTic(ticId: string): void;

  /** Register a process with the kernel */
  registerProcess(process: Process): void;

  /** Unregister a process from the kernel */
  unregisterProcess(processId: string): void;

  /** Get a process by ID */
  getProcessById(processId: string): Process | null;

  /** Request a resource quota */
  requestQuota(nodeId: string, resourceType: ResourceType, amount: number, priority?: number): Promise<Quota | null>;

  /** Release a resource quota */
  releaseQuota(quotaId: string): void;

  /** Handle API request */
  handleApiRequest(request: ApiRequest): Promise<ApiResponse>;

  /** Send API request */
  sendApiRequest(request: ApiRequest): Promise<ApiResponse>;

  /** Get system load */
  getSystemLoad(): number;

  /** Get system uptime */
  getSystemUptime(): number;

  /** Calculate geodesic path for resource distribution */
  calculateGeodesic(source: string, target: string): Path;

  /** Apply HoloG curvature direction */
  applyHoloG(goal: Goal): void;

  /** Get the space metric tensor */
  getMetricTensor(): MetricTensor;

  /** Set the space metric tensor */
  setMetricTensor(tensor: MetricTensor): void;

  /** Borrow resources from siblings */
  borrowFromSiblings(nodeId: string, resourceType: ResourceType, amount: number): Promise<Quota | null>;

  /** Adjust internal allocation to prioritize critical components */
  adjustInternalAllocation(nodeId: string, priorityMap: Record<string, number>): Promise<boolean>;
}
```

## Implementation Guidelines

1. **Separation of Concerns**: Each component should have a single responsibility
2. **Testability**: All components should be easily testable in isolation
3. **Performance**: Minimize overhead, especially in hot paths
4. **Scalability**: Design for potential distributed execution
5. **Extensibility**: Allow for future enhancements without breaking changes

## File Structure

```
src/
├── kernel/
│   ├── index.ts           # Main entry point
│   ├── core.ts            # Kernel core implementation
│   ├── node.ts            # Node implementation
│   ├── quota.ts           # Resource quota system
│   ├── api.ts             # API request system
│   ├── geodesic.ts        # Geodesic calculator
│   ├── holog.ts           # HoloG implementation
│   ├── metric.ts          # Space metric tensor
│   ├── types.ts           # Type definitions
│   └── tests/             # Unit tests
│       ├── core.test.ts
│       ├── node.test.ts
│       ├── quota.test.ts
│       ├── api.test.ts
│       ├── geodesic.test.ts
│       ├── holog.test.ts
│       └── metric.test.ts
├── types/
│   └── index.type.ts      # Public type definitions
└── index.ts               # Public API
```

## Additional Components

### Geodesic Calculator

The Geodesic Calculator computes optimal paths for resource distribution based on the space metric tensor:

```typescript
interface GeodesicCalculator {
  /** Calculate geodesic path between two nodes */
  calculatePath(source: string, target: string): Path;

  /** Get the current metric tensor */
  getMetricTensor(): MetricTensor;

  /** Set the metric tensor */
  setMetricTensor(tensor: MetricTensor): void;

  /** Update the metric tensor based on system state */
  updateMetricTensor(systemState: SystemState): void;
}
```

### HoloG (Holographic Gravity)

HoloG directs curvature toward specific goals, independent of geodesic formation:

```typescript
interface HoloG {
  /** Apply curvature direction toward a goal */
  applyGoal(goal: Goal): void;

  /** Remove a goal */
  removeGoal(goalId: string): void;

  /** Get all active goals */
  getGoals(): Goal[];

  /** Calculate the combined curvature effect */
  calculateCurvature(): CurvatureTensor;

  /** Apply the curvature to the metric tensor */
  applyToMetricTensor(metricTensor: MetricTensor): MetricTensor;
}
```

## Conclusion

The SpiceTime Kernel is the foundation of the SpiceTime architecture, representing the origin node (0,0,0 coordinates) in the SpiceTime space. It provides the core functionality for process management, resource allocation, and system metrics.

By implementing the Resource Quota System, it ensures efficient resource utilization while respecting constraints at each level of the hierarchy. The hierarchical, fractal-like structure allows each node to act as a kernel for its children, creating a symmetrical system where resources flow from the origin node through the hierarchy.

The Geodesic Calculator and HoloG components work together to form optimal paths for resource distribution and direct curvature toward specific goals, creating a dynamic system that adapts to changing conditions and priorities.

By following Rust-compatible patterns, the kernel can be easily converted to Rust/WASM in the future, providing better performance and resource utilization.
