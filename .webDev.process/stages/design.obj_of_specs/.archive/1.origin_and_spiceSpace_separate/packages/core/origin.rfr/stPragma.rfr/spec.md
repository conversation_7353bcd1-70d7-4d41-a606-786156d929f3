# SpiceTime Pragma Specification

## Overview

The `stPragma` module provides a mechanism for declaring dependencies, resource requirements, and other metadata for SpiceTime processes. Pragmas are special directives that are processed at compile time or runtime to provide additional information about a script or module. In the SpiceTime architecture, pragmas are used to declare dependencies, specify resource quotas, define API contracts, and provide other metadata that helps the system optimize resource allocation and service discovery.

The `stPragma` module implements a pragmatic syntax approach to SpiceTime application development, providing an ultra-minimal, declarative approach through specialized file extensions, pragma directives, and a scoping mechanism that eliminates traditional React boilerplate. This allows complex applications to be initialized with just a few lines of JSX, with the build system applying specialized processing based on file extensions.

## Core Functionality

The `stPragma` module provides the following core functionality:

1. **Dependency Declaration**: Declare dependencies and the API functions they use
2. **Resource Requirements**: Specify resource quotas needed by a process
3. **API Contract Definition**: Define the API contract for a module
4. **File Type Association**: Associate file extensions with export types
5. **Metadata Annotation**: Provide additional metadata about a module
6. **Pragma Processing**: Parse and process pragma directives
7. **File Extension Processing**: Apply specialized processing based on file extensions
8. **Implicit Imports**: Automatically import required components based on JSX usage
9. **Section-Based Code Organization**: Organize code into sections using pragma directives
10. **Preset-Based Configuration**: Start applications with intelligent presets that can be customized

## Pragma Syntax

Pragmas are declared using a special syntax at the beginning of a file:

```typescript
// @pragma dependency fs:filesystem [readFile, writeFile, exists]
// @pragma dependency logger:logging [info, error, debug]
// @pragma resource cpu 100
// @pragma resource memory 50MB
// @pragma resource storage 1GB
// @pragma api getData:GET /data/:id
// @pragma api updateData:POST /data/:id
// @pragma metadata version 1.0.0
// @pragma metadata author "John Doe"
// @pragma return
// @pragma section imports
// @pragma section state
// @pragma section effects
```

## Pragmatic File Structure

The minimal SpiceTime application requires just three lines of JSX:

```jsx
// @return

<SpiceTimeApp preset="enterprise">
  <Node id="my-service" preset="web-service" />
</SpiceTimeApp>
```

This simplicity is achieved through:

1. **File Extension Processing**: The `.stApp.jsx` extension triggers specialized babel plugins that handle all the React boilerplate.
2. **Pragma Directives**: The `@return` pragma indicates what should be rendered, eliminating the need for explicit function declarations and return statements.
3. **Implicit Imports**: The babel plugin automatically imports required components based on what's used in the JSX.

## Pragma Types

### Dependency Pragma

The dependency pragma declares a dependency and the API functions it uses:

```typescript
// @pragma dependency <name>:<namespace> [<function1>, <function2>, ...]
```

Example:
```typescript
// @pragma dependency fs:filesystem [readFile, writeFile, exists]
```

### Resource Pragma

The resource pragma specifies a resource quota needed by a process:

```typescript
// @pragma resource <type> <amount>[<unit>]
```

Example:
```typescript
// @pragma resource cpu 100
// @pragma resource memory 50MB
// @pragma resource storage 1GB
```

### API Pragma

The API pragma defines an API endpoint provided by a module:

```typescript
// @pragma api <name>:<method> <path>
```

Example:
```typescript
// @pragma api getData:GET /data/:id
// @pragma api updateData:POST /data/:id
```

### Metadata Pragma

The metadata pragma provides additional information about a module:

```typescript
// @pragma metadata <key> <value>
```

Example:
```typescript
// @pragma metadata version 1.0.0
// @pragma metadata author "John Doe"
```

### File Type Pragma

The file type pragma associates a file extension with an export type:

```typescript
// @pragma filetype <extension> <exportType>
```

Example:
```typescript
// @pragma filetype .process Process
// @pragma filetype .component Component
// @pragma filetype .stApp.jsx SpiceTimeApp
```

### Section Pragma

The section pragma defines a section of code with a specific purpose:

```typescript
// @pragma section <name>
```

Example:
```typescript
// @pragma section imports
import React from 'react';
import { useEffect, useState } from 'react';

// @pragma section state
const [count, setCount] = useState(0);

// @pragma section effects
useEffect(() => {
  console.log('Count changed:', count);
}, [count]);
```

### Return Pragma

The return pragma indicates what should be rendered:

```typescript
// @pragma return
```

Example:
```jsx
// @pragma return
<div>
  <h1>Hello, World!</h1>
  <p>Count: {count}</p>
  <button onClick={() => setCount(count + 1)}>Increment</button>
</div>
```

## Pragma Processing

Pragmas are processed at compile time or runtime to extract metadata and configure the system:

```typescript
interface PragmaProcessor {
  /** Parse pragmas from a file */
  parsePragmas(fileContent: string): Pragma[];

  /** Process dependency pragmas */
  processDependencyPragmas(pragmas: Pragma[]): Dependency[];

  /** Process resource pragmas */
  processResourcePragmas(pragmas: Pragma[]): ResourceRequirement[];

  /** Process API pragmas */
  processApiPragmas(pragmas: Pragma[]): ApiContract[];

  /** Process metadata pragmas */
  processMetadataPragmas(pragmas: Pragma[]): Metadata;

  /** Process file type pragmas */
  processFileTypePragmas(pragmas: Pragma[]): FileTypeAssociation[];

  /** Process section pragmas */
  processSectionPragmas(pragmas: Pragma[]): Section[];

  /** Process return pragma */
  processReturnPragma(pragmas: Pragma[]): ReturnStatement | null;

  /** Process file extension */
  processFileExtension(filePath: string): FileExtensionProcessor | null;
}
```

## Dependency Resolution

The `stPragma` module provides utilities for resolving dependencies based on pragma declarations:

```typescript
interface DependencyResolver {
  /** Resolve dependencies for a module */
  resolveDependencies(dependencies: Dependency[]): Promise<Record<string, any>>;

  /** Discover alternative providers for a namespace */
  discoverProviders(namespace: string): Promise<ServiceProvider[]>;

  /** Register a service provider */
  registerProvider(provider: ServiceProvider): void;

  /** Unregister a service provider */
  unregisterProvider(providerId: string): void;
}
```

## Resource Requirement Resolution

The `stPragma` module provides utilities for resolving resource requirements based on pragma declarations:

```typescript
interface ResourceResolver {
  /** Resolve resource requirements for a module */
  resolveResourceRequirements(requirements: ResourceRequirement[]): Promise<Record<ResourceType, Quota>>;

  /** Request quotas for resource requirements */
  requestQuotas(processId: string, requirements: ResourceRequirement[]): Promise<Record<ResourceType, Quota>>;

  /** Release quotas */
  releaseQuotas(quotas: Quota[]): Promise<void>;
}
```

## API Contract Resolution

The `stPragma` module provides utilities for resolving API contracts based on pragma declarations:

```typescript
interface ApiContractResolver {
  /** Resolve API contracts for a module */
  resolveApiContracts(contracts: ApiContract[]): Promise<Record<string, ApiEndpoint>>;

  /** Register API endpoints */
  registerApiEndpoints(processId: string, endpoints: ApiEndpoint[]): Promise<void>;

  /** Unregister API endpoints */
  unregisterApiEndpoints(endpoints: ApiEndpoint[]): Promise<void>;
}
```

## Integration with Process Module

The `stPragma` module integrates with the Process module to provide dependency injection and resource allocation:

```typescript
function createProcessWithPragmas(filePath: string): Promise<Process> {
  // Read file content
  const fileContent = fs.readFileSync(filePath, 'utf-8');

  // Parse pragmas
  const pragmas = pragmaProcessor.parsePragmas(fileContent);

  // Process pragmas
  const dependencies = pragmaProcessor.processDependencyPragmas(pragmas);
  const resourceRequirements = pragmaProcessor.processResourcePragmas(pragmas);
  const apiContracts = pragmaProcessor.processApiPragmas(pragmas);
  const metadata = pragmaProcessor.processMetadataPragmas(pragmas);

  // Resolve dependencies
  const resolvedDependencies = await dependencyResolver.resolveDependencies(dependencies);

  // Create process with resolved dependencies
  const process = createProcess(
    (deps) => {
      // Import the module with dependencies injected
      const moduleExports = require(filePath);

      // Return the process generator
      return moduleExports.default(deps);
    },
    resolvedDependencies
  );

  // Request quotas for resource requirements
  const quotas = await resourceResolver.requestQuotas(process.id, resourceRequirements);

  // Register API endpoints
  const endpoints = await apiContractResolver.resolveApiContracts(apiContracts);
  await apiContractResolver.registerApiEndpoints(process.id, Object.values(endpoints));

  // Set metadata on process
  process.metadata = metadata;

  return process;
}
```

## File Structure

```
src/
├── pragma.ts           # Pragma parsing and processing
├── dependency.ts       # Dependency resolution
├── resource.ts         # Resource requirement resolution
├── api.ts              # API contract resolution
├── metadata.ts         # Metadata processing
├── filetype.ts         # File type association
├── section.ts          # Section processing
├── return.ts           # Return statement processing
├── extension/          # File extension processors
│   ├── stApp.jsx.ts    # SpiceTime App processor
│   ├── stComponent.jsx.ts # SpiceTime Component processor
│   ├── stProcess.ts    # SpiceTime Process processor
│   └── index.ts       # Extension processor exports
├── babel/              # Babel plugins
│   ├── pragma.ts       # Pragma plugin
│   ├── section.ts      # Section plugin
│   ├── return.ts       # Return plugin
│   └── index.ts       # Babel plugin exports
├── types/
│   └── index.type.ts   # Type definitions
├── tests/
│   ├── pragma.test.ts
│   ├── dependency.test.ts
│   ├── resource.test.ts
│   ├── api.test.ts
│   ├── metadata.test.ts
│   ├── filetype.test.ts
│   ├── section.test.ts
│   ├── return.test.ts
│   ├── extension.test.ts
│   └── babel.test.ts
└── index.ts            # Public API
```

## Type Definitions

```typescript
interface Pragma {
  /** Pragma type */
  type: 'dependency' | 'resource' | 'api' | 'metadata' | 'filetype' | 'section' | 'return';

  /** Pragma parameters */
  params: any;
}

interface Dependency {
  /** Dependency name */
  name: string;

  /** Dependency namespace */
  namespace: string;

  /** API functions used */
  apiFunctions: string[];
}

interface ResourceRequirement {
  /** Resource type */
  type: ResourceType;

  /** Required amount */
  amount: number;

  /** Unit (if applicable) */
  unit?: string;
}

interface ApiContract {
  /** API name */
  name: string;

  /** HTTP method */
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

  /** API path */
  path: string;
}

interface ApiEndpoint {
  /** API name */
  name: string;

  /** HTTP method */
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

  /** API path */
  path: string;

  /** Handler function */
  handler: (req: any, res: any) => Promise<void>;
}

interface Metadata {
  /** Metadata key-value pairs */
  [key: string]: any;
}

interface FileTypeAssociation {
  /** File extension */
  extension: string;

  /** Export type */
  exportType: string;
}

interface Section {
  /** Section name */
  name: string;

  /** Section content */
  content: string;

  /** Start line */
  startLine: number;

  /** End line */
  endLine: number;
}

interface ReturnStatement {
  /** Return content */
  content: string;

  /** Start line */
  startLine: number;

  /** End line */
  endLine: number;
}

interface FileExtensionProcessor {
  /** File extension */
  extension: string;

  /** Process file content */
  process(filePath: string, content: string): string;

  /** Get babel plugins */
  getBabelPlugins(): any[];
}

interface ServiceProvider {
  /** Unique provider identifier */
  id: string;

  /** Provider name */
  name: string;

  /** Namespace provided */
  namespace: string;

  /** API functions provided */
  apiFunctions: string[];

  /** Performance metrics */
  performanceMetrics: {
    /** Average response time */
    averageResponseTime: number;

    /** Availability (0-1) */
    availability: number;

    /** Error rate (0-1) */
    errorRate: number;

    /** Current load (0-1) */
    currentLoad: number;
  };

  /** Current price */
  price: number;
}
```

## Integration with cat_types

The `stPragma` module integrates with the `cat_types` package to provide type generation and pragma processing:

```typescript
interface CatTypeProvider {
  /** Generate types from pragmas */
  generateTypes(pragmas: Pragma[]): TypeDefinition[];

  /** Generate categorical structure from types */
  generateCategoryStructure(types: TypeDefinition[]): CategoryStructure;

  /** Register types with the pragma system */
  registerTypes(types: TypeDefinition[]): void;

  /** Get type for a pragma */
  getTypeForPragma(pragma: Pragma): TypeDefinition | null;
}

interface TypeDefinition {
  /** Type name */
  name: string;

  /** Type category */
  category?: string;

  /** Type variant */
  variant?: string;

  /** Type role */
  role?: string;

  /** Pragma type */
  pragmaType?: string;

  /** Is default type */
  isDefault?: boolean;

  /** Type parameters */
  typeParameters?: string[];

  /** Sequence number */
  sequenceNumber?: number;
}

interface CategoryStructure {
  /** Objects in the category */
  objects: string[];

  /** Morphisms in the category */
  morphisms: Record<string, {
    domain: string;
    codomain: string;
  }>;

  /** Functors in the category */
  functors: Record<string, {
    sourceCategory: string;
    targetCategory: string;
    objectMapping: Record<string, string>;
    morphismMapping: Record<string, string>;
  }>;
}
```

## Implementation Guidelines

1. **Performance**: Pragma parsing should be efficient, especially for large files
2. **Caching**: Results of pragma processing should be cached when possible
3. **Error Handling**: Clear error messages for invalid pragma syntax
4. **Extensibility**: Support for custom pragma types
5. **Integration**: Seamless integration with the Process and Kernel modules
6. **Minimal Boilerplate**: Eliminate traditional React boilerplate through pragmatic syntax
7. **Declarative Configuration**: Configure applications through declarative JSX rather than imperative code
8. **Visual Customization**: Support customization through visual builders rather than code modification
9. **Type Safety**: Ensure type safety through integration with the cat_types package

## Conclusion

The `stPragma` module provides a powerful mechanism for declaring dependencies, resource requirements, and other metadata for SpiceTime processes. By processing these declarations at compile time or runtime, the system can optimize resource allocation, facilitate service discovery, and provide better developer experience.

The pragmatic syntax approach enables an ultra-minimal, declarative approach to SpiceTime application development through specialized file extensions, pragma directives, and a scoping mechanism that eliminates traditional React boilerplate. This allows complex applications to be initialized with just a few lines of JSX, with the build system applying specialized processing based on file extensions.

The integration with the cat_types package provides type generation and pragma processing, ensuring type safety and enabling the creation of a universal type system that bridges different programming languages and platforms.

The integration with the Process and Kernel modules ensures that processes receive the resources and dependencies they need to function correctly, while the behavioral CSS system allows for defining organizational behaviors through a familiar selector-based syntax.
