# SpiceTime Core Package API

## Overview

This document provides the API specification for the SpiceTime core package. The core package includes the `createSpiceTimeApp` function, the categorical framework, the process space, and other core components that form the foundation of the SpiceTime system.

## createSpiceTimeApp

### Function Signature

```typescript
function createSpiceTimeApp<Config extends SpiceTimeConfig>(
  config: Config
): SpiceTimeApp<Config>;
```

### Parameters

- `config`: Configuration object for the SpiceTime application

### Returns

- `SpiceTimeApp<Config>`: SpiceTime application instance

### Example

```typescript
const app = createSpiceTimeApp({
  name: 'MySpiceTimeApp',
  version: '1.0.0',
  
  categorical: {
    // Categorical framework configuration
  },
  
  process: {
    // Process space configuration
  },
  
  kernel: {
    // Kernel configuration
  },
  
  plugins: [
    // Plugin configuration
  ]
});

// Start the application
await app.start();

// Use the application
const category = app.createCategory({
  // Category configuration
});

// Stop the application
await app.stop();
```

## SpiceTimeApp

### Interface

```typescript
interface SpiceTimeApp<Config extends SpiceTimeConfig> {
  // Application identity
  readonly name: string;
  readonly version: string;
  
  // Application lifecycle
  start(): Promise<void>;
  stop(): Promise<void>;
  
  // Categorical framework
  createCategory<Obj, Mor>(config: CategoryConfig<Obj, Mor>): CategoryId;
  getCategory<Obj, Mor>(id: CategoryId): Category<Obj, Mor>;
  createFunctor<SrcObj, SrcMor, TgtObj, TgtMor>(
    config: FunctorConfig<SrcObj, SrcMor, TgtObj, TgtMor>
  ): FunctorId;
  getFunctor<SrcObj, SrcMor, TgtObj, TgtMor>(
    id: FunctorId
  ): Functor<SrcObj, SrcMor, TgtObj, TgtMor>;
  createNaturalTransformation<SrcObj, SrcMor, TgtObj, TgtMor>(
    config: NaturalTransformationConfig<SrcObj, SrcMor, TgtObj, TgtMor>
  ): NaturalTransformationId;
  getNaturalTransformation<SrcObj, SrcMor, TgtObj, TgtMor>(
    id: NaturalTransformationId
  ): NaturalTransformation<SrcObj, SrcMor, TgtObj, TgtMor>;
  
  // Process space
  createProcess<P extends Process>(config: ProcessConfig<P>): ProcessId;
  getProcess<P extends Process>(id: ProcessId): P;
  createChannel<E extends Event>(config: ChannelConfig<E>): ChannelId;
  getChannel<E extends Event>(id: ChannelId): Channel<E>;
  emitEvent<E extends Event>(channelId: ChannelId, event: E): void;
  
  // Kernel interface
  getKernel(): KernelBridge;
  
  // Plugin management
  registerPlugin(plugin: Plugin): void;
  
  // Configuration access
  getConfig(): Config;
}
```

## Categorical Framework

### CategoryTheory

```typescript
interface CategoryTheory {
  // Category management
  createCategory<Obj, Mor>(config: CategoryConfig<Obj, Mor>): CategoryId;
  getCategory<Obj, Mor>(id: CategoryId): Category<Obj, Mor>;
  
  // Functor management
  createFunctor<SrcObj, SrcMor, TgtObj, TgtMor>(
    config: FunctorConfig<SrcObj, SrcMor, TgtObj, TgtMor>
  ): FunctorId;
  getFunctor<SrcObj, SrcMor, TgtObj, TgtMor>(
    id: FunctorId
  ): Functor<SrcObj, SrcMor, TgtObj, TgtMor>;
  
  // Natural transformation management
  createNaturalTransformation<SrcObj, SrcMor, TgtObj, TgtMor>(
    config: NaturalTransformationConfig<SrcObj, SrcMor, TgtObj, TgtMor>
  ): NaturalTransformationId;
  getNaturalTransformation<SrcObj, SrcMor, TgtObj, TgtMor>(
    id: NaturalTransformationId
  ): NaturalTransformation<SrcObj, SrcMor, TgtObj, TgtMor>;
  
  // Categorical operations
  compose<A, B, C>(f: Morphism<A, B>, g: Morphism<B, C>): Morphism<A, C>;
  identity<A>(): Morphism<A, A>;
  applyFunctor<F, A, B>(functor: F, morphism: Morphism<A, B>): Morphism<F<A>, F<B>>;
  applyNaturalTransformation<F, G, A>(
    naturalTransformation: NaturalTransformation<F, G>,
    object: A
  ): Morphism<F<A>, G<A>>;
  
  // Verification
  verifyCategory<Obj, Mor>(category: Category<Obj, Mor>): VerificationResult;
  verifyFunctor<SrcObj, SrcMor, TgtObj, TgtMor>(
    functor: Functor<SrcObj, SrcMor, TgtObj, TgtMor>
  ): VerificationResult;
  verifyNaturalTransformation<SrcObj, SrcMor, TgtObj, TgtMor>(
    naturalTransformation: NaturalTransformation<SrcObj, SrcMor, TgtObj, TgtMor>
  ): VerificationResult;
}
```

### Category

```typescript
interface Category<Obj, Mor> {
  // Category identity
  id: CategoryId;
  name: string;
  description: string;
  
  // Objects and morphisms
  objects: Set<Obj>;
  morphisms: Map<[Obj, Obj], Set<Mor>>;
  
  // Category operations
  identity(obj: Obj): Mor;
  compose(f: Mor, g: Mor): Mor;
  source(mor: Mor): Obj;
  target(mor: Mor): Obj;
  getMorphisms(source: Obj, target: Obj): Set<Mor>;
  addObject(obj: Obj): void;
  addMorphism(source: Obj, target: Obj, mor: Mor): void;
}
```

### Functor

```typescript
interface Functor<SrcObj, SrcMor, TgtObj, TgtMor> {
  // Functor identity
  id: FunctorId;
  name: string;
  description: string;
  
  // Source and target categories
  sourceCategory: CategoryId;
  targetCategory: CategoryId;
  
  // Functor operations
  mapObject(obj: SrcObj): TgtObj;
  mapMorphism(mor: SrcMor): TgtMor;
  applyObject(obj: SrcObj): TgtObj;
  applyMorphism(mor: SrcMor): TgtMor;
}
```

### NaturalTransformation

```typescript
interface NaturalTransformation<SrcObj, SrcMor, TgtObj, TgtMor> {
  // Natural transformation identity
  id: NaturalTransformationId;
  name: string;
  description: string;
  
  // Source and target functors
  sourceFunctor: FunctorId;
  targetFunctor: FunctorId;
  
  // Natural transformation operations
  component(obj: SrcObj): TgtMor;
  apply(obj: SrcObj): TgtMor;
}
```

## Process Space

### ProcessSpace

```typescript
interface ProcessSpace<State extends Record<string, any>> {
  // Process management
  createProcess<P extends Process>(config: ProcessConfig<P>): ProcessId;
  getProcess<P extends Process>(id: ProcessId): P;
  updateProcess<P extends Process>(id: ProcessId, update: Partial<P>): void;
  deleteProcess(id: ProcessId): void;
  
  // Channel management
  createChannel<E extends Event>(config: ChannelConfig<E>): ChannelId;
  getChannel<E extends Event>(id: ChannelId): Channel<E>;
  updateChannel<E extends Event>(id: ChannelId, update: Partial<Channel<E>>): void;
  deleteChannel(id: ChannelId): void;
  
  // Event handling
  emitEvent<E extends Event>(channelId: ChannelId, event: E): void;
  subscribeToChannel<E extends Event>(channelId: ChannelId, handler: EventHandler<E>): Subscription;
  
  // Transformation management
  createTransformation<S, T>(config: TransformationConfig<S, T>): TransformationId;
  getTransformation<S, T>(id: TransformationId): Transformation<S, T>;
  applyTransformation<S, T>(id: TransformationId, source: S): T;
  
  // Process space operations
  getState(): State;
  setState(update: Partial<State>): void;
  
  // Simulation
  step(): void;
  run(steps: number): void;
  pause(): void;
  reset(): void;
  
  // Observation
  observe<T>(selector: (state: State) => T): Observable<T>;
  query<T>(selector: (state: State) => T): T;
}
```

### Process

```typescript
interface Process {
  // Process identity
  id: ProcessId;
  type: string;
  
  // Process state
  state: Record<string, any>;
  
  // Process connections
  inputs: ChannelId[];
  outputs: ChannelId[];
  
  // Process metadata
  metadata: Record<string, any>;
}
```

### Channel

```typescript
interface Channel<E extends Event> {
  // Channel identity
  id: ChannelId;
  type: string;
  
  // Channel connections
  source: ProcessId | null;
  target: ProcessId | null;
  
  // Channel operations
  filter: (event: E) => boolean;
  transform: (event: E) => E;
  
  // Channel metadata
  permissions: Permission[];
  metadata: Record<string, any>;
}
```

### Event

```typescript
interface Event {
  // Event identity
  type: string;
  
  // Event data
  payload: any;
  
  // Event metadata
  metadata: Record<string, any>;
  timestamp: number;
}
```

## Kernel Interface

### KernelBridge

```typescript
interface KernelBridge {
  // Kernel operations
  start(): Promise<void>;
  stop(): Promise<void>;
  
  // Process operations
  createProcess(config: ProcessConfig): Promise<string>;
  terminateProcess(id: string): Promise<void>;
  pauseProcess(id: string): Promise<void>;
  resumeProcess(id: string): Promise<void>;
  
  // Event operations
  emitEvent(event: Event): Promise<void>;
  onEvent(eventType: string, handler: (event: Event) => void): Subscription;
  
  // Resource operations
  allocateResource(processId: string, resource: ResourceRequest): Promise<ResourceAllocation>;
  releaseResource(allocationId: string): Promise<void>;
  getResourceUsage(): Promise<ResourceUsage>;
  
  // Tree operations
  getProcessTree(): Promise<ProcessTree>;
  getProcessPath(processId: string): Promise<string[]>;
  
  // Middleware operations
  registerMiddleware(chainId: string, middleware: MiddlewareConfig): Promise<string>;
  unregisterMiddleware(chainId: string, middlewareId: string): Promise<void>;
}
```

## Configuration

### SpiceTimeConfig

```typescript
interface SpiceTimeConfig {
  // Application identity
  name: string;
  version: string;
  
  // Categorical framework configuration
  categorical?: {
    // Initial categories
    categories?: CategoryConfig[];
    
    // Initial functors
    functors?: FunctorConfig[];
    
    // Initial natural transformations
    naturalTransformations?: NaturalTransformationConfig[];
  };
  
  // Process space configuration
  process?: {
    // Initial processes
    processes?: ProcessConfig[];
    
    // Initial channels
    channels?: ChannelConfig[];
    
    // Initial transformations
    transformations?: TransformationConfig[];
    
    // Initial state
    initialState?: Record<string, any>;
  };
  
  // Kernel configuration
  kernel?: {
    // Scheduler configuration
    scheduler?: {
      policy?: 'round-robin' | 'priority' | 'fair-share' | 'custom';
      timeSlice?: number;
      customAlgorithm?: string;
    };
    
    // Resource configuration
    resources?: {
      type: string;
      capacity: number;
      constraints?: {
        type: string;
        parameters: any[];
      }[];
    }[];
    
    // Middleware configuration
    middleware?: {
      id: string;
      chainId: string;
      handler: (event: Event) => Event | Promise<Event>;
    }[];
  };
  
  // Plugin configuration
  plugins?: {
    id: string;
    name: string;
    version: string;
    initialize: (app: SpiceTimeApp<any>) => void;
  }[];
}
```

## Plugin System

### Plugin

```typescript
interface Plugin {
  // Plugin identity
  id: string;
  name: string;
  version: string;
  
  // Plugin initialization
  initialize: (app: SpiceTimeApp<any>) => void;
}
```

## Error Handling

### SpiceTimeError

```typescript
class SpiceTimeError extends Error {
  // Error identity
  code: string;
  
  // Error context
  context: Record<string, any>;
  
  // Error cause
  cause?: Error;
  
  // Constructor
  constructor(message: string, code: string, context?: Record<string, any>, cause?: Error);
}
```

## Utility Types

### Subscription

```typescript
interface Subscription {
  // Subscription operations
  unsubscribe(): void;
}
```

### Observable

```typescript
interface Observable<T> {
  // Observable operations
  subscribe(observer: Observer<T>): Subscription;
  map<U>(mapper: (value: T) => U): Observable<U>;
  filter(predicate: (value: T) => boolean): Observable<T>;
  reduce<U>(reducer: (accumulator: U, value: T) => U, initialValue: U): Observable<U>;
}
```

### Observer

```typescript
interface Observer<T> {
  // Observer operations
  next: (value: T) => void;
  error?: (error: Error) => void;
  complete?: () => void;
}
```
