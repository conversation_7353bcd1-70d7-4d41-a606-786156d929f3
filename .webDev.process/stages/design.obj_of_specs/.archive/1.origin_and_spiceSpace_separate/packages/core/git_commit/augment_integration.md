# Augment Git Commit Integration Guide

This document outlines how Augment should integrate with the git_commit utility to provide an interactive commit workflow.

## 1. Workflow Implementation

### 1.1 Triggering the Workflow

When a user types a command like:
- "Prepare commits for my staged changes"
- "Help me commit my changes"
- "Create commits from my staged files"

Augment should recognize this as a request to use the git_commit utility.

### 1.2 Generating Suggestions

Augment should:

1. Call `generateCommitSuggestions()` from the git_commit utility
2. Format the results in a user-friendly way
3. Present them to the user for review

Example code:
```typescript
import { generateCommitSuggestions } from '@sta/git-smart-commit';

// When user requests commit assistance
function handleCommitRequest() {
  try {
    const suggestions = generateCommitSuggestions();
    
    if (suggestions.length === 0) {
      return "No staged changes found. Please stage some changes first with 'git add'.";
    }
    
    // Format suggestions for display
    let response = "I've analyzed your staged changes and suggest the following commits:\n\n";
    
    suggestions.forEach((suggestion, index) => {
      const commitType = suggestion.scope ? 
        `${suggestion.type}(${suggestion.scope})` : 
        suggestion.type;
        
      response += `${index + 1}. ${commitType}: ${suggestion.message}\n`;
      
      suggestion.files.forEach(file => {
        response += `   - ${file.path}\n`;
      });
      
      response += '\n';
    });
    
    response += "Would you like to modify these suggestions or proceed with these commits?";
    
    return response;
  } catch (error) {
    return `Error generating commit suggestions: ${error.message}`;
  }
}
```

### 1.3 Handling User Curation

Augment should parse user responses for curation commands like:
- "Move file X to commit Y"
- "Change commit 1 message to Z"
- "Add a new commit for file A"
- "Remove commit 2"

Example code:
```typescript
import { curateCommitSuggestions } from '@sta/git-smart-commit';

// When user wants to move a file between commits
function handleMoveFile(suggestions, filePath, fromCommitId, toCommitId) {
  try {
    const updatedSuggestions = curateCommitSuggestions(
      suggestions,
      'move-file',
      { filePath, fromCommitId, toCommitId }
    );
    
    // Format and display updated suggestions
    // ...
    
    return formattedResponse;
  } catch (error) {
    return `Error updating commits: ${error.message}`;
  }
}
```

### 1.4 Executing Commits

When the user approves the commit plan, Augment should:

1. Call `executeCommits()` with the approved suggestions
2. Report the results back to the user

Example code:
```typescript
import { executeCommits } from '@sta/git-smart-commit';

// When user approves the commit plan
function handleCommitExecution(approvedSuggestions) {
  try {
    const results = executeCommits(approvedSuggestions);
    
    // Format results for display
    let response = "Commit results:\n\n";
    
    results.forEach((result, index) => {
      const suggestion = approvedSuggestions[index];
      const commitType = suggestion.scope ? 
        `${suggestion.type}(${suggestion.scope})` : 
        suggestion.type;
        
      if (result.success) {
        response += `✅ ${commitType}: ${suggestion.message}`;
        if (result.commitId) {
          response += ` (${result.commitId})`;
        }
      } else {
        response += `❌ Failed to commit "${commitType}: ${suggestion.message}"`;
        response += `\n   Error: ${result.message}`;
      }
      
      response += '\n';
    });
    
    return response;
  } catch (error) {
    return `Error executing commits: ${error.message}`;
  }
}
```

## 2. Natural Language Processing

Augment should recognize various ways users might express curation commands:

| User Input | Interpretation |
|------------|----------------|
| "Move file.js from commit 1 to commit 2" | Move file between commits |
| "Change commit 2 message to 'fix: resolve bug'" | Update commit message |
| "Make commit 1 a feat instead of fix" | Change commit type |
| "Add a new commit for utils.js" | Create new commit |
| "Remove the second commit" | Delete a commit |
| "Proceed with these commits" | Execute commits |

## 3. Error Handling

Augment should handle common errors gracefully:

- No staged changes
- Git repository issues
- Permission problems
- Invalid curation commands

For each error, provide clear guidance on how to resolve the issue.

## 4. Example Conversation Flow

```
User: Help me commit my changes

Augment: I've analyzed your staged changes and suggest the following commits:

1. feat(auth): implement JWT authentication
   - src/auth/jwt.ts
   - src/auth/middleware.ts

2. fix(ui): correct button alignment on mobile
   - src/components/Button.tsx

Would you like to modify these suggestions or proceed with these commits?

User: Move src/auth/middleware.ts to a separate commit with message "feat(api): add auth middleware"

Augment: Updated commit plan:

1. feat(auth): implement JWT authentication
   - src/auth/jwt.ts

2. feat(api): add auth middleware
   - src/auth/middleware.ts

3. fix(ui): correct button alignment on mobile
   - src/components/Button.tsx

Ready to proceed with these commits?

User: Yes, proceed with these commits

Augment: Commit results:

✅ feat(auth): implement JWT authentication (a1b2c3d)
✅ feat(api): add auth middleware (e4f5g6h)
✅ fix(ui): correct button alignment on mobile (i7j8k9l)
```