# SpiceTime Linguistics Package Examples

## Overview

This document provides examples of using the SpiceTime linguistics package in different scenarios. These examples demonstrate the key features of the package and how they can be used to solve common problems.

## Basic Usage

### Creating a Linguistic System

```typescript
import { createLinguisticSystem } from '@spicetime/linguistics';

// Create a linguistic system with default configuration
const linguistics = await createLinguisticSystem();

// Create a linguistic system with custom configuration
const customLinguistics = await createLinguisticSystem({
  environment: {
    tier: 'standard'
  },
  features: {
    'advanced-parsing': true,
    'jit-compilation': true
  },
  parser: {
    maxSyntaxDepth: 100,
    supportedFeatures: ['all']
  },
  evaluator: {
    maxCallStackDepth: 100,
    optimizationLevel: 'standard',
    jit: true
  }
});
```

### Creating Terms

```typescript
import { createLinguisticSystem, TermType } from '@spicetime/linguistics';

const linguistics = await createLinguisticSystem();

// Get built-in types
const numberType = linguistics.getType('number');
const stringType = linguistics.getType('string');

// Create a value term
const valueTerm = linguistics.createTerm(
  'pi',
  TermType.VALUE,
  [],
  numberType,
  (context, args) => Math.PI
);

// Create a function term
const addTerm = linguistics.createTerm(
  'add',
  TermType.FUNCTION,
  [
    { name: 'a', type: numberType, isOptional: false, isRest: false },
    { name: 'b', type: numberType, isOptional: false, isRest: false }
  ],
  numberType,
  (context, args) => args[0] + args[1]
);

// Create an operator term
const multiplyTerm = linguistics.createTerm(
  'multiply',
  TermType.OPERATOR,
  [
    { name: 'a', type: numberType, isOptional: false, isRest: false },
    { name: 'b', type: numberType, isOptional: false, isRest: false }
  ],
  numberType,
  (context, args) => args[0] * args[1]
);

// Create a composition term
const addAndMultiplyTerm = addTerm.compose(multiplyTerm);

// Create a lambda term
const lambdaTerm = linguistics.createTerm(
  'lambda',
  TermType.LAMBDA,
  [
    { name: 'x', type: numberType, isOptional: false, isRest: false }
  ],
  numberType,
  (context, args) => args[0] * 2
);

// Create a macro term
const macroTerm = linguistics.createTerm(
  'macro',
  TermType.MACRO,
  [
    { name: 'x', type: numberType, isOptional: false, isRest: false }
  ],
  numberType,
  (context, args) => {
    // Create a new term based on the argument
    return linguistics.createTerm(
      'double',
      TermType.FUNCTION,
      [
        { name: 'y', type: numberType, isOptional: false, isRest: false }
      ],
      numberType,
      (innerContext, innerArgs) => innerArgs[0] * 2
    );
  }
);
```

### Creating Scopes

```typescript
import { createLinguisticSystem } from '@spicetime/linguistics';

const linguistics = await createLinguisticSystem();

// Create a root scope
const rootScope = linguistics.createScope('root');

// Create a child scope
const childScope = linguistics.createScope('child', rootScope.id);

// Define terms in scopes
linguistics.defineTerm(rootScope.id, 'pi', valueTerm);
linguistics.defineTerm(rootScope.id, 'add', addTerm);
linguistics.defineTerm(childScope.id, 'multiply', multiplyTerm);

// Lookup terms in scopes
const piTerm = linguistics.lookupTerm(rootScope.id, 'pi');
const addTermFromRoot = linguistics.lookupTerm(rootScope.id, 'add');
const multiplyTermFromChild = linguistics.lookupTerm(childScope.id, 'multiply');
const addTermFromChild = linguistics.lookupTerm(childScope.id, 'add'); // Inherited from parent
```

### Creating Trees

```typescript
import { createLinguisticSystem } from '@spicetime/linguistics';

const linguistics = await createLinguisticSystem();

// Create a tree
const tree = linguistics.createTree('mathTree');

// Get the root scope of the tree
const rootScope = tree.root;

// Create a child scope in the tree
const algebraScope = tree.createScope('algebra', rootScope);
const geometryScope = tree.createScope('geometry', rootScope);
const linearAlgebraScope = tree.createScope('linearAlgebra', algebraScope);

// Find a scope in the tree
const foundScope = tree.findScope(linearAlgebraScope.id);

// Traverse the tree
tree.traverse({
  visitScope: (scope) => {
    console.log(`Visiting scope: ${scope.name}`);
  },
  visitTerm: (term, scope) => {
    console.log(`Visiting term: ${term.name} in scope: ${scope.name}`);
  }
});

// Analyze the tree
const analysis = tree.analyze();
console.log(`Scope count: ${analysis.scopeCount}`);
console.log(`Term count: ${analysis.termCount}`);
console.log(`Max depth: ${analysis.maxDepth}`);
console.log(`Max breadth: ${analysis.maxBreadth}`);
console.log(`Complexity: ${analysis.complexity}`);
```

### Creating Forests

```typescript
import { createLinguisticSystem, CrossLinkType } from '@spicetime/linguistics';

const linguistics = await createLinguisticSystem();

// Create a forest
const forest = linguistics.createForest('mathForest');

// Create trees
const algebraTree = linguistics.createTree('algebraTree');
const geometryTree = linguistics.createTree('geometryTree');

// Add trees to the forest
forest.addTree(algebraTree);
forest.addTree(geometryTree);

// Create cross-links between trees
const algebraRoot = algebraTree.root;
const geometryRoot = geometryTree.root;

const crossLink = {
  id: 'algebraToGeometry',
  name: 'algebraToGeometry',
  sourceScope: algebraRoot,
  targetScope: geometryRoot,
  type: CrossLinkType.REFERENCE
};

forest.addCrossLink(crossLink);

// Find a tree in the forest
const foundTree = forest.findTree(algebraTree.id);

// Traverse the forest
forest.traverse({
  visitTree: (tree) => {
    console.log(`Visiting tree: ${tree.name}`);
  },
  visitScope: (scope, tree) => {
    console.log(`Visiting scope: ${scope.name} in tree: ${tree.name}`);
  },
  visitTerm: (term, scope, tree) => {
    console.log(`Visiting term: ${term.name} in scope: ${scope.name} in tree: ${tree.name}`);
  },
  visitCrossLink: (link) => {
    console.log(`Visiting cross-link: ${link.name} from ${link.sourceScope.name} to ${link.targetScope.name}`);
  }
});

// Analyze the forest
const analysis = forest.analyze();
console.log(`Tree count: ${analysis.treeCount}`);
console.log(`Scope count: ${analysis.scopeCount}`);
console.log(`Term count: ${analysis.termCount}`);
console.log(`Cross-link count: ${analysis.crossLinkCount}`);
console.log(`Complexity: ${analysis.complexity}`);
```

### Evaluating Terms

```typescript
import { createLinguisticSystem } from '@spicetime/linguistics';

const linguistics = await createLinguisticSystem();

// Create terms
const numberType = linguistics.getType('number');

const addTerm = linguistics.createTerm(
  'add',
  'function',
  [
    { name: 'a', type: numberType, isOptional: false, isRest: false },
    { name: 'b', type: numberType, isOptional: false, isRest: false }
  ],
  numberType,
  (context, args) => args[0] + args[1]
);

// Create a scope
const scope = linguistics.createScope('root');
linguistics.defineTerm(scope.id, 'add', addTerm);

// Create an evaluation context
const context = linguistics.createContext(scope.id);

// Evaluate the term
const result = linguistics.evaluate(addTerm, context, [2, 3]);
console.log(result); // 5

// Set a variable in the context
context.setValue('x', 10);

// Create a term that uses the variable
const multiplyByXTerm = linguistics.createTerm(
  'multiplyByX',
  'function',
  [
    { name: 'a', type: numberType, isOptional: false, isRest: false }
  ],
  numberType,
  (context, args) => args[0] * context.getValue('x')
);

// Evaluate the term
const multiplyResult = linguistics.evaluate(multiplyByXTerm, context, [5]);
console.log(multiplyResult); // 50

// Create a child context
const childContext = context.createChild();
childContext.setValue('x', 20);

// Evaluate the term in the child context
const childMultiplyResult = linguistics.evaluate(multiplyByXTerm, childContext, [5]);
console.log(childMultiplyResult); // 100
```

## Advanced Usage

### Parsing Code

```typescript
import { createLinguisticSystem } from '@spicetime/linguistics';

const linguistics = await createLinguisticSystem();

// Parse code into terms
const terms = linguistics.parse(`
  function add(a, b) {
    return a + b;
  }
  
  const result = add(2, 3);
`);

// Create a scope
const scope = linguistics.createScope('root');

// Define the parsed terms in the scope
for (const term of terms) {
  linguistics.defineTerm(scope.id, term.name, term);
}

// Create an evaluation context
const context = linguistics.createContext(scope.id);

// Lookup the 'add' term
const addTerm = linguistics.lookupTerm(scope.id, 'add');

// Evaluate the term
if (addTerm) {
  const result = linguistics.evaluate(addTerm, context, [2, 3]);
  console.log(result); // 5
}
```

### Custom Types

```typescript
import { createLinguisticSystem, TypeKind } from '@spicetime/linguistics';

const linguistics = await createLinguisticSystem();

// Create a custom type
const personType = linguistics.createType(
  'Person',
  TypeKind.OBJECT,
  {
    properties: {
      name: { type: 'string', required: true },
      age: { type: 'number', required: true }
    },
    isAssignableFrom: (other) => {
      if (other.kind !== TypeKind.OBJECT) return false;
      if (other.name === 'Person') return true;
      return false;
    },
    isSubtypeOf: (other) => {
      if (other.kind === TypeKind.OBJECT && other.name === 'Person') return true;
      if (other.kind === TypeKind.ANY) return true;
      return false;
    }
  }
);

// Register the type
linguistics.registerType('Person', personType);

// Create a term that uses the custom type
const createPersonTerm = linguistics.createTerm(
  'createPerson',
  'function',
  [
    { name: 'name', type: linguistics.getType('string'), isOptional: false, isRest: false },
    { name: 'age', type: linguistics.getType('number'), isOptional: false, isRest: false }
  ],
  personType,
  (context, args) => ({
    name: args[0],
    age: args[1]
  })
);

// Create a scope
const scope = linguistics.createScope('root');
linguistics.defineTerm(scope.id, 'createPerson', createPersonTerm);

// Create an evaluation context
const context = linguistics.createContext(scope.id);

// Evaluate the term
const person = linguistics.evaluate(createPersonTerm, context, ['John', 30]);
console.log(person); // { name: 'John', age: 30 }

// Validate the value against the type
const isValid = linguistics.validateValue(personType, person);
console.log(isValid); // true

// Validate an invalid value
const isInvalid = linguistics.validateValue(personType, { name: 'John' });
console.log(isInvalid); // false (missing age)
```

### Adaptive Implementation

```typescript
import { createLinguisticSystem, detectEnvironment, determineTier } from '@spicetime/linguistics';

// Detect the environment
const capabilities = detectEnvironment();
console.log('Environment capabilities:', capabilities);

// Determine the appropriate tier
const tier = determineTier(capabilities);
console.log('Determined tier:', tier);

// Create a linguistic system for the determined tier
const linguistics = await createLinguisticSystem({
  environment: {
    tier
  }
});

// Check which features are available
const hasAdvancedParsing = linguistics.hasFeature('advanced-parsing');
console.log('Advanced parsing:', hasAdvancedParsing);

const hasJitCompilation = linguistics.hasFeature('jit-compilation');
console.log('JIT compilation:', hasJitCompilation);

// Get all available features
const availableFeatures = linguistics.getAvailableFeatures();
console.log('Available features:', availableFeatures);

// Use the linguistic system regardless of the tier
const scope = linguistics.createScope('root');
const terms = linguistics.parse('function add(a, b) { return a + b; }');
for (const term of terms) {
  linguistics.defineTerm(scope.id, term.name, term);
}
const context = linguistics.createContext(scope.id);
const addTerm = linguistics.lookupTerm(scope.id, 'add');
if (addTerm) {
  const result = linguistics.evaluate(addTerm, context, [2, 3]);
  console.log(result); // 5
}
```

### Storage Integration

```typescript
import { createLinguisticSystem, saveForest, loadForest } from '@spicetime/linguistics';

const linguistics = await createLinguisticSystem();

// Create a forest
const forest = linguistics.createForest('mathForest');

// Create trees
const algebraTree = linguistics.createTree('algebraTree');
const geometryTree = linguistics.createTree('geometryTree');

// Add trees to the forest
forest.addTree(algebraTree);
forest.addTree(geometryTree);

// Save the forest to local storage
await saveForest(forest, 'mathForest.json', {
  type: 'local'
});

// Load the forest from local storage
const loadedForest = await loadForest('mathForest.json', {
  type: 'local'
});

// Save the forest to remote storage
await saveForest(forest, 'mathForest.json', {
  type: 'remote',
  remote: {
    endpoint: 'https://api.example.com/storage',
    credentials: {
      username: 'user',
      password: 'password'
    }
  }
});

// Load the forest from remote storage
const remoteForest = await loadForest('mathForest.json', {
  type: 'remote',
  remote: {
    endpoint: 'https://api.example.com/storage',
    credentials: {
      username: 'user',
      password: 'password'
    }
  }
});

// Save the forest to distributed storage
await saveForest(forest, 'mathForest.json', {
  type: 'distributed',
  distributed: {
    replication: {
      factor: 3,
      consistency: 'eventual'
    },
    sharding: {
      enabled: true,
      strategy: 'hash'
    }
  }
});

// Load the forest from distributed storage
const distributedForest = await loadForest('mathForest.json', {
  type: 'distributed',
  distributed: {
    replication: {
      factor: 3,
      consistency: 'eventual'
    },
    sharding: {
      enabled: true,
      strategy: 'hash'
    }
  }
});
```

### Integration with Core Package

```typescript
import { createSpiceTimeApp } from '@spicetime/core';
import { createLinguisticSystem, integrateLinguisticsWithCore } from '@spicetime/linguistics';

// Create a SpiceTime app
const app = createSpiceTimeApp({
  name: 'MyApp',
  version: '1.0.0'
});

// Create a linguistic system
const linguistics = await createLinguisticSystem();

// Integrate the linguistic system with the core package
integrateLinguisticsWithCore(linguistics, app);

// Use the integrated system
const category = app.createCategory({
  name: 'LinguisticCategory',
  description: 'A category for linguistic terms'
});

const functor = app.createFunctor({
  name: 'LinguisticFunctor',
  description: 'A functor for linguistic terms',
  sourceCategory: category.id,
  targetCategory: category.id,
  mapObject: (obj) => obj,
  mapMorphism: (mor) => mor
});

// Create a linguistic process
const process = app.createProcess({
  type: 'linguistic',
  state: {
    scope: linguistics.createScope('processScope')
  }
});

// Create a linguistic channel
const channel = app.createChannel({
  type: 'linguistic',
  source: process.id,
  target: process.id
});

// Emit an event
app.emitEvent({
  type: 'evaluateTerm',
  payload: {
    termName: 'add',
    args: [2, 3]
  }
});
```

### Utility Functions

```typescript
import { memoize, deepClone, deepMerge, isFeatureSupported } from '@spicetime/linguistics';

// Memoize a function
const fibonacci = memoize((n: number): number => {
  if (n <= 1) return n;
  return fibonacci(n - 1) + fibonacci(n - 2);
});

console.log(fibonacci(10)); // 55
console.log(fibonacci(20)); // 6765

// Deep clone an object
const original = {
  name: 'John',
  age: 30,
  address: {
    street: '123 Main St',
    city: 'New York'
  }
};

const clone = deepClone(original);
clone.address.city = 'Boston';

console.log(original.address.city); // New York
console.log(clone.address.city); // Boston

// Deep merge objects
const target = {
  name: 'John',
  age: 30,
  address: {
    street: '123 Main St',
    city: 'New York'
  }
};

const source = {
  age: 31,
  address: {
    city: 'Boston',
    zip: '02108'
  },
  phone: '555-1234'
};

const merged = deepMerge(target, source);

console.log(merged);
// {
//   name: 'John',
//   age: 31,
//   address: {
//     street: '123 Main St',
//     city: 'Boston',
//     zip: '02108'
//   },
//   phone: '555-1234'
// }

// Check if a feature is supported
const hasWebAssembly = isFeatureSupported('webAssembly');
console.log('WebAssembly support:', hasWebAssembly);

const hasSharedArrayBuffer = isFeatureSupported('sharedArrayBuffer');
console.log('SharedArrayBuffer support:', hasSharedArrayBuffer);

const hasIndexedDB = isFeatureSupported('indexedDB');
console.log('IndexedDB support:', hasIndexedDB);
```
