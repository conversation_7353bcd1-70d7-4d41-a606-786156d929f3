# SpiceTime Linguistics Package API

## Overview

This document provides the API specification for the SpiceTime linguistics package. The linguistics package provides the foundation for functional programming with linguistic terms in the SpiceTime architecture.

## Core API

### createLinguisticSystem

```typescript
function createLinguisticSystem(config?: LinguisticsConfig): Promise<LinguisticSystem>;
```

Creates a linguistic system with the specified configuration.

#### Parameters

- `config`: Optional configuration for the linguistic system

#### Returns

- `Promise<LinguisticSystem>`: A promise that resolves to a linguistic system

#### Example

```typescript
import { createLinguisticSystem } from '@spicetime/linguistics';

const linguistics = await createLinguisticSystem({
  environment: {
    tier: 'standard'
  },
  features: {
    'advanced-parsing': true,
    'jit-compilation': true
  }
});

// Use the linguistic system
const scope = linguistics.createScope('root');
```

### LinguisticSystem

```typescript
interface LinguisticSystem {
  // Initialization
  initialize(): Promise<void>;
  
  // Term operations
  parse(code: string): Term[];
  createTerm(name: string, type: TermType, parameters: Parameter[], returnType: Type, implementation: Implementation): Term;
  getTerm(id: string): Term | null;
  
  // Scope operations
  createScope(name: string, parentId?: string): Scope;
  getScope(id: string): Scope | null;
  
  // Tree operations
  createTree(name: string): Tree;
  getTree(id: string): Tree | null;
  
  // Forest operations
  createForest(name: string): Forest;
  getForest(id: string): Forest | null;
  
  // Evaluation operations
  evaluate(term: Term, context: EvaluationContext, args?: any[]): any;
  createContext(scopeId: string): EvaluationContext;
  
  // Type operations
  registerType(name: string, type: Type): void;
  getType(name: string): Type | null;
  
  // Feature operations
  hasFeature(feature: string): boolean;
  getAvailableFeatures(): string[];
  getRequestedFeatures(): string[];
  
  // Enhancement operations
  enhance(components: any[]): LinguisticSystem;
}
```

### Term

```typescript
interface Term {
  // Term identity
  id: string;
  name: string;
  
  // Term semantics
  type: TermType;
  parameters: Parameter[];
  returnType: Type;
  
  // Term operations
  evaluate(context: EvaluationContext, args: any[]): any;
  compose(other: Term): Term;
  analyze(): TermAnalysis;
  
  // Term serialization
  toJSON(): object;
  toString(): string;
}
```

### Scope

```typescript
interface Scope {
  // Scope identity
  id: string;
  name: string;
  
  // Scope hierarchy
  parent: Scope | null;
  children: Scope[];
  
  // Scope operations
  lookup(name: string): Term | null;
  define(name: string, term: Term): void;
  createChild(name: string): Scope;
  analyze(): ScopeAnalysis;
  
  // Scope serialization
  toJSON(): object;
  toString(): string;
}
```

### Tree

```typescript
interface Tree {
  // Tree identity
  id: string;
  name: string;
  
  // Tree structure
  root: Scope;
  
  // Tree operations
  findScope(id: string): Scope | null;
  createScope(name: string, parent: Scope): Scope;
  traverse(visitor: TreeVisitor): void;
  analyze(): TreeAnalysis;
  
  // Tree serialization
  toJSON(): object;
  toString(): string;
}
```

### Forest

```typescript
interface Forest {
  // Forest identity
  id: string;
  name: string;
  
  // Forest structure
  trees: Tree[];
  crossLinks: CrossLink[];
  
  // Forest operations
  findTree(id: string): Tree | null;
  addTree(tree: Tree): void;
  addCrossLink(link: CrossLink): void;
  traverse(visitor: ForestVisitor): void;
  analyze(): ForestAnalysis;
  
  // Forest serialization
  toJSON(): object;
  toString(): string;
}
```

### EvaluationContext

```typescript
interface EvaluationContext {
  // Context identity
  id: string;
  
  // Context scope
  scope: Scope;
  
  // Context variables
  variables: Map<string, any>;
  
  // Context operations
  getValue(name: string): any;
  setValue(name: string, value: any): void;
  createChild(): EvaluationContext;
  
  // Context serialization
  toJSON(): object;
  toString(): string;
}
```

### Type

```typescript
interface Type {
  // Type identity
  id: string;
  name: string;
  
  // Type structure
  kind: TypeKind;
  
  // Type operations
  isAssignableFrom(other: Type): boolean;
  isSubtypeOf(other: Type): boolean;
  analyze(): TypeAnalysis;
  
  // Type serialization
  toJSON(): object;
  toString(): string;
}
```

## Factory API

### createTerm

```typescript
function createTerm(
  name: string,
  type: TermType,
  parameters: Parameter[],
  returnType: Type,
  implementation: Implementation
): Term;
```

Creates a term with the specified name, type, parameters, return type, and implementation.

#### Parameters

- `name`: The name of the term
- `type`: The type of the term
- `parameters`: The parameters of the term
- `returnType`: The return type of the term
- `implementation`: The implementation of the term

#### Returns

- `Term`: The created term

#### Example

```typescript
import { createTerm, TermType } from '@spicetime/linguistics';

const term = createTerm(
  'add',
  TermType.FUNCTION,
  [
    { name: 'a', type: numberType, isOptional: false, isRest: false },
    { name: 'b', type: numberType, isOptional: false, isRest: false }
  ],
  numberType,
  (context, args) => args[0] + args[1]
);
```

### createScope

```typescript
function createScope(name: string, parent?: Scope): Scope;
```

Creates a scope with the specified name and optional parent.

#### Parameters

- `name`: The name of the scope
- `parent`: Optional parent scope

#### Returns

- `Scope`: The created scope

#### Example

```typescript
import { createScope } from '@spicetime/linguistics';

const rootScope = createScope('root');
const childScope = createScope('child', rootScope);
```

### createTree

```typescript
function createTree(name: string, rootScope?: Scope): Tree;
```

Creates a tree with the specified name and optional root scope.

#### Parameters

- `name`: The name of the tree
- `rootScope`: Optional root scope

#### Returns

- `Tree`: The created tree

#### Example

```typescript
import { createTree, createScope } from '@spicetime/linguistics';

const rootScope = createScope('root');
const tree = createTree('myTree', rootScope);
```

### createForest

```typescript
function createForest(name: string): Forest;
```

Creates a forest with the specified name.

#### Parameters

- `name`: The name of the forest

#### Returns

- `Forest`: The created forest

#### Example

```typescript
import { createForest, createTree } from '@spicetime/linguistics';

const forest = createForest('myForest');
const tree = createTree('myTree');
forest.addTree(tree);
```

### createEvaluationContext

```typescript
function createEvaluationContext(scope: Scope): EvaluationContext;
```

Creates an evaluation context for the specified scope.

#### Parameters

- `scope`: The scope for the context

#### Returns

- `EvaluationContext`: The created evaluation context

#### Example

```typescript
import { createEvaluationContext, createScope } from '@spicetime/linguistics';

const scope = createScope('root');
const context = createEvaluationContext(scope);
```

### createType

```typescript
function createType(name: string, kind: TypeKind, config: TypeConfig): Type;
```

Creates a type with the specified name, kind, and configuration.

#### Parameters

- `name`: The name of the type
- `kind`: The kind of the type
- `config`: The configuration for the type

#### Returns

- `Type`: The created type

#### Example

```typescript
import { createType, TypeKind } from '@spicetime/linguistics';

const numberType = createType('number', TypeKind.PRIMITIVE, {
  isAssignableFrom: (other) => other.kind === TypeKind.PRIMITIVE && other.name === 'number',
  isSubtypeOf: (other) => other.kind === TypeKind.PRIMITIVE && (other.name === 'number' || other.name === 'any')
});
```

## Parser API

### parse

```typescript
function parse(code: string, options?: ParserOptions): Term[];
```

Parses the specified code into terms.

#### Parameters

- `code`: The code to parse
- `options`: Optional parser options

#### Returns

- `Term[]`: The parsed terms

#### Example

```typescript
import { parse } from '@spicetime/linguistics';

const terms = parse(`
  function add(a, b) {
    return a + b;
  }
  
  const result = add(2, 3);
`);
```

### ParserOptions

```typescript
interface ParserOptions {
  // Parser configuration
  ecmaVersion?: number;
  sourceType?: 'script' | 'module';
  locations?: boolean;
  
  // Parser plugins
  plugins?: string[];
  
  // Parser extensions
  extensions?: ParserExtension[];
}
```

## Evaluator API

### evaluate

```typescript
function evaluate(term: Term, context: EvaluationContext, args?: any[]): any;
```

Evaluates the specified term in the specified context with the specified arguments.

#### Parameters

- `term`: The term to evaluate
- `context`: The context for evaluation
- `args`: Optional arguments for the term

#### Returns

- `any`: The result of the evaluation

#### Example

```typescript
import { evaluate, createEvaluationContext } from '@spicetime/linguistics';

const context = createEvaluationContext(scope);
const result = evaluate(term, context, [2, 3]);
```

### EvaluatorOptions

```typescript
interface EvaluatorOptions {
  // Evaluation strategy
  strategy?: EvaluationStrategy;
  
  // Maximum evaluation depth
  maxDepth?: number;
  
  // Evaluation timeout
  timeout?: number;
  
  // JIT compilation
  jit?: boolean;
  
  // Optimization level
  optimizationLevel?: 'none' | 'standard' | 'advanced';
}
```

## Storage API

### saveForest

```typescript
function saveForest(forest: Forest, path: string, options?: StorageOptions): Promise<void>;
```

Saves the specified forest to the specified path.

#### Parameters

- `forest`: The forest to save
- `path`: The path to save to
- `options`: Optional storage options

#### Returns

- `Promise<void>`: A promise that resolves when the forest is saved

#### Example

```typescript
import { saveForest } from '@spicetime/linguistics';

await saveForest(forest, 'myForest.json');
```

### loadForest

```typescript
function loadForest(path: string, options?: StorageOptions): Promise<Forest>;
```

Loads a forest from the specified path.

#### Parameters

- `path`: The path to load from
- `options`: Optional storage options

#### Returns

- `Promise<Forest>`: A promise that resolves to the loaded forest

#### Example

```typescript
import { loadForest } from '@spicetime/linguistics';

const forest = await loadForest('myForest.json');
```

### StorageOptions

```typescript
interface StorageOptions {
  // Storage type
  type?: 'local' | 'remote' | 'hybrid' | 'distributed';
  
  // Remote options
  remote?: {
    endpoint?: string;
    credentials?: {
      username?: string;
      password?: string;
    };
  };
  
  // Distributed options
  distributed?: {
    replication?: {
      factor?: number;
      consistency?: 'eventual' | 'strong';
    };
    sharding?: {
      enabled?: boolean;
      strategy?: 'hash' | 'range';
    };
  };
}
```

## Adaptive API

### detectEnvironment

```typescript
function detectEnvironment(): EnvironmentCapabilities;
```

Detects the capabilities of the current environment.

#### Returns

- `EnvironmentCapabilities`: The detected capabilities

#### Example

```typescript
import { detectEnvironment } from '@spicetime/linguistics';

const capabilities = detectEnvironment();
console.log(`Memory: ${capabilities.memory.totalMemory} bytes`);
console.log(`CPU cores: ${capabilities.processing.cpuCores}`);
```

### determineTier

```typescript
function determineTier(capabilities: EnvironmentCapabilities): EnvironmentTier;
```

Determines the appropriate tier for the specified capabilities.

#### Parameters

- `capabilities`: The environment capabilities

#### Returns

- `EnvironmentTier`: The determined tier

#### Example

```typescript
import { detectEnvironment, determineTier } from '@spicetime/linguistics';

const capabilities = detectEnvironment();
const tier = determineTier(capabilities);
console.log(`Tier: ${tier}`);
```

### EnvironmentCapabilities

```typescript
interface EnvironmentCapabilities {
  // Memory capabilities
  memory: {
    totalMemory: number;
    maxArrayBufferSize: number;
    supportsSharedArrayBuffer: boolean;
  };
  
  // Storage capabilities
  storage: {
    hasLocalStorage: boolean;
    hasIndexedDB: boolean;
    hasFileSystemAccess: boolean;
    availableStorageSpace: number;
    supportsPersistentStorage: boolean;
  };
  
  // Processing capabilities
  processing: {
    cpuCores: number;
    supportsWebWorkers: boolean;
    supportsSharedWorkers: boolean;
    supportsServiceWorkers: boolean;
    supportsWebAssembly: boolean;
    supportsSIMD: boolean;
  };
  
  // Network capabilities
  network: {
    isOnline: boolean;
    connectionType: 'unknown' | 'ethernet' | 'wifi' | 'cellular' | 'none';
    effectiveType: 'unknown' | 'slow-2g' | '2g' | '3g' | '4g';
    isMetered: boolean;
    saveData: boolean;
    downlinkMax: number;
  };
  
  // Platform information
  platform: {
    type: 'browser' | 'node' | 'deno' | 'react-native' | 'electron' | 'unknown';
    os: 'windows' | 'macos' | 'linux' | 'android' | 'ios' | 'unknown';
    device: 'desktop' | 'tablet' | 'mobile' | 'server' | 'unknown';
    browser?: string;
    browserVersion?: string;
    nodeVersion?: string;
  };
}
```

## Integration API

### integrateLinguisticsWithCore

```typescript
function integrateLinguisticsWithCore(linguistics: LinguisticSystem, core: any): void;
```

Integrates the specified linguistic system with the specified core system.

#### Parameters

- `linguistics`: The linguistic system to integrate
- `core`: The core system to integrate with

#### Example

```typescript
import { createLinguisticSystem, integrateLinguisticsWithCore } from '@spicetime/linguistics';
import { createSpiceTimeApp } from '@spicetime/core';

const linguistics = await createLinguisticSystem();
const app = createSpiceTimeApp();
integrateLinguisticsWithCore(linguistics, app);
```

### createLinguisticProcess

```typescript
function createLinguisticProcess(scope: Scope): any;
```

Creates a process for the specified scope.

#### Parameters

- `scope`: The scope for the process

#### Returns

- `any`: The created process

#### Example

```typescript
import { createLinguisticProcess } from '@spicetime/linguistics';

const process = createLinguisticProcess(scope);
```

### createLinguisticChannel

```typescript
function createLinguisticChannel(source: any, target: any): any;
```

Creates a channel between the specified source and target processes.

#### Parameters

- `source`: The source process
- `target`: The target process

#### Returns

- `any`: The created channel

#### Example

```typescript
import { createLinguisticChannel } from '@spicetime/linguistics';

const channel = createLinguisticChannel(sourceProcess, targetProcess);
```

## Utility API

### memoize

```typescript
function memoize<T extends (...args: any[]) => any>(
  fn: T,
  keyFn?: (...args: Parameters<T>) => string
): (...args: Parameters<T>) => ReturnType<T>;
```

Memoizes the specified function.

#### Parameters

- `fn`: The function to memoize
- `keyFn`: Optional function to generate a key from the arguments

#### Returns

- `(...args: Parameters<T>) => ReturnType<T>`: The memoized function

#### Example

```typescript
import { memoize } from '@spicetime/linguistics';

const fibonacci = memoize((n: number): number => {
  if (n <= 1) return n;
  return fibonacci(n - 1) + fibonacci(n - 2);
});
```

### deepClone

```typescript
function deepClone<T>(obj: T): T;
```

Creates a deep clone of the specified object.

#### Parameters

- `obj`: The object to clone

#### Returns

- `T`: The cloned object

#### Example

```typescript
import { deepClone } from '@spicetime/linguistics';

const clonedObject = deepClone(originalObject);
```

### deepMerge

```typescript
function deepMerge<T extends object, U extends object>(target: T, source: U): T & U;
```

Deeply merges the specified source object into the specified target object.

#### Parameters

- `target`: The target object
- `source`: The source object

#### Returns

- `T & U`: The merged object

#### Example

```typescript
import { deepMerge } from '@spicetime/linguistics';

const mergedObject = deepMerge(targetObject, sourceObject);
```

### isFeatureSupported

```typescript
function isFeatureSupported(feature: string): boolean;
```

Checks if the specified feature is supported in the current environment.

#### Parameters

- `feature`: The feature to check

#### Returns

- `boolean`: Whether the feature is supported

#### Example

```typescript
import { isFeatureSupported } from '@spicetime/linguistics';

if (isFeatureSupported('webAssembly')) {
  // Use WebAssembly
} else {
  // Use fallback
}
```
