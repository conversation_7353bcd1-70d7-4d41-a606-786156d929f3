# Error Module (core_error)

## Overview

The Error Module (formerly utils_error) provides a comprehensive error handling system for the SpiceTime architecture. It defines error types, error factories, and error handling utilities that enable robust error management across the system.

## Structure

```
error
├── src               # Source code
│   ├── atomic        # Atomic errors
│   │   ├── base.ts       # Base error
│   │   ├── validation.ts # Validation error
│   │   ├── runtime.ts    # Runtime error
│   │   └── index.ts      # Exports
│   ├── composite     # Composite errors
│   │   ├── aggregate.ts  # Aggregate error
│   │   ├── chain.ts      # Error chain
│   │   ├── context.ts    # Contextual error
│   │   └── index.ts      # Exports
│   ├── functional    # Functional errors
│   │   ├── result.ts     # Result type
│   │   ├── option.ts     # Option type
│   │   ├── either.ts     # Either type
│   │   └── index.ts      # Exports
│   └── index.ts      # Main exports
├── test              # Tests
│   ├── atomic        # Atomic error tests
│   ├── composite     # Composite error tests
│   ├── functional    # Functional error tests
│   └── index.test.ts # Main tests
└── docs              # Documentation
    ├── api.md        # API documentation
    ├── examples.md   # Examples
    └── concepts.md   # Conceptual documentation
```

## Key Components

The Error Module consists of several key components:

1. **Atomic Errors**: Fundamental error types that form the building blocks for more complex error handling.
   - [Base Error](./atomic/BASE_ERROR.md)
   - [Validation Error](./atomic/VALIDATION_ERROR.md)
   - [Runtime Error](./atomic/RUNTIME_ERROR.md)

2. **Composite Errors**: Combinations of atomic errors to form more complex structures.
   - [Aggregate Error](./composite/AGGREGATE_ERROR.md)
   - [Error Chain](./composite/ERROR_CHAIN.md)
   - [Contextual Error](./composite/CONTEXTUAL_ERROR.md)

3. **Functional Errors**: Higher-order error handling mechanisms that provide a foundation for functional error handling.
   - [Result Type](./functional/RESULT.md)
   - [Option Type](./functional/OPTION.md)
   - [Either Type](./functional/EITHER.md)

4. **Error Operations**: Utilities for working with errors.
   - [Try/Catch Operations](./operations/TRY_CATCH.md)
   - [Error Transformation](./operations/TRANSFORMATION.md)
   - [Error Recovery](./operations/RECOVERY.md)

## Design Philosophy

The Error Module is designed with the following principles in mind:

1. **Type Safety**: Errors should be strongly typed to enable compile-time error checking.
2. **Composability**: Errors should be composable to enable complex error handling scenarios.
3. **Context Preservation**: Errors should preserve context to aid in debugging and error resolution.
4. **Functional Approach**: Errors should be handled in a functional way to enable clean, declarative error handling.
5. **Extensibility**: The error system should be extensible to accommodate domain-specific error types.

## Atomic Errors

Atomic errors are the fundamental, indivisible units of the error system. They form the building blocks for more complex error handling.

### Base Error

The base error provides a foundation for all other errors:

```typescript
class BaseError extends Error {
  code: string;
  details: Record<string, any>;
  timestamp: number;

  constructor(message: string, code: string, details: Record<string, any> = {}) {
    super(message);
    this.name = this.constructor.name;
    this.code = code;
    this.details = details;
    this.timestamp = Date.now();

    // Capture stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }

  toJSON(): Record<string, any> {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      details: this.details,
      timestamp: this.timestamp,
      stack: this.stack
    };
  }

  toString(): string {
    return `${this.name} [${this.code}]: ${this.message}`;
  }
}
```

### Validation Error

The validation error represents errors that occur during data validation:

```typescript
class ValidationError extends BaseError {
  constructor(message: string, details: Record<string, any> = {}) {
    super(message, 'VALIDATION_ERROR', details);
  }
}
```

### Runtime Error

The runtime error represents errors that occur during runtime:

```typescript
class RuntimeError extends BaseError {
  constructor(message: string, details: Record<string, any> = {}) {
    super(message, 'RUNTIME_ERROR', details);
  }
}
```

## Composite Errors

Composite errors are combinations of atomic errors to form more complex structures. They allow for the creation of rich, structured error handling.

### Aggregate Error

The aggregate error combines multiple errors into a single error:

```typescript
class AggregateError extends BaseError {
  errors: Error[];

  constructor(message: string, errors: Error[], details: Record<string, any> = {}) {
    super(message, 'AGGREGATE_ERROR', { ...details, errorCount: errors.length });
    this.errors = errors;
  }

  toJSON(): Record<string, any> {
    return {
      ...super.toJSON(),
      errors: this.errors.map(error =>
        error instanceof BaseError ? error.toJSON() : { message: error.message, stack: error.stack }
      )
    };
  }

  toString(): string {
    return `${super.toString()}\nContains ${this.errors.length} errors:\n${this.errors.map(e => `- ${e.toString()}`).join('\n')}`;
  }
}
```

### Error Chain

The error chain represents a sequence of errors, where each error caused the next:

```typescript
class ErrorChain extends BaseError {
  cause: Error;

  constructor(message: string, cause: Error, details: Record<string, any> = {}) {
    super(message, 'CHAIN_ERROR', details);
    this.cause = cause;
  }

  toJSON(): Record<string, any> {
    return {
      ...super.toJSON(),
      cause: this.cause instanceof BaseError ? this.cause.toJSON() : { message: this.cause.message, stack: this.cause.stack }
    };
  }

  toString(): string {
    return `${super.toString()}\nCaused by: ${this.cause.toString()}`;
  }
}
```

### Contextual Error

The contextual error adds context to an error:

```typescript
class ContextualError extends BaseError {
  context: Record<string, any>;
  originalError: Error;

  constructor(message: string, originalError: Error, context: Record<string, any>, details: Record<string, any> = {}) {
    super(message, 'CONTEXTUAL_ERROR', details);
    this.context = context;
    this.originalError = originalError;
  }

  toJSON(): Record<string, any> {
    return {
      ...super.toJSON(),
      context: this.context,
      originalError: this.originalError instanceof BaseError ?
        this.originalError.toJSON() :
        { message: this.originalError.message, stack: this.originalError.stack }
    };
  }

  toString(): string {
    return `${super.toString()}\nContext: ${JSON.stringify(this.context)}\nOriginal error: ${this.originalError.toString()}`;
  }
}
```

## Functional Errors

Functional errors are higher-order error handling mechanisms that provide a foundation for functional error handling.

### Result Type

The Result type represents the result of an operation that might fail:

```typescript
type Result<T, E extends Error = Error> = Success<T> | Failure<E>;

class Success<T> {
  readonly value: T;
  readonly isSuccess = true;
  readonly isFailure = false;

  constructor(value: T) {
    this.value = value;
  }

  map<U>(f: (value: T) => U): Result<U, never> {
    return new Success(f(this.value));
  }

  flatMap<U, E extends Error>(f: (value: T) => Result<U, E>): Result<U, E> {
    return f(this.value);
  }

  getOrElse(_defaultValue: T): T {
    return this.value;
  }

  getOrThrow(): T {
    return this.value;
  }
}

class Failure<E extends Error> {
  readonly error: E;
  readonly isSuccess = false;
  readonly isFailure = true;

  constructor(error: E) {
    this.error = error;
  }

  map<U>(_f: (value: never) => U): Result<U, E> {
    return this as unknown as Result<U, E>;
  }

  flatMap<U, F extends Error>(_f: (value: never) => Result<U, F>): Result<U, E | F> {
    return this as unknown as Result<U, E | F>;
  }

  getOrElse<T>(defaultValue: T): T {
    return defaultValue;
  }

  getOrThrow(): never {
    throw this.error;
  }
}
```

### Option Type

The Option type represents a value that might not exist:

```typescript
type Option<T> = Some<T> | None;

class Some<T> {
  readonly value: T;
  readonly isSome = true;
  readonly isNone = false;

  constructor(value: T) {
    this.value = value;
  }

  map<U>(f: (value: T) => U): Option<U> {
    return new Some(f(this.value));
  }

  flatMap<U>(f: (value: T) => Option<U>): Option<U> {
    return f(this.value);
  }

  getOrElse(_defaultValue: T): T {
    return this.value;
  }

  getOrThrow(): T {
    return this.value;
  }
}

class None {
  readonly isSome = false;
  readonly isNone = true;

  map<U>(_f: (value: never) => U): Option<U> {
    return this as unknown as Option<U>;
  }

  flatMap<U>(_f: (value: never) => Option<U>): Option<U> {
    return this as unknown as Option<U>;
  }

  getOrElse<T>(defaultValue: T): T {
    return defaultValue;
  }

  getOrThrow(): never {
    throw new Error('Cannot get value from None');
  }
}
```

### Either Type

The Either type represents a value of one of two possible types:

```typescript
type Either<L, R> = Left<L> | Right<R>;

class Left<L> {
  readonly value: L;
  readonly isLeft = true;
  readonly isRight = false;

  constructor(value: L) {
    this.value = value;
  }

  map<U>(_f: (value: never) => U): Either<L, U> {
    return this as unknown as Either<L, U>;
  }

  flatMap<U, M>(_f: (value: never) => Either<M, U>): Either<L | M, U> {
    return this as unknown as Either<L | M, U>;
  }

  getLeftOrElse(_defaultValue: L): L {
    return this.value;
  }

  getRightOrElse<R>(defaultValue: R): R {
    return defaultValue;
  }

  fold<U>(onLeft: (value: L) => U, _onRight: (value: never) => U): U {
    return onLeft(this.value);
  }
}

class Right<R> {
  readonly value: R;
  readonly isLeft = false;
  readonly isRight = true;

  constructor(value: R) {
    this.value = value;
  }

  map<U>(f: (value: R) => U): Either<never, U> {
    return new Right(f(this.value));
  }

  flatMap<U, L>(f: (value: R) => Either<L, U>): Either<L, U> {
    return f(this.value);
  }

  getLeftOrElse<L>(defaultValue: L): L {
    return defaultValue;
  }

  getRightOrElse(_defaultValue: R): R {
    return this.value;
  }

  fold<U>(_onLeft: (value: never) => U, onRight: (value: R) => U): U {
    return onRight(this.value);
  }
}
```

## Error Operations

The Error Module provides operations for working with errors:

### Try

The try function executes a function and returns a Result:

```typescript
function tryResult<T>(f: () => T): Result<T> {
  try {
    return new Success(f());
  } catch (error) {
    return new Failure(error instanceof Error ? error : new Error(String(error)));
  }
}
```

### Wrap

The wrap function wraps a function to return a Result:

```typescript
function wrapResult<T, A extends any[]>(f: (...args: A) => T): (...args: A) => Result<T> {
  return (...args: A) => {
    try {
      return new Success(f(...args));
    } catch (error) {
      return new Failure(error instanceof Error ? error : new Error(String(error)));
    }
  };
}
```

### Recover

The recover function recovers from an error:

```typescript
function recover<T>(result: Result<T>, recovery: (error: Error) => T): T {
  if (result.isSuccess) {
    return result.value;
  } else {
    return recovery(result.error);
  }
}
```

## Integration with Other Modules

The Error Module integrates with other modules in the following ways:

1. **Origin Reference Frame**: Provides error handling for the origin.rfr module.
2. **Categorical Types Object**: Provides error handling for the cat_types.obj module.
3. **Process Reference Frame**: Provides error handling for the process.rfr module.
4. **SpiceTime Version Space**: Provides error handling for the spice.space module.

## API

The Error Module provides the following API:

```typescript
// Atomic errors
export class BaseError extends Error {
  code: string;
  details: Record<string, any>;
  timestamp: number;

  constructor(message: string, code: string, details?: Record<string, any>);
  toJSON(): Record<string, any>;
  toString(): string;
}

export class ValidationError extends BaseError {
  constructor(message: string, details?: Record<string, any>);
}

export class RuntimeError extends BaseError {
  constructor(message: string, details?: Record<string, any>);
}

// Composite errors
export class AggregateError extends BaseError {
  errors: Error[];

  constructor(message: string, errors: Error[], details?: Record<string, any>);
  toJSON(): Record<string, any>;
  toString(): string;
}

export class ErrorChain extends BaseError {
  cause: Error;

  constructor(message: string, cause: Error, details?: Record<string, any>);
  toJSON(): Record<string, any>;
  toString(): string;
}

export class ContextualError extends BaseError {
  context: Record<string, any>;
  originalError: Error;

  constructor(message: string, originalError: Error, context: Record<string, any>, details?: Record<string, any>);
  toJSON(): Record<string, any>;
  toString(): string;
}

// Functional errors
export type Result<T, E extends Error = Error> = Success<T> | Failure<E>;

export class Success<T> {
  readonly value: T;
  readonly isSuccess: true;
  readonly isFailure: false;

  constructor(value: T);
  map<U>(f: (value: T) => U): Result<U, never>;
  flatMap<U, E extends Error>(f: (value: T) => Result<U, E>): Result<U, E>;
  getOrElse(defaultValue: T): T;
  getOrThrow(): T;
}

export class Failure<E extends Error> {
  readonly error: E;
  readonly isSuccess: false;
  readonly isFailure: true;

  constructor(error: E);
  map<U>(f: (value: never) => U): Result<U, E>;
  flatMap<U, F extends Error>(f: (value: never) => Result<U, F>): Result<U, E | F>;
  getOrElse<T>(defaultValue: T): T;
  getOrThrow(): never;
}

export type Option<T> = Some<T> | None;

export class Some<T> {
  readonly value: T;
  readonly isSome: true;
  readonly isNone: false;

  constructor(value: T);
  map<U>(f: (value: T) => U): Option<U>;
  flatMap<U>(f: (value: T) => Option<U>): Option<U>;
  getOrElse(defaultValue: T): T;
  getOrThrow(): T;
}

export class None {
  readonly isSome: false;
  readonly isNone: true;

  map<U>(f: (value: never) => U): Option<U>;
  flatMap<U>(f: (value: never) => Option<U>): Option<U>;
  getOrElse<T>(defaultValue: T): T;
  getOrThrow(): never;
}

export type Either<L, R> = Left<L> | Right<R>;

export class Left<L> {
  readonly value: L;
  readonly isLeft: true;
  readonly isRight: false;

  constructor(value: L);
  map<U>(f: (value: never) => U): Either<L, U>;
  flatMap<U, M>(f: (value: never) => Either<M, U>): Either<L | M, U>;
  getLeftOrElse(defaultValue: L): L;
  getRightOrElse<R>(defaultValue: R): R;
  fold<U>(onLeft: (value: L) => U, onRight: (value: never) => U): U;
}

export class Right<R> {
  readonly value: R;
  readonly isLeft: false;
  readonly isRight: true;

  constructor(value: R);
  map<U>(f: (value: R) => U): Either<never, U>;
  flatMap<U, L>(f: (value: R) => Either<L, U>): Either<L, U>;
  getLeftOrElse<L>(defaultValue: L): L;
  getRightOrElse(defaultValue: R): R;
  fold<U>(onLeft: (value: never) => U, onRight: (value: R) => U): U;
}

// Error operations
export function tryResult<T>(f: () => T): Result<T>;
export function wrapResult<T, A extends any[]>(f: (...args: A) => T): (...args: A) => Result<T>;
export function recover<T>(result: Result<T>, recovery: (error: Error) => T): T;
```

## Examples

### Using Atomic Errors

```typescript
import { ValidationError, RuntimeError } from '@spicetime/core/error';

// Create a validation error
const validationError = new ValidationError('Invalid input', { field: 'username', value: '' });
console.log(validationError.toString()); // ValidationError [VALIDATION_ERROR]: Invalid input

// Create a runtime error
const runtimeError = new RuntimeError('Failed to execute', { operation: 'processData' });
console.log(runtimeError.toString()); // RuntimeError [RUNTIME_ERROR]: Failed to execute
```

### Using Composite Errors

```typescript
import { ValidationError, RuntimeError, AggregateError, ErrorChain, ContextualError } from '@spicetime/core/error';

// Create atomic errors
const validationError = new ValidationError('Invalid input', { field: 'username' });
const runtimeError = new RuntimeError('Failed to execute');

// Create an aggregate error
const aggregateError = new AggregateError('Multiple errors occurred', [validationError, runtimeError]);
console.log(aggregateError.toString());
// AggregateError [AGGREGATE_ERROR]: Multiple errors occurred
// Contains 2 errors:
// - ValidationError [VALIDATION_ERROR]: Invalid input
// - RuntimeError [RUNTIME_ERROR]: Failed to execute

// Create an error chain
const chainError = new ErrorChain('Failed to process request', validationError);
console.log(chainError.toString());
// ErrorChain [CHAIN_ERROR]: Failed to process request
// Caused by: ValidationError [VALIDATION_ERROR]: Invalid input

// Create a contextual error
const contextualError = new ContextualError(
  'Error in user service',
  runtimeError,
  { userId: '123', action: 'update' }
);
console.log(contextualError.toString());
// ContextualError [CONTEXTUAL_ERROR]: Error in user service
// Context: {"userId":"123","action":"update"}
// Original error: RuntimeError [RUNTIME_ERROR]: Failed to execute
```

### Using Functional Errors

```typescript
import { Result, Success, Failure, Option, Some, None, Either, Left, Right, tryResult } from '@spicetime/core/error';

// Using Result
function divide(a: number, b: number): Result<number> {
  if (b === 0) {
    return new Failure(new Error('Division by zero'));
  }
  return new Success(a / b);
}

const result1 = divide(10, 2);
const result2 = divide(10, 0);

if (result1.isSuccess) {
  console.log('Result 1:', result1.value); // Result 1: 5
}

if (result2.isFailure) {
  console.log('Result 2 error:', result2.error.message); // Result 2 error: Division by zero
}

// Using Option
function findUser(id: string): Option<{ id: string, name: string }> {
  if (id === '123') {
    return new Some({ id: '123', name: 'John' });
  }
  return new None();
}

const user1 = findUser('123');
const user2 = findUser('456');

console.log('User 1 exists:', user1.isSome); // User 1 exists: true
console.log('User 2 exists:', user2.isSome); // User 2 exists: false

// Using Either
function parseJson(json: string): Either<Error, any> {
  try {
    return new Right(JSON.parse(json));
  } catch (error) {
    return new Left(error instanceof Error ? error : new Error(String(error)));
  }
}

const validJson = '{"name":"John","age":30}';
const invalidJson = '{name:"John",age:30}';

const parsed1 = parseJson(validJson);
const parsed2 = parseJson(invalidJson);

parsed1.fold(
  error => console.log('Error:', error.message),
  data => console.log('Data:', data) // Data: { name: 'John', age: 30 }
);

parsed2.fold(
  error => console.log('Error:', error.message), // Error: Unexpected token n in JSON at position 1
  data => console.log('Data:', data)
);

// Using tryResult
const result = tryResult(() => {
  if (Math.random() < 0.5) {
    throw new Error('Random error');
  }
  return 'Success';
});

if (result.isSuccess) {
  console.log('Operation succeeded:', result.value);
} else {
  console.log('Operation failed:', result.error.message);
}
```

## Next Steps

1. **Document the atomic errors**: Create detailed documentation for the atomic errors.
2. **Document the composite errors**: Create detailed documentation for the composite errors.
3. **Document the functional errors**: Create detailed documentation for the functional errors.
4. **Create API specifications**: Define the API for each module in the error module.
5. **Create schema definitions**: Define the schemas for the data structures used in the error module.
6. **Create examples**: Provide examples of using the error module in different scenarios.
