# SpiceTime Core Package Schema

## Overview

This document provides the schema definitions for the SpiceTime core package. These schemas define the structure of the data used by the core components, including the configuration objects, the categorical framework, the process space, and the kernel interface.

## Configuration Schemas

### SpiceTimeConfig

```typescript
/**
 * Configuration for a SpiceTime application.
 */
interface SpiceTimeConfig {
  /**
   * The name of the application.
   */
  name: string;
  
  /**
   * The version of the application.
   */
  version: string;
  
  /**
   * Configuration for the categorical framework.
   */
  categorical?: CategoricalConfig;
  
  /**
   * Configuration for the process space.
   */
  process?: ProcessConfig;
  
  /**
   * Configuration for the kernel.
   */
  kernel?: KernelConfig;
  
  /**
   * Configuration for plugins.
   */
  plugins?: PluginConfig[];
}
```

### CategoricalConfig

```typescript
/**
 * Configuration for the categorical framework.
 */
interface CategoricalConfig {
  /**
   * Initial categories to create.
   */
  categories?: CategoryConfig[];
  
  /**
   * Initial functors to create.
   */
  functors?: FunctorConfig[];
  
  /**
   * Initial natural transformations to create.
   */
  naturalTransformations?: NaturalTransformationConfig[];
}
```

### ProcessConfig

```typescript
/**
 * Configuration for the process space.
 */
interface ProcessConfig {
  /**
   * Initial processes to create.
   */
  processes?: ProcessDefinition[];
  
  /**
   * Initial channels to create.
   */
  channels?: ChannelDefinition[];
  
  /**
   * Initial transformations to create.
   */
  transformations?: TransformationDefinition[];
  
  /**
   * Initial state of the process space.
   */
  initialState?: Record<string, any>;
}
```

### KernelConfig

```typescript
/**
 * Configuration for the kernel.
 */
interface KernelConfig {
  /**
   * Configuration for the scheduler.
   */
  scheduler?: {
    /**
     * The scheduling policy to use.
     */
    policy?: 'round-robin' | 'priority' | 'fair-share' | 'custom';
    
    /**
     * The time slice for the scheduler.
     */
    timeSlice?: number;
    
    /**
     * The custom algorithm to use if policy is 'custom'.
     */
    customAlgorithm?: string;
  };
  
  /**
   * Configuration for resources.
   */
  resources?: ResourceConfig[];
  
  /**
   * Configuration for middleware.
   */
  middleware?: MiddlewareConfig[];
}
```

### PluginConfig

```typescript
/**
 * Configuration for a plugin.
 */
interface PluginConfig {
  /**
   * The ID of the plugin.
   */
  id: string;
  
  /**
   * The name of the plugin.
   */
  name: string;
  
  /**
   * The version of the plugin.
   */
  version: string;
  
  /**
   * The initialization function for the plugin.
   */
  initialize: (app: SpiceTimeApp<any>) => void;
}
```

## Categorical Framework Schemas

### CategoryConfig

```typescript
/**
 * Configuration for a category.
 */
interface CategoryConfig<Obj, Mor> {
  /**
   * The name of the category.
   */
  name: string;
  
  /**
   * The description of the category.
   */
  description: string;
  
  /**
   * Function to check if two objects are equal.
   */
  objectEquals: (a: Obj, b: Obj) => boolean;
  
  /**
   * Function to check if two morphisms are equal.
   */
  morphismEquals: (a: Mor, b: Mor) => boolean;
  
  /**
   * Function to get the identity morphism for an object.
   */
  identity: (obj: Obj) => Mor;
  
  /**
   * Function to compose two morphisms.
   */
  compose: (f: Mor, g: Mor) => Mor;
  
  /**
   * Initial objects to add to the category.
   */
  initialObjects?: Obj[];
  
  /**
   * Initial morphisms to add to the category.
   */
  initialMorphisms?: {
    source: Obj;
    target: Obj;
    morphism: Mor;
  }[];
  
  /**
   * Metadata for the category.
   */
  metadata?: Record<string, any>;
}
```

### FunctorConfig

```typescript
/**
 * Configuration for a functor.
 */
interface FunctorConfig<SrcObj, SrcMor, TgtObj, TgtMor> {
  /**
   * The name of the functor.
   */
  name: string;
  
  /**
   * The description of the functor.
   */
  description: string;
  
  /**
   * The ID of the source category.
   */
  sourceCategory: CategoryId;
  
  /**
   * The ID of the target category.
   */
  targetCategory: CategoryId;
  
  /**
   * Function to map objects from the source category to the target category.
   */
  mapObject: (obj: SrcObj) => TgtObj;
  
  /**
   * Function to map morphisms from the source category to the target category.
   */
  mapMorphism: (mor: SrcMor) => TgtMor;
  
  /**
   * Metadata for the functor.
   */
  metadata?: Record<string, any>;
}
```

### NaturalTransformationConfig

```typescript
/**
 * Configuration for a natural transformation.
 */
interface NaturalTransformationConfig<SrcObj, SrcMor, TgtObj, TgtMor> {
  /**
   * The name of the natural transformation.
   */
  name: string;
  
  /**
   * The description of the natural transformation.
   */
  description: string;
  
  /**
   * The ID of the source functor.
   */
  sourceFunctor: FunctorId;
  
  /**
   * The ID of the target functor.
   */
  targetFunctor: FunctorId;
  
  /**
   * Function to get the component of the natural transformation at an object.
   */
  component: (obj: SrcObj) => TgtMor;
  
  /**
   * Metadata for the natural transformation.
   */
  metadata?: Record<string, any>;
}
```

## Process Space Schemas

### ProcessDefinition

```typescript
/**
 * Definition of a process.
 */
interface ProcessDefinition {
  /**
   * The type of the process.
   */
  type: string;
  
  /**
   * The initial state of the process.
   */
  initialState: Record<string, any>;
  
  /**
   * The IDs of the input channels.
   */
  inputs?: ChannelId[];
  
  /**
   * The IDs of the output channels.
   */
  outputs?: ChannelId[];
  
  /**
   * Metadata for the process.
   */
  metadata?: Record<string, any>;
}
```

### ChannelDefinition

```typescript
/**
 * Definition of a channel.
 */
interface ChannelDefinition {
  /**
   * The type of the channel.
   */
  type: string;
  
  /**
   * The ID of the source process, or null if the channel has no source.
   */
  source?: ProcessId;
  
  /**
   * The ID of the target process, or null if the channel has no target.
   */
  target?: ProcessId;
  
  /**
   * Function to filter events on the channel.
   */
  filter?: (event: Event) => boolean;
  
  /**
   * Function to transform events on the channel.
   */
  transform?: (event: Event) => Event;
  
  /**
   * Permissions for the channel.
   */
  permissions?: Permission[];
  
  /**
   * Metadata for the channel.
   */
  metadata?: Record<string, any>;
}
```

### TransformationDefinition

```typescript
/**
 * Definition of a transformation.
 */
interface TransformationDefinition {
  /**
   * The type of the transformation.
   */
  type: string;
  
  /**
   * The type of the source.
   */
  source: TypeDefinition;
  
  /**
   * The type of the target.
   */
  target: TypeDefinition;
  
  /**
   * Function to transform the source to the target.
   */
  transform: (source: any) => any;
  
  /**
   * Metadata for the transformation.
   */
  metadata?: Record<string, any>;
}
```

### TypeDefinition

```typescript
/**
 * Definition of a type.
 */
type TypeDefinition = 
  | { kind: 'primitive', type: 'string' | 'number' | 'boolean' | 'null' | 'undefined' | 'any' }
  | { kind: 'array', itemType: TypeDefinition }
  | { kind: 'object', properties: Record<string, TypeDefinition>, required?: string[] }
  | { kind: 'union', types: TypeDefinition[] }
  | { kind: 'intersection', types: TypeDefinition[] }
  | { kind: 'reference', name: string };
```

### Permission

```typescript
/**
 * Permission for a channel.
 */
interface Permission {
  /**
   * The type of the permission.
   */
  type: string;
  
  /**
   * The subject of the permission, or null if the permission applies to all subjects.
   */
  subject: string | null;
  
  /**
   * The action that the permission applies to.
   */
  action: string;
  
  /**
   * The resource that the permission applies to, or null if the permission applies to all resources.
   */
  resource: string | null;
  
  /**
   * Function to check if the permission applies in a given context.
   */
  condition: (context: Context, event: Event) => boolean;
}
```

## Kernel Schemas

### ResourceConfig

```typescript
/**
 * Configuration for a resource.
 */
interface ResourceConfig {
  /**
   * The type of the resource.
   */
  type: string;
  
  /**
   * The capacity of the resource.
   */
  capacity: number;
  
  /**
   * Constraints on the resource.
   */
  constraints?: ResourceConstraint[];
}
```

### ResourceConstraint

```typescript
/**
 * Constraint on a resource.
 */
interface ResourceConstraint {
  /**
   * The type of the constraint.
   */
  type: string;
  
  /**
   * Parameters for the constraint.
   */
  parameters: any[];
}
```

### MiddlewareConfig

```typescript
/**
 * Configuration for middleware.
 */
interface MiddlewareConfig {
  /**
   * The ID of the middleware.
   */
  id: string;
  
  /**
   * The ID of the chain that the middleware belongs to.
   */
  chainId: string;
  
  /**
   * Function to handle events.
   */
  handler: (event: Event) => Event | Promise<Event>;
}
```

### ResourceRequest

```typescript
/**
 * Request for a resource.
 */
interface ResourceRequest {
  /**
   * The type of the resource.
   */
  type: string;
  
  /**
   * The amount of the resource requested.
   */
  amount: number;
  
  /**
   * Constraints on the resource.
   */
  constraints?: ResourceConstraint[];
}
```

### ResourceAllocation

```typescript
/**
 * Allocation of a resource.
 */
interface ResourceAllocation {
  /**
   * The ID of the allocation.
   */
  id: string;
  
  /**
   * The ID of the process that the resource is allocated to.
   */
  processId: string;
  
  /**
   * The type of the resource.
   */
  resourceType: string;
  
  /**
   * The amount of the resource allocated.
   */
  amount: number;
  
  /**
   * The time when the allocation was created.
   */
  createdAt: number;
  
  /**
   * The time when the allocation expires, or undefined if it never expires.
   */
  expiresAt?: number;
}
```

### ResourceUsage

```typescript
/**
 * Usage of resources.
 */
interface ResourceUsage {
  /**
   * Usage by resource type.
   */
  byType: Record<string, {
    /**
     * The capacity of the resource.
     */
    capacity: number;
    
    /**
     * The amount of the resource used.
     */
    used: number;
    
    /**
     * The amount of the resource available.
     */
    available: number;
  }>;
  
  /**
   * Usage by process.
   */
  byProcess: Record<string, {
    /**
     * Allocations by resource type.
     */
    allocations: Record<string, {
      /**
       * The amount of the resource allocated.
       */
      amount: number;
      
      /**
       * The time when the allocation was created.
       */
      createdAt: number;
      
      /**
       * The time when the allocation expires, or undefined if it never expires.
       */
      expiresAt?: number;
    }[]>;
  }>;
}
```

### ProcessTree

```typescript
/**
 * Tree of processes.
 */
interface ProcessTree {
  /**
   * The ID of the root process.
   */
  root: string;
  
  /**
   * Nodes in the tree, indexed by process ID.
   */
  nodes: Record<string, ProcessTreeNode>;
}
```

### ProcessTreeNode

```typescript
/**
 * Node in a process tree.
 */
interface ProcessTreeNode {
  /**
   * The ID of the process.
   */
  processId: string;
  
  /**
   * The ID of the parent process, or undefined if the process has no parent.
   */
  parent?: string;
  
  /**
   * The IDs of the child processes.
   */
  children: string[];
  
  /**
   * The depth of the node in the tree.
   */
  depth: number;
  
  /**
   * The path to the node in the tree.
   */
  path: string;
}
```

## Event Schemas

### Event

```typescript
/**
 * An event in the system.
 */
interface Event {
  /**
   * The type of the event.
   */
  type: string;
  
  /**
   * The payload of the event.
   */
  payload: any;
  
  /**
   * Metadata for the event.
   */
  metadata: Record<string, any>;
  
  /**
   * The time when the event was created.
   */
  timestamp: number;
}
```

### Context

```typescript
/**
 * Context for evaluating permissions and conditions.
 */
interface Context {
  /**
   * The user in the context, or null if there is no user.
   */
  user: User | null;
  
  /**
   * The roles in the context.
   */
  roles: string[];
  
  /**
   * The time of the context.
   */
  timestamp: number;
  
  /**
   * Metadata for the context.
   */
  metadata: Record<string, any>;
}
```

### User

```typescript
/**
 * A user in the system.
 */
interface User {
  /**
   * The ID of the user.
   */
  id: string;
  
  /**
   * The roles of the user.
   */
  roles: string[];
  
  /**
   * Metadata for the user.
   */
  metadata: Record<string, any>;
}
```

## Utility Schemas

### Subscription

```typescript
/**
 * A subscription to an event source.
 */
interface Subscription {
  /**
   * Unsubscribe from the event source.
   */
  unsubscribe: () => void;
}
```

### Observable

```typescript
/**
 * An observable stream of values.
 */
interface Observable<T> {
  /**
   * Subscribe to the observable.
   */
  subscribe: (observer: Observer<T>) => Subscription;
  
  /**
   * Map the values of the observable.
   */
  map: <U>(mapper: (value: T) => U) => Observable<U>;
  
  /**
   * Filter the values of the observable.
   */
  filter: (predicate: (value: T) => boolean) => Observable<T>;
  
  /**
   * Reduce the values of the observable.
   */
  reduce: <U>(reducer: (accumulator: U, value: T) => U, initialValue: U) => Observable<U>;
}
```

### Observer

```typescript
/**
 * An observer of an observable.
 */
interface Observer<T> {
  /**
   * Handle a new value.
   */
  next: (value: T) => void;
  
  /**
   * Handle an error.
   */
  error?: (error: Error) => void;
  
  /**
   * Handle completion.
   */
  complete?: () => void;
}
```

### VerificationResult

```typescript
/**
 * Result of verifying a categorical structure.
 */
interface VerificationResult {
  /**
   * Whether the verification was successful.
   */
  valid: boolean;
  
  /**
   * Errors found during verification.
   */
  errors: VerificationError[];
  
  /**
   * Warnings found during verification.
   */
  warnings: VerificationWarning[];
}
```

### VerificationError

```typescript
/**
 * An error found during verification.
 */
interface VerificationError {
  /**
   * The code of the error.
   */
  code: string;
  
  /**
   * The message of the error.
   */
  message: string;
  
  /**
   * Details of the error.
   */
  details: any;
}
```

### VerificationWarning

```typescript
/**
 * A warning found during verification.
 */
interface VerificationWarning {
  /**
   * The code of the warning.
   */
  code: string;
  
  /**
   * The message of the warning.
   */
  message: string;
  
  /**
   * Details of the warning.
   */
  details: any;
}
```
