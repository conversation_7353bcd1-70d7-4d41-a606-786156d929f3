# Scope Type Specification

## Overview

This specification defines the type structure for scopes in the cat_types package. Scopes are JavaScript prototypical objects that provide context-aware access to terms, with getters being invoked with the scope itself as their context.

## Type Definition

```typescript
/**
 * Scope type
 */
export type Scope = 'public' | 'protected' | 'private' | 'dynamic' | 'injected';

/**
 * Scope object
 */
export interface ScopeObject {
  /**
   * Allows indexing with any string key
   * Returns the value associated with the key
   * 
   * When accessed, `this` refers to the scope object itself
   */
  [key: string]: any;
}

/**
 * Scope getter function
 * 
 * When invoked, `this` refers to the scope object
 */
export type ScopeGetter<T = any> = (this: ScopeObject) => T;

/**
 * Scope definition
 */
export interface ScopeDefinition {
  /**
   * Name of the scope
   */
  readonly name: string;
  
  /**
   * Type of the scope
   */
  readonly type: Scope;
  
  /**
   * Parent scope, if any
   */
  readonly parent?: ScopeObject;
  
  /**
   * Terms defined in this scope
   */
  readonly terms: Map<string, ScopeGetter>;
}

/**
 * Scope factory
 */
export interface ScopeFactory {
  /**
   * Creates a new scope
   * 
   * @param name Scope name
   * @param type Scope type
   * @param parent Parent scope
   * @returns A new scope object
   */
  createScope(name: string, type: Scope, parent?: ScopeObject): ScopeObject;
  
  /**
   * Adds a term to a scope
   * 
   * @param scope Scope to add the term to
   * @param name Term name
   * @param getter Term getter function
   */
  addTerm(scope: ScopeObject, name: string, getter: ScopeGetter): void;
  
  /**
   * Creates a child scope
   * 
   * @param parent Parent scope
   * @param name Child scope name
   * @param type Child scope type
   * @returns A new child scope
   */
  createChildScope(parent: ScopeObject, name: string, type: Scope): ScopeObject;
  
  /**
   * Gets the definition of a scope
   * 
   * @param scope Scope object
   * @returns The scope definition
   */
  getScopeDefinition(scope: ScopeObject): ScopeDefinition;
}
```

## Usage

Scopes in cat_types are JavaScript prototypical objects that provide context-aware access to terms. When a term is accessed through a scope, the getter function is invoked with the scope itself as its context (`this`). This allows terms to access other terms in the same scope or parent scopes.

For example:

```typescript
// Create a scope
const scope = scopeFactory.createScope('root', 'public');

// Add a term
scopeFactory.addTerm(scope, 'x', function() { return 42; });

// Add a term that uses another term
scopeFactory.addTerm(scope, 'y', function() { return this.x * 2; });

// Access terms
console.log(scope.x); // 42
console.log(scope.y); // 84
```

The `ScopeGetter` type explicitly indicates that when the getter function is invoked, `this` refers to the scope object. This ensures that terms can access other terms in the same scope or parent scopes.

## Implementation Requirements

Implementations of scopes must:

1. Use JavaScript's prototype chain with Object.create for scope inheritance
2. Use getters for lazy evaluation
3. Ensure that when a getter is invoked, `this` refers to the scope object
4. Support accessing terms from parent scopes through the prototype chain

The implementation should leverage JavaScript's native capabilities for object property access and prototype inheritance, avoiding unnecessary wrapper functions.

## File Location

This type definition should be placed in:
```
spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/__types/scope.type.ts
```

## Related Types

- `ScopedTreeFunctor`: Uses scopes to provide a categorical view of scoped tree structures
