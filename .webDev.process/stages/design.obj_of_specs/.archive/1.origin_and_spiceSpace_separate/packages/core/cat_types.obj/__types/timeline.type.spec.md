# Timeline Functor Type Specification

## Overview

This specification defines the type structure for Timeline Functors in the cat_types package. A Timeline Functor is a specialized functor that models sequential, time-ordered data structures. It builds on category theory's concepts of monoids, streams, and directed graphs to provide a categorical foundation for timelines, concept streams, and other sequential structures.

## Type Definition

```typescript
/**
 * Represents a point in time with associated data
 * 
 * @typeParam T - Type of data associated with the time point
 */
export interface TimePoint<T = any> {
  /**
   * Unique identifier for the time point
   */
  readonly id: symbol;
  
  /**
   * Timestamp or sequence number
   */
  readonly time: number;
  
  /**
   * Data associated with the time point
   */
  readonly data: T;
  
  /**
   * Metadata about the time point
   */
  readonly meta?: Record<string, any>;
}

/**
 * Represents a transition between time points
 * 
 * @typeParam T - Type of data associated with the time points
 */
export interface TimeTransition<T = any> {
  /**
   * Source time point
   */
  readonly source: TimePoint<T>;
  
  /**
   * Target time point
   */
  readonly target: TimePoint<T>;
  
  /**
   * Transition function that transforms source data to target data
   */
  readonly transform: (source: T) => T;
  
  /**
   * Duration of the transition
   */
  readonly duration?: number;
  
  /**
   * Metadata about the transition
   */
  readonly meta?: Record<string, any>;
}

/**
 * Represents a Timeline Functor
 * 
 * A Timeline Functor models sequential, time-ordered data structures.
 * It can represent finite sequences, infinite streams, or branching timelines.
 * 
 * @typeParam T - Type of objects in the target category
 * @typeParam TM - Type of morphisms in the target category
 * @typeParam D - Type of data associated with time points
 */
export interface TimelineFunctor<
  T extends CatObject,
  TM extends Morphism<T>,
  D = any
> extends Functor<CatObject, Morphism<CatObject>, T, TM> {
  /**
   * The time points in the timeline
   */
  readonly points: Map<symbol, TimePoint<D>>;
  
  /**
   * The transitions between time points
   */
  readonly transitions: Map<string, TimeTransition<D>>;
  
  /**
   * Gets a time point by its ID
   * 
   * @param id Time point ID
   * @returns The time point with the given ID, or undefined if not found
   */
  getPoint(id: symbol): TimePoint<D> | undefined;
  
  /**
   * Gets time points within a time range
   * 
   * @param startTime Start time (inclusive)
   * @param endTime End time (inclusive)
   * @returns Time points within the given range
   */
  getPointsInRange(startTime: number, endTime: number): TimePoint<D>[];
  
  /**
   * Gets the transition between two time points
   * 
   * @param sourceId Source time point ID
   * @param targetId Target time point ID
   * @returns The transition between the given time points, or undefined if not found
   */
  getTransition(sourceId: symbol, targetId: symbol): TimeTransition<D> | undefined;
  
  /**
   * Adds a time point to the timeline
   * 
   * @param time Timestamp or sequence number
   * @param data Data associated with the time point
   * @param meta Metadata about the time point
   * @returns The new time point
   */
  addPoint(time: number, data: D, meta?: Record<string, any>): TimePoint<D>;
  
  /**
   * Adds a transition between two time points
   * 
   * @param sourceId Source time point ID
   * @param targetId Target time point ID
   * @param transform Transition function that transforms source data to target data
   * @param duration Duration of the transition
   * @param meta Metadata about the transition
   * @returns The new transition
   */
  addTransition(
    sourceId: symbol,
    targetId: symbol,
    transform: (source: D) => D,
    duration?: number,
    meta?: Record<string, any>
  ): TimeTransition<D>;
  
  /**
   * Gets the current time point
   * 
   * @returns The current time point
   */
  getCurrentPoint(): TimePoint<D>;
  
  /**
   * Sets the current time point
   * 
   * @param id Time point ID
   * @returns The new current time point
   */
  setCurrentPoint(id: symbol): TimePoint<D>;
  
  /**
   * Gets the next time point(s) from the current point
   * 
   * @returns The next time point(s)
   */
  getNextPoints(): TimePoint<D>[];
  
  /**
   * Gets the previous time point(s) from the current point
   * 
   * @returns The previous time point(s)
   */
  getPreviousPoints(): TimePoint<D>[];
  
  /**
   * Advances the timeline to the next time point
   * 
   * @param targetId Target time point ID (if there are multiple next points)
   * @returns The new current time point
   */
  advance(targetId?: symbol): TimePoint<D>;
  
  /**
   * Rewinds the timeline to the previous time point
   * 
   * @param targetId Target time point ID (if there are multiple previous points)
   * @returns The new current time point
   */
  rewind(targetId?: symbol): TimePoint<D>;
  
  /**
   * Creates a branch from the current time point
   * 
   * @param time Timestamp or sequence number for the new branch point
   * @param data Data associated with the new branch point
   * @param meta Metadata about the new branch point
   * @returns The new branch point
   */
  createBranch(time: number, data: D, meta?: Record<string, any>): TimePoint<D>;
  
  /**
   * Merges two branches
   * 
   * @param sourceId Source branch point ID
   * @param targetId Target branch point ID
   * @param mergeFunction Function to merge data from source and target
   * @param meta Metadata about the merge
   * @returns The merged time point
   */
  mergeBranches(
    sourceId: symbol,
    targetId: symbol,
    mergeFunction: (source: D, target: D) => D,
    meta?: Record<string, any>
  ): TimePoint<D>;
  
  /**
   * Maps a function over all time points
   * 
   * @param fn Function to map
   * @returns A new timeline functor with the mapped time points
   */
  map<U>(fn: (data: D, time: number) => U): TimelineFunctor<T, TM, U>;
  
  /**
   * Filters the timeline
   * 
   * @param predicate Predicate function
   * @returns A new timeline functor with the filtered time points
   */
  filter(predicate: (data: D, time: number) => boolean): TimelineFunctor<T, TM, D>;
  
  /**
   * Folds the timeline from start to end
   * 
   * @param fn Folder function
   * @param initialValue Initial value
   * @returns The folded value
   */
  fold<U>(fn: (accumulator: U, data: D, time: number) => U, initialValue: U): U;
  
  /**
   * Creates an infinite stream from the current point
   * 
   * @param generator Function to generate the next time point
   * @returns A stream functor
   */
  toStream(generator: (previous: D, time: number) => [D, number]): StreamFunctor<T, TM, D>;
}

/**
 * Represents a Stream Functor (infinite timeline)
 * 
 * @typeParam T - Type of objects in the target category
 * @typeParam TM - Type of morphisms in the target category
 * @typeParam D - Type of data in the stream
 */
export interface StreamFunctor<
  T extends CatObject,
  TM extends Morphism<T>,
  D = any
> extends TimelineFunctor<T, TM, D> {
  /**
   * Generator function for the stream
   */
  readonly generator: (previous: D, time: number) => [D, number];
  
  /**
   * Takes the next n elements from the stream
   * 
   * @param n Number of elements to take
   * @returns The next n elements
   */
  take(n: number): D[];
  
  /**
   * Creates a new stream by applying a function to each element
   * 
   * @param fn Function to apply
   * @returns A new stream
   */
  mapStream<U>(fn: (data: D) => U): StreamFunctor<T, TM, U>;
  
  /**
   * Creates a new stream by filtering elements
   * 
   * @param predicate Predicate function
   * @returns A new stream
   */
  filterStream(predicate: (data: D) => boolean): StreamFunctor<T, TM, D>;
  
  /**
   * Zips this stream with another stream
   * 
   * @param other Other stream
   * @param zipper Function to combine elements
   * @returns A new stream
   */
  zipWith<U, R>(
    other: StreamFunctor<any, any, U>,
    zipper: (a: D, b: U) => R
  ): StreamFunctor<T, TM, R>;
}
```

## Dependencies

- `Functor`: The base functor interface that TimelineFunctor extends
- `CatObject`: Represents objects in a category
- `Morphism`: Represents morphisms (arrows) between objects in a category

## Usage

The `TimelineFunctor` interface is used to model sequential, time-ordered data structures. It can represent finite sequences, infinite streams, or branching timelines. It provides methods for navigating, manipulating, and transforming timelines.

In our conceptual framework, the Timeline Functor represents the foundation of time emergence through recursive interactions. It models how time emerges when points on a sequence start interacting with previous points, creating memory and recursion. The branching capabilities enable modeling alternative timelines, which is essential for representing different development paths in a project.

The `StreamFunctor` interface extends the Timeline Functor to represent infinite streams, which are particularly useful for modeling continuous processes or infinite sequences of concepts.

## Implementation Requirements

Implementations of the `TimelineFunctor` interface must:

1. Implement all methods from the `Functor` interface
2. Maintain collections of time points and transitions
3. Provide methods for navigating the timeline (forward, backward)
4. Support branching and merging of timelines
5. Enable functional operations like map, filter, and fold
6. Support the concept of a "current" time point
7. Enable modeling of recursive interactions between time points
8. Support the emergence of time through these recursive interactions

The implementation should reflect our understanding of how time emerges from recursive interactions in a sequence. Each transition between time points can represent a "tick" of time, and the accumulation of these ticks creates the flow of time. The branching capabilities enable modeling alternative timelines, which is essential for representing different development paths in a project.

## Categorical Foundation

The Timeline Functor is based on several categorical concepts:

1. **Monoid Structure**: The timeline forms a monoid where time points can be sequenced with an associative operation (concatenation) and an identity element (empty timeline).

2. **Directed Graph**: The timeline can be viewed as a directed graph, where time points are vertices and transitions are edges.

3. **Presheaf**: For branching timelines, the structure can be viewed as a presheaf, which is a functor from a category (representing the timeline structure) to Set (representing the data at each time point).

4. **Comonad**: For infinite streams, the structure can be viewed as a comonad, which captures the idea of an infinite sequence where you can always get the "next" element.

## File Location

This type definition should be placed in:
```
spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/__types/timeline.type.ts
```

## Related Types

- `SequenceFunctor<T, TM>`: Represents a finite sequence
- `StreamFunctor<T, TM, D>`: Represents an infinite stream
- `BranchingTimelineFunctor<T, TM, D>`: Represents a timeline with multiple branches
