# Tree Functor Specification

## Overview

A Tree Functor is a root functor that wraps a treenity tree (@treenity/core). It provides a categorical view of tree structures, mapping tree operations to categorical operations.

## Dependencies

- @treenity/core: Provides the Tree and TreeNode interfaces

## Type Definition

```typescript
interface TreeFunctor<
  T extends CatObject,
  TM extends Morphism<T>
> extends RootFunctor<CatObject, Morphism<CatObject>, T, TM> {
  readonly tree: Tree;
  getNode(id: string): T | undefined;
  addChild(parentId: string, value: any): T | undefined;
  removeNode(id: string): boolean;
}
```

## Implementation

The tree functor implementation should:

1. Extend the root functor implementation
2. Wrap a treenity tree instance
3. Map tree operations to categorical operations
4. Provide methods to manipulate the tree structure

## Usage

```typescript
// Create a tree functor
const treeFunctor = new TreeFunctor(
  targetCategory,
  (treeNode) => mapNodeToObject(treeNode),
  (treeMorphism) => mapTreeMorphismToCategoryMorphism(treeMorphism)
);

// Get a node by ID
const node = treeFunctor.getNode('node-id');

// Add a child to a parent node
const newChild = treeFunctor.addChild('parent-id', childValue);

// Remove a node
const removed = treeFunctor.removeNode('node-id');
```

## Treenity Tree Integration

The tree functor should use the treenity tree API directly, not reimplement it:

```typescript
import { createTree, Tree } from '@treenity/core';

class TreeFunctor<T extends CatObject, TM extends Morphism<T>> extends BaseRootFunctor<...> {
  readonly tree: Tree;
  
  constructor(...) {
    super(...);
    this.tree = createTree({ id: 'root', value: rootValue, children: [] });
  }
  
  getNode(id: string): T | undefined {
    const node = this.tree.getNode(id);
    if (!node) return undefined;
    return this.mapObject(node);
  }
  
  addChild(parentId: string, value: any): T | undefined {
    const newNode = this.tree.addChild(parentId, value);
    if (!newNode) return undefined;
    return this.mapObject(newNode);
  }
  
  removeNode(id: string): boolean {
    return this.tree.removeNode(id);
  }
}
```

## File Structure

- `__types/tree.type.ts`: Type definitions
- `functors/tree.rfr.ts`: Implementation
