# Timeline Types Index Specification

## Overview

This specification defines the index file that exports all timeline-related types from the cat_types package. This index serves as the entry point for importing timeline types and ensures that all components are properly exposed.

## Type Definition

```typescript
/**
 * Base timeline types
 */
export * from './base.type';

/**
 * Timeline functor
 */
export * from './timeline.type';

/**
 * Branching timeline functor
 */
export * from './branching.type';

/**
 * Stream functor
 */
export * from './stream.type';

/**
 * Timeline category
 * 
 * A category where objects are time points and morphisms are transitions
 */
export interface TimelineCategory<D = any> extends Category<TimePoint<D>, TimeTransition<D>> {
  /**
   * Gets all time points in chronological order
   * 
   * @returns All time points
   */
  getTimePoints(): TimePoint<D>[];
  
  /**
   * Gets all transitions in chronological order
   * 
   * @returns All transitions
   */
  getTimeTransitions(): TimeTransition<D>[];
}

/**
 * Creates a timeline functor
 * 
 * @param target Target category
 * @param objectMap Function that maps time points to target objects
 * @param morphismMap Function that maps time transitions to target morphisms
 * @returns A new timeline functor
 */
export function createTimelineFunctor<
  T extends CatObject,
  TM extends Morphism<T>,
  D = any
>(
  target: Category<T, TM>,
  objectMap: (timePoint: TimePoint<D>) => T,
  morphismMap: (timeTransition: TimeTransition<D>) => TM
): TimelineFunctor<T, TM, D>;

/**
 * Creates a branching timeline functor
 * 
 * @param target Target category
 * @param objectMap Function that maps time points to target objects
 * @param morphismMap Function that maps time transitions to target morphisms
 * @returns A new branching timeline functor
 */
export function createBranchingTimelineFunctor<
  T extends CatObject,
  TM extends Morphism<T>,
  D = any
>(
  target: Category<T, TM>,
  objectMap: (timePoint: TimePoint<D>) => T,
  morphismMap: (timeTransition: TimeTransition<D>) => TM
): BranchingTimelineFunctor<T, TM, D>;

/**
 * Creates a stream functor
 * 
 * @param target Target category
 * @param objectMap Function that maps time points to target objects
 * @param morphismMap Function that maps time transitions to target morphisms
 * @param generator Function to generate new elements
 * @param initialValue Initial value for the stream
 * @param initialTime Initial time for the stream
 * @returns A new stream functor
 */
export function createStreamFunctor<
  T extends CatObject,
  TM extends Morphism<T>,
  D = any
>(
  target: Category<T, TM>,
  objectMap: (timePoint: TimePoint<D>) => T,
  morphismMap: (timeTransition: TimeTransition<D>) => TM,
  generator: (previous: D, time: number) => [D, number],
  initialValue: D,
  initialTime: number = 0
): StreamFunctor<T, TM, D>;
```

## Dependencies

- `Category`: Represents a category with objects and morphisms
- `CatObject`: Represents objects in a category
- `Morphism`: Represents morphisms (arrows) between objects in a category
- `TimePoint`: Represents a point in time with associated data
- `TimeTransition`: Represents a transition between time points
- `TimelineFunctor`: Represents a timeline as a functor
- `BranchingTimelineFunctor`: Represents a timeline with multiple branches
- `StreamFunctor`: Represents an infinite stream

## Usage

This index file serves as the entry point for importing timeline-related types from the cat_types package. It exports all the base types, functor interfaces, and utility functions needed to work with timelines.

In our conceptual framework, this index represents the integration of various timeline concepts into a cohesive whole. It brings together the concepts of finite sequences, branching timelines, and infinite streams, providing a comprehensive toolkit for modeling time-based structures.

## File Location

This index file should be placed in:
```
spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/__types/timeline/index.ts
```

## Related Files

- `base.type.ts`: Defines the base timeline types
- `timeline.type.ts`: Defines the timeline functor
- `branching.type.ts`: Defines the branching timeline functor
- `stream.type.ts`: Defines the stream functor
