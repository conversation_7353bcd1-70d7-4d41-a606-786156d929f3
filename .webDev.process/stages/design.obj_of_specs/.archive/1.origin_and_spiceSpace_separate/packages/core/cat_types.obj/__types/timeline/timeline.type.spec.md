# Timeline Functor Type Specification

## Overview

This specification defines the type structure for the Timeline Functor in the cat_types package. A Timeline Functor is a specialized functor that models sequential, time-ordered data structures, building on the base timeline types.

## Type Definition

```typescript
/**
 * Represents a Timeline Functor
 * 
 * A Timeline Functor models sequential, time-ordered data structures.
 * It can represent finite sequences or branching timelines.
 * 
 * @typeParam T - Type of objects in the target category
 * @typeParam TM - Type of morphisms in the target category
 * @typeParam D - Type of data associated with time points
 */
export interface TimelineFunctor<
  T extends CatObject,
  TM extends Morphism<T>,
  D = any
> extends Functor<CatObject, Morphism<CatObject>, T, TM> {
  /**
   * The time points in the timeline
   */
  readonly points: Map<symbol, TimePoint<D>>;
  
  /**
   * The transitions between time points
   */
  readonly transitions: Map<string, TimeTransition<D>>;
  
  /**
   * The current time point
   */
  readonly currentPoint: TimePoint<D>;
  
  /**
   * Gets a time point by its ID
   * 
   * @param id Time point ID
   * @returns The time point with the given ID, or undefined if not found
   */
  getPoint(id: symbol): TimePoint<D> | undefined;
  
  /**
   * Gets time points within a time range
   * 
   * @param startTime Start time (inclusive)
   * @param endTime End time (inclusive)
   * @returns Time points within the given range
   */
  getPointsInRange(startTime: number, endTime: number): TimePoint<D>[];
  
  /**
   * Gets the transition between two time points
   * 
   * @param sourceId Source time point ID
   * @param targetId Target time point ID
   * @returns The transition between the given time points, or undefined if not found
   */
  getTransition(sourceId: symbol, targetId: symbol): TimeTransition<D> | undefined;
  
  /**
   * Adds a time point to the timeline
   * 
   * @param time Timestamp or sequence number
   * @param data Data associated with the time point
   * @param meta Metadata about the time point
   * @returns The new time point
   */
  addPoint(time: number, data: D, meta?: Record<string, any>): TimePoint<D>;
  
  /**
   * Adds a transition between two time points
   * 
   * @param sourceId Source time point ID
   * @param targetId Target time point ID
   * @param transform Transition function that transforms source data to target data
   * @param duration Duration of the transition
   * @param meta Metadata about the transition
   * @returns The new transition
   */
  addTransition(
    sourceId: symbol,
    targetId: symbol,
    transform: (source: D) => D,
    duration?: number,
    meta?: Record<string, any>
  ): TimeTransition<D>;
  
  /**
   * Gets the current time point
   * 
   * @returns The current time point
   */
  getCurrentPoint(): TimePoint<D>;
  
  /**
   * Sets the current time point
   * 
   * @param id Time point ID
   * @returns The new current time point
   */
  setCurrentPoint(id: symbol): TimePoint<D>;
  
  /**
   * Gets the next time point(s) from the current point
   * 
   * @returns The next time point(s)
   */
  getNextPoints(): TimePoint<D>[];
  
  /**
   * Gets the previous time point(s) from the current point
   * 
   * @returns The previous time point(s)
   */
  getPreviousPoints(): TimePoint<D>[];
  
  /**
   * Advances the timeline to the next time point
   * 
   * @param targetId Target time point ID (if there are multiple next points)
   * @returns The new current time point
   */
  advance(targetId?: symbol): TimePoint<D>;
  
  /**
   * Rewinds the timeline to the previous time point
   * 
   * @param targetId Target time point ID (if there are multiple previous points)
   * @returns The new current time point
   */
  rewind(targetId?: symbol): TimePoint<D>;
  
  /**
   * Maps a function over all time points
   * 
   * @param fn Function to map
   * @returns A new timeline functor with the mapped time points
   */
  map<U>(fn: (data: D, time: number) => U): TimelineFunctor<T, TM, U>;
  
  /**
   * Filters the timeline
   * 
   * @param predicate Predicate function
   * @returns A new timeline functor with the filtered time points
   */
  filter(predicate: (data: D, time: number) => boolean): TimelineFunctor<T, TM, D>;
  
  /**
   * Folds the timeline from start to end
   * 
   * @param fn Folder function
   * @param initialValue Initial value
   * @returns The folded value
   */
  fold<U>(fn: (accumulator: U, data: D, time: number) => U, initialValue: U): U;
}
```

## Dependencies

- `Functor`: The base functor interface that TimelineFunctor extends
- `CatObject`: Represents objects in a category
- `Morphism`: Represents morphisms (arrows) between objects in a category
- `TimePoint`: Represents a point in time with associated data
- `TimeTransition`: Represents a transition between time points

## Usage

The `TimelineFunctor` interface is used to model sequential, time-ordered data structures. It provides methods for navigating, manipulating, and transforming timelines.

In our conceptual framework, the Timeline Functor represents the foundation of time emergence through recursive interactions. It models how time emerges when points on a sequence start interacting with previous points, creating memory and recursion.

## Implementation Requirements

Implementations of the `TimelineFunctor` interface must:

1. Implement all methods from the `Functor` interface
2. Maintain collections of time points and transitions
3. Provide methods for navigating the timeline (forward, backward)
4. Track a "current" time point
5. Enable functional operations like map, filter, and fold
6. Support the concept of time emergence through recursive interactions

The implementation should reflect our understanding of how time emerges from recursive interactions in a sequence. Each transition between time points can represent a "tick" of time, and the accumulation of these ticks creates the flow of time.

## Categorical Foundation

The Timeline Functor is based on several categorical concepts:

1. **Monoid Structure**: The timeline forms a monoid where time points can be sequenced with an associative operation (concatenation) and an identity element (empty timeline).

2. **Directed Graph**: The timeline can be viewed as a directed graph, where time points are vertices and transitions are edges.

## File Location

This type definition should be placed in:
```
spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/__types/timeline/timeline.type.ts
```

## Related Types

- `StreamFunctor`: Extends the timeline concept to infinite streams
- `BranchingTimelineFunctor`: Extends the timeline concept to support multiple branches
