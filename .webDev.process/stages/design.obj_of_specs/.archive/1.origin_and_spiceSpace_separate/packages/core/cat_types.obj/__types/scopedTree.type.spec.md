# Scoped Tree Functor Type Specification

## Overview

This specification defines the type structure for Scoped Tree Functors in the cat_types package. A Scoped Tree Functor extends a Tree Functor with scoping capabilities, using JavaScript's prototype chain to implement scope inheritance.

The Scoped Tree Functor embodies our concept of Theory of Mind in collaborative environments. Just as team members model other team members' thought processes, scopes in a tree model the visibility and accessibility of terms across different contexts. This creates a structure where each branch is another ScopedTree (an endofunctor pattern), mirroring how mental models recursively contain other mental models.

## Type Definition

```typescript
/**
 * Scope type
 */
export type Scope = 'public' | 'protected' | 'private' | 'dynamic' | 'injected';

/**
 * Scope object
 */
export interface ScopeObject {
  [key: string]: any;
}

/**
 * Represents a Scoped Tree Functor
 *
 * A Scoped Tree Functor is an endofunctor where each branch is another ScopedTree.
 * It uses JavaScript's prototype chain to implement scope inheritance.
 * The scope IS the tree instance - a JavaScript prototypical object.
 *
 * @typeParam T - Type of objects in the target category
 * @typeParam TM - Type of morphisms in the target category
 */
export interface ScopedTreeFunctor<
  T extends CatObject,
  TM extends Morphism<T>
> extends TreeFunctor<T, TM> {
  /**
   * Global scope (public terms)
   */
  readonly globalScope: ScopeObject;

  /**
   * Gets a directory scope
   *
   * @param directory Directory path
   * @returns The scope for the directory
   */
  getDirectoryScope(directory: string): ScopeObject;

  /**
   * Gets a file scope
   *
   * @param filePath File path
   * @returns The scope for the file
   */
  getFileScope(filePath: string): ScopeObject;

  /**
   * Adds a term to a scope
   *
   * @param scope Scope to add the term to
   * @param name Term name
   * @param value Term value
   */
  addToScope(scope: ScopeObject, name: string, value: any): void;

  // No getFromScope method - use JavaScript's native object property access

  /**
   * Adds a child to a parent node with the specified scope
   *
   * @param parentId Parent node ID
   * @param value Child node value
   * @param scope Child node scope
   * @returns The new child node
   */
  addScopedChild(parentId: string, value: any, scope: Scope): T | undefined;
}
```

## Dependencies

- `TreeFunctor`: The base tree functor interface that ScopedTreeFunctor extends
- `CatObject`: Represents objects in a category
- `Morphism`: Represents morphisms (arrows) between objects in a category

## Usage

The `ScopedTreeFunctor` interface is used to provide a categorical view of scoped tree structures. It extends the tree functor with scoping capabilities, using JavaScript's prototype chain to implement scope inheritance.

In our conceptual framework, the ScopedTree represents how mental models are organized in collaborative environments. Each scope level (public, protected, private) models different levels of accessibility in team communication, just as different mental models have different levels of accessibility to other team members. The prototype chain models how these mental models inherit and extend each other, creating a recursive structure of shared understanding.

## Implementation Requirements

Implementations of the `ScopedTreeFunctor` interface must:

1. Implement all methods from the `TreeFunctor` interface
2. Use JavaScript's prototype chain with Object.create for scope inheritance
3. Use getters for lazy evaluation
4. Treat each branch as another ScopedTree (endofunctor pattern)
5. Use the scope AS the tree instance - a JavaScript prototypical object
6. Enable the modeling of recursive mental models
7. Support different levels of accessibility based on scope
8. Provide mechanisms for optimizing recursive structures (similar to HoloG)

The implementation should reflect our understanding of how Theory of Mind works in collaborative environments. Just as HoloG optimizes recursive mental models by pruning insignificant reflections, the ScopedTree should optimize access to terms based on their relevance and importance. This creates a more efficient and harmonious collaborative structure.

## JavaScript Integration

The implementation should use JavaScript's built-in features for scope inheritance:

```typescript
class ScopedTreeFunctorImpl<T extends CatObject, TM extends Morphism<T>>
  implements ScopedTreeFunctor<T, TM> {

  readonly globalScope: ScopeObject = {};
  private directoryScopes: Map<string, ScopeObject> = new Map();

  getDirectoryScope(directory: string): ScopeObject {
    if (!this.directoryScopes.has(directory)) {
      // Create a new scope that inherits from the global scope
      const scope = Object.create(this.globalScope);
      this.directoryScopes.set(directory, scope);
    }

    return this.directoryScopes.get(directory)!;
  }

  addToScope(scope: ScopeObject, name: string, value: any): void {
    // Define the property as a getter to ensure lazy evaluation
    Object.defineProperty(scope, name, {
      get: () => value,
      enumerable: true,
      configurable: true
    });
  }

  // Other methods...
}
```

## File Location

This type definition should be placed in:
```
spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/__types/scopedTree.type.ts
```

## Related Types

- `Scope`: Type alias for the different scope types
- `ScopeObject`: Interface for scope objects
