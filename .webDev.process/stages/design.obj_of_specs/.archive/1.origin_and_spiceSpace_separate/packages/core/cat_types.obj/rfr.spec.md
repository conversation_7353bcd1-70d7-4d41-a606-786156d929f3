# Root Functor (RFR) Specification

## Overview

A Root Functor is a special kind of functor that creates endofunctors but doesn't inherit from any. It forms the root of a tree of endofunctors. Root functors can be extended to create new root functors, forming a hierarchy.

## Type Definition

```typescript
interface RootFunctor<
  S extends CatObject,
  SM extends Morphism<S>,
  T extends CatObject,
  TM extends Morphism<T>
> extends Functor<S, SM, T, TM> {
  createEndofunctor(
    objectMap: (obj: T) => T,
    morphismMap: (morphism: TM) => TM
  ): Functor<T, TM, T, TM>;
  
  readonly parent?: RootFunctor<any, any, S, SM>;
}
```

## Implementation

The root functor implementation should:

1. Extend the base functor implementation
2. Store a reference to its parent root functor, if any
3. Provide a method to create endofunctors on its target category
4. Allow extension to create new root functors

## Usage

```typescript
// Create a root functor
const rootFunctor = new BaseRootFunctor(
  sourceCategory,
  targetCategory,
  (obj) => mapObjectFunction(obj),
  (morphism) => mapMorphismFunction(morphism)
);

// Create an endofunctor on the target category
const endofunctor = rootFunctor.createEndofunctor(
  (obj) => transformObjectFunction(obj),
  (morphism) => transformMorphismFunction(morphism)
);

// Extend the root functor to create a new root functor
const extendedRootFunctor = rootFunctor.extend(
  newTargetCategory,
  (obj) => mapToNewTargetFunction(obj),
  (morphism) => mapToNewTargetMorphismFunction(morphism)
);
```

## Hierarchy

Root functors form a hierarchy:

```
OriginRootFunctor
  ├── TreeRootFunctor
  │     └── ScopedTreeRootFunctor
  └── OtherRootFunctor
```

Each root functor in the hierarchy extends its parent, adding new functionality while preserving the core categorical structure.

## File Structure

- `__types/rfr.type.ts`: Type definitions
- `functors/rfr.fr.ts`: Implementation
