# Root Functor (RFR) Implementation Specification

## Overview

This specification defines the implementation details for Root Functors in the cat_types package. A Root Functor is a special kind of functor that creates endofunctors but doesn't inherit from any. It forms the root of a tree of endofunctors and can be extended to create new root functors.

## Class Definition

```typescript
/**
 * Base implementation of a Root Functor
 * 
 * @typeParam S - Type of objects in the source category
 * @typeParam SM - Type of morphisms in the source category
 * @typeParam T - Type of objects in the target category
 * @typeParam TM - Type of morphisms in the target category
 */
export class BaseRootFunctor<
  S extends CatObject,
  SM extends Morphism<S>,
  T extends CatObject,
  TM extends Morphism<T>
> extends BaseFunctor<S, SM, T, TM> implements RootFunctor<S, SM, T, TM> {
  /**
   * Parent root functor, if any
   */
  readonly parent?: RootFunctor<any, any, S, SM>;
  
  /**
   * Creates a new root functor
   * 
   * @param source Source category
   * @param target Target category
   * @param objectMap Function that maps objects from source to target
   * @param morphismMap Function that maps morphisms from source to target
   * @param parent Parent root functor, if any
   */
  constructor(
    source: Category<S, SM>,
    target: Category<T, TM>,
    objectMap: (obj: S) => T,
    morphismMap: (morphism: SM) => TM,
    parent?: RootFunctor<any, any, S, SM>
  ) {
    super(source, target, objectMap, morphismMap);
    this.parent = parent;
  }
  
  /**
   * Creates an endofunctor on the target category
   * 
   * @param objectMap Function that maps objects within the target category
   * @param morphismMap Function that maps morphisms within the target category
   * @returns An endofunctor on the target category
   */
  createEndofunctor(
    objectMap: (obj: T) => T,
    morphismMap: (morphism: TM) => TM
  ): Functor<T, TM, T, TM> {
    return new BaseFunctor(
      this.target,
      this.target,
      objectMap,
      morphismMap
    );
  }
  
  /**
   * Extends this root functor to create a new root functor
   * 
   * @param target Target category
   * @param objectMap Function that maps objects from this target to the new target
   * @param morphismMap Function that maps morphisms from this target to the new target
   * @returns A new root functor that extends this one
   */
  extend<U extends CatObject, UM extends Morphism<U>>(
    target: Category<U, UM>,
    objectMap: (obj: T) => U,
    morphismMap: (morphism: TM) => UM
  ): RootFunctor<T, TM, U, UM> {
    return new BaseRootFunctor(
      this.target,
      target,
      objectMap,
      morphismMap,
      this
    );
  }
}
```

## Utility Function

```typescript
/**
 * Creates a root functor
 * 
 * @param source Source category
 * @param target Target category
 * @param objectMap Function that maps objects from source to target
 * @param morphismMap Function that maps morphisms from source to target
 * @param parent Parent root functor, if any
 * @returns A new root functor
 */
export function createRootFunctor<
  S extends CatObject,
  SM extends Morphism<S>,
  T extends CatObject,
  TM extends Morphism<T>
>(
  source: Category<S, SM>,
  target: Category<T, TM>,
  objectMap: (obj: S) => T,
  morphismMap: (morphism: SM) => TM,
  parent?: RootFunctor<any, any, S, SM>
): RootFunctor<S, SM, T, TM> {
  return new BaseRootFunctor(
    source,
    target,
    objectMap,
    morphismMap,
    parent
  );
}
```

## Implementation Details

### Constructor

The constructor takes five parameters:
1. `source`: The source category
2. `target`: The target category
3. `objectMap`: A function that maps objects from the source category to the target category
4. `morphismMap`: A function that maps morphisms from the source category to the target category
5. `parent`: The parent root functor, if any

It calls the super constructor with the first four parameters and stores the parent.

### createEndofunctor

The `createEndofunctor` method creates a new endofunctor on the target category:

1. It creates a new `BaseFunctor` with the target category as both source and target
2. It uses the provided object and morphism mapping functions
3. It returns the new functor

### extend

The `extend` method creates a new root functor that extends this one:

1. It creates a new `BaseRootFunctor` with this functor's target as the source
2. It uses the provided target category and mapping functions
3. It sets this functor as the parent of the new functor
4. It returns the new functor

## File Location

This implementation should be placed in:
```
spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/functors/rfr.fr.ts
```

## Dependencies

- `__types/rfr.type.ts`: Type definitions for root functors
- `functor.ts`: Functor implementation
- `cat.ts`: Category implementation
- `catObject.ts`: Category object implementation
- `morphism.ts`: Morphism implementation
