# Branching Timeline Functor Type Specification

## Overview

This specification defines the type structure for the Branching Timeline Functor in the cat_types package. A Branching Timeline Functor extends the basic Timeline Functor to support multiple branches, enabling the modeling of alternative timelines and parallel development paths.

## Type Definition

```typescript
/**
 * Represents a branch in a timeline
 * 
 * @typeParam D - Type of data associated with time points
 */
export interface TimelineBranch<D = any> {
  /**
   * Unique identifier for the branch
   */
  readonly id: symbol;
  
  /**
   * Name of the branch
   */
  readonly name: string;
  
  /**
   * Time point where the branch starts
   */
  readonly startPoint: TimePoint<D>;
  
  /**
   * Time points in the branch
   */
  readonly points: Set<symbol>;
  
  /**
   * Parent branch, if any
   */
  readonly parent?: TimelineBranch<D>;
  
  /**
   * Metadata about the branch
   */
  readonly meta?: Record<string, any>;
}

/**
 * Represents a Branching Timeline Functor
 * 
 * A Branching Timeline Functor extends the basic Timeline Functor
 * to support multiple branches, enabling the modeling of alternative
 * timelines and parallel development paths.
 * 
 * @typeParam T - Type of objects in the target category
 * @typeParam TM - Type of morphisms in the target category
 * @typeParam D - Type of data associated with time points
 */
export interface BranchingTimelineFunctor<
  T extends CatObject,
  TM extends Morphism<T>,
  D = any
> extends TimelineFunctor<T, TM, D> {
  /**
   * The branches in the timeline
   */
  readonly branches: Map<symbol, TimelineBranch<D>>;
  
  /**
   * The current branch
   */
  readonly currentBranch: TimelineBranch<D>;
  
  /**
   * Gets a branch by its ID
   * 
   * @param id Branch ID
   * @returns The branch with the given ID, or undefined if not found
   */
  getBranch(id: symbol): TimelineBranch<D> | undefined;
  
  /**
   * Gets a branch by its name
   * 
   * @param name Branch name
   * @returns The branch with the given name, or undefined if not found
   */
  getBranchByName(name: string): TimelineBranch<D> | undefined;
  
  /**
   * Creates a new branch from the current time point
   * 
   * @param name Branch name
   * @param meta Metadata about the branch
   * @returns The new branch
   */
  createBranch(name: string, meta?: Record<string, any>): TimelineBranch<D>;
  
  /**
   * Switches to a different branch
   * 
   * @param branchId Branch ID
   * @returns The new current branch
   */
  switchBranch(branchId: symbol): TimelineBranch<D>;
  
  /**
   * Merges two branches
   * 
   * @param sourceBranchId Source branch ID
   * @param targetBranchId Target branch ID
   * @param mergePoint Time point where the merge should occur
   * @param mergeFunction Function to merge data from source and target
   * @param meta Metadata about the merge
   * @returns The merged time point
   */
  mergeBranches(
    sourceBranchId: symbol,
    targetBranchId: symbol,
    mergePoint: number,
    mergeFunction: (source: D, target: D) => D,
    meta?: Record<string, any>
  ): TimePoint<D>;
  
  /**
   * Gets the common ancestor of two branches
   * 
   * @param branch1Id First branch ID
   * @param branch2Id Second branch ID
   * @returns The common ancestor time point, or undefined if none exists
   */
  getCommonAncestor(branch1Id: symbol, branch2Id: symbol): TimePoint<D> | undefined;
  
  /**
   * Gets all branches that descend from a given branch
   * 
   * @param branchId Branch ID
   * @returns Descendant branches
   */
  getDescendantBranches(branchId: symbol): TimelineBranch<D>[];
  
  /**
   * Gets the branch that contains a given time point
   * 
   * @param pointId Time point ID
   * @returns The branch containing the time point, or undefined if none exists
   */
  getBranchForPoint(pointId: symbol): TimelineBranch<D> | undefined;
}
```

## Dependencies

- `TimelineFunctor`: The base timeline functor that BranchingTimelineFunctor extends
- `TimePoint`: Represents a point in time with associated data
- `CatObject`: Represents objects in a category
- `Morphism`: Represents morphisms (arrows) between objects in a category

## Usage

The `BranchingTimelineFunctor` interface is used to model timelines with multiple branches, enabling the representation of alternative development paths, version control branches, or any other scenario where a timeline can split and potentially merge.

In our conceptual framework, the Branching Timeline Functor represents how different development paths can evolve independently while still being part of the same project. This aligns with our concept of branch-specific documentation and development processes, where each branch has its own ideation and design stages, ticking at its own pace while still being synchronized to the team clock.

## Implementation Requirements

Implementations of the `BranchingTimelineFunctor` interface must:

1. Implement all methods from the `TimelineFunctor` interface
2. Maintain a collection of branches
3. Track a "current" branch
4. Provide methods for creating, switching, and merging branches
5. Support finding common ancestors and descendant branches
6. Enable mapping between time points and their containing branches

The implementation should reflect our understanding of how different development paths can evolve independently while still being part of the same project. Branches should be able to diverge and converge, modeling the natural flow of development in a team environment.

## Categorical Foundation

The Branching Timeline Functor is based on several categorical concepts:

1. **Presheaf**: The branching structure can be viewed as a presheaf, which is a functor from a category (representing the timeline structure) to Set (representing the data at each time point).

2. **Pullback**: Branch merges can be modeled as pullbacks, which represent the "best" way to combine two objects with a common ancestor.

## File Location

This type definition should be placed in:
```
spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/__types/timeline/branching.type.ts
```

## Related Types

- `TimelineFunctor`: The base timeline functor that BranchingTimelineFunctor extends
- `StreamFunctor`: Represents infinite streams, which can be used alongside branching timelines
