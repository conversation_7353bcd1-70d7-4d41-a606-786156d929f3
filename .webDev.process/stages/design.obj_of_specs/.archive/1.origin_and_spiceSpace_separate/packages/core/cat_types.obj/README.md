# Categorical Types Object (cat_types.obj)

## Overview

The Categorical Types Object (cat_types.obj) is a fundamental component of the Origin Reference Frame (origin.rfr) in the SpiceTime architecture. It defines the types used throughout the system, organized categorically, providing a foundation for type-safe operations and transformations.

## Structure

```
cat_types.obj
├── src               # Source code
│   ├── atomic        # Atomic types
│   │   ├── primitive.ts  # Primitive types
│   │   ├── unit.ts       # Unit types
│   │   ├── void.ts       # Void types
│   │   └── index.ts      # Exports
│   ├── composite     # Composite types
│   │   ├── product.ts    # Product types
│   │   ├── sum.ts        # Sum types
│   │   ├── function.ts   # Function types
│   │   └── index.ts      # Exports
│   ├── functional    # Functional types
│   │   ├── functor.ts    # Functor types
│   │   ├── applicative.ts # Applicative types
│   │   ├── monad.ts      # Monad types
│   │   └── index.ts      # Exports
│   └── index.ts      # Main exports
├── test              # Tests
│   ├── atomic        # Atomic type tests
│   ├── composite     # Composite type tests
│   ├── functional    # Functional type tests
│   └── index.test.ts # Main tests
└── docs              # Documentation
    ├── api.md        # API documentation
    ├── examples.md   # Examples
    └── concepts.md   # Conceptual documentation
```

## Atomic Types

Atomic types are the fundamental, indivisible units of the type system. They form the building blocks for more complex types.

### Primitive Types

Primitive types are the basic types provided by the underlying language:

```typescript
interface PrimitiveType<T> extends Type<T> {
  kind: 'primitive';
  name: string;
}

const stringType: PrimitiveType<string> = {
  kind: 'primitive',
  name: 'string',
  validate: (value): value is string => typeof value === 'string',
  serialize: (value) => value,
  deserialize: (value) => value as string
};

const numberType: PrimitiveType<number> = {
  kind: 'primitive',
  name: 'number',
  validate: (value): value is number => typeof value === 'number',
  serialize: (value) => value,
  deserialize: (value) => value as number
};

const booleanType: PrimitiveType<boolean> = {
  kind: 'primitive',
  name: 'boolean',
  validate: (value): value is boolean => typeof value === 'boolean',
  serialize: (value) => value,
  deserialize: (value) => value as boolean
};
```

### Unit Types

Unit types are types with exactly one value, used for signaling:

```typescript
interface UnitType<T> extends Type<T> {
  kind: 'unit';
  name: string;
  value: T;
}

const nullType: UnitType<null> = {
  kind: 'unit',
  name: 'null',
  value: null,
  validate: (value): value is null => value === null,
  serialize: () => null,
  deserialize: () => null
};

const undefinedType: UnitType<undefined> = {
  kind: 'unit',
  name: 'undefined',
  value: undefined,
  validate: (value): value is undefined => value === undefined,
  serialize: () => undefined,
  deserialize: () => undefined
};
```

### Void Types

Void types are types with no values, used for error handling:

```typescript
interface VoidType extends Type<never> {
  kind: 'void';
  name: string;
}

const neverType: VoidType = {
  kind: 'void',
  name: 'never',
  validate: (_): _ is never => false,
  serialize: () => {
    throw new Error('Cannot serialize never');
  },
  deserialize: () => {
    throw new Error('Cannot deserialize never');
  }
};
```

## Composite Types

Composite types are combinations of atomic types to form more complex structures. They allow for the creation of rich, structured data types.

### Product Types

Product types combine multiple values, like records or tuples:

```typescript
interface ProductType<T> extends Type<T> {
  kind: 'product';
  name: string;
  fields: Record<string, Type<any>>;
}

function createProductType<T extends Record<string, any>>(
  name: string,
  fields: { [K in keyof T]: Type<T[K]> }
): ProductType<T> {
  return {
    kind: 'product',
    name,
    fields,
    validate: (value): value is T => {
      if (typeof value !== 'object' || value === null) {
        return false;
      }
      
      for (const [key, type] of Object.entries(fields)) {
        if (!type.validate(value[key])) {
          return false;
        }
      }
      
      return true;
    },
    serialize: (value) => {
      const result: Record<string, any> = {};
      
      for (const [key, type] of Object.entries(fields)) {
        result[key] = type.serialize(value[key]);
      }
      
      return result;
    },
    deserialize: (value) => {
      const result: Record<string, any> = {};
      
      for (const [key, type] of Object.entries(fields)) {
        result[key] = type.deserialize(value[key]);
      }
      
      return result as T;
    }
  };
}
```

### Sum Types

Sum types represent alternatives, like unions or enums:

```typescript
interface SumType<T> extends Type<T> {
  kind: 'sum';
  name: string;
  variants: Type<any>[];
}

function createSumType<T>(
  name: string,
  variants: Type<T>[]
): SumType<T> {
  return {
    kind: 'sum',
    name,
    variants,
    validate: (value): value is T => {
      return variants.some(variant => variant.validate(value));
    },
    serialize: (value) => {
      for (const variant of variants) {
        if (variant.validate(value)) {
          return variant.serialize(value);
        }
      }
      
      throw new Error(`Cannot serialize value with sum type ${name}`);
    },
    deserialize: (value) => {
      for (const variant of variants) {
        try {
          return variant.deserialize(value);
        } catch (error) {
          // Try the next variant
        }
      }
      
      throw new Error(`Cannot deserialize value with sum type ${name}`);
    }
  };
}
```

### Function Types

Function types represent functions from one type to another:

```typescript
interface FunctionType<T extends (...args: any[]) => any> extends Type<T> {
  kind: 'function';
  name: string;
  parameters: Type<Parameters<T>[number]>[];
  returnType: Type<ReturnType<T>>;
}

function createFunctionType<T extends (...args: any[]) => any>(
  name: string,
  parameters: Type<Parameters<T>[number]>[],
  returnType: Type<ReturnType<T>>
): FunctionType<T> {
  return {
    kind: 'function',
    name,
    parameters,
    returnType,
    validate: (value): value is T => {
      return typeof value === 'function';
    },
    serialize: () => {
      throw new Error('Cannot serialize functions');
    },
    deserialize: () => {
      throw new Error('Cannot deserialize functions');
    }
  };
}
```

## Functional Types

Functional types are higher-order types that operate on other types. They provide a foundation for functional programming concepts.

### Functor Types

Functor types are types that can be mapped over:

```typescript
interface FunctorType<F extends Functor<any>> extends Type<F> {
  kind: 'functor';
  name: string;
  map: <A, B>(fa: F<A>, f: (a: A) => B) => F<B>;
}

function createFunctorType<F extends Functor<any>>(
  name: string,
  map: <A, B>(fa: F<A>, f: (a: A) => B) => F<B>,
  validate: (value: any) => value is F<any>,
  serialize: (value: F<any>) => any,
  deserialize: (value: any) => F<any>
): FunctorType<F> {
  return {
    kind: 'functor',
    name,
    map,
    validate,
    serialize,
    deserialize
  };
}
```

### Applicative Types

Applicative types are types that can apply functions wrapped in the type:

```typescript
interface ApplicativeType<F extends Applicative<any>> extends FunctorType<F> {
  kind: 'applicative';
  name: string;
  of: <A>(a: A) => F<A>;
  ap: <A, B>(ff: F<(a: A) => B>, fa: F<A>) => F<B>;
}

function createApplicativeType<F extends Applicative<any>>(
  name: string,
  map: <A, B>(fa: F<A>, f: (a: A) => B) => F<B>,
  of: <A>(a: A) => F<A>,
  ap: <A, B>(ff: F<(a: A) => B>, fa: F<A>) => F<B>,
  validate: (value: any) => value is F<any>,
  serialize: (value: F<any>) => any,
  deserialize: (value: any) => F<any>
): ApplicativeType<F> {
  return {
    kind: 'applicative',
    name,
    map,
    of,
    ap,
    validate,
    serialize,
    deserialize
  };
}
```

### Monad Types

Monad types are types that can be chained together:

```typescript
interface MonadType<F extends Monad<any>> extends ApplicativeType<F> {
  kind: 'monad';
  name: string;
  chain: <A, B>(fa: F<A>, f: (a: A) => F<B>) => F<B>;
}

function createMonadType<F extends Monad<any>>(
  name: string,
  map: <A, B>(fa: F<A>, f: (a: A) => B) => F<B>,
  of: <A>(a: A) => F<A>,
  ap: <A, B>(ff: F<(a: A) => B>, fa: F<A>) => F<B>,
  chain: <A, B>(fa: F<A>, f: (a: A) => F<B>) => F<B>,
  validate: (value: any) => value is F<any>,
  serialize: (value: F<any>) => any,
  deserialize: (value: any) => F<any>
): MonadType<F> {
  return {
    kind: 'monad',
    name,
    map,
    of,
    ap,
    chain,
    validate,
    serialize,
    deserialize
  };
}
```

## Type Operations

The Categorical Types Object provides operations for working with types:

### Type Composition

Compose two types to create a new type:

```typescript
function composeTypes<A, B>(a: Type<A>, b: Type<B>): Type<[A, B]> {
  return {
    kind: 'product',
    name: `[${a.name}, ${b.name}]`,
    validate: (value): value is [A, B] => {
      return Array.isArray(value) && 
             value.length === 2 && 
             a.validate(value[0]) && 
             b.validate(value[1]);
    },
    serialize: (value) => [a.serialize(value[0]), b.serialize(value[1])],
    deserialize: (value) => [a.deserialize(value[0]), b.deserialize(value[1])]
  };
}
```

### Type Mapping

Map a function over a type to create a new type:

```typescript
function mapType<A, B>(type: Type<A>, f: (a: A) => B, g: (b: B) => A): Type<B> {
  return {
    kind: 'mapped',
    name: `${type.name} -> mapped`,
    validate: (value): value is B => {
      try {
        return type.validate(g(value));
      } catch (error) {
        return false;
      }
    },
    serialize: (value) => type.serialize(g(value)),
    deserialize: (value) => f(type.deserialize(value))
  };
}
```

### Type Validation

Validate a value against a type:

```typescript
function validateType<T>(type: Type<T>, value: any): value is T {
  return type.validate(value);
}
```

### Type Serialization

Serialize a value using a type:

```typescript
function serializeType<T>(type: Type<T>, value: T): any {
  return type.serialize(value);
}
```

### Type Deserialization

Deserialize a value using a type:

```typescript
function deserializeType<T>(type: Type<T>, value: any): T {
  return type.deserialize(value);
}
```

## Integration with Other Modules

The Categorical Types Object integrates with other modules in the following ways:

1. **Process Reference Frame**: Provides types for processes and their inputs/outputs.
2. **SpiceTime Version Space**: Provides types for version nodes and their values.
3. **Error Handling**: Uses the error module for type validation errors.

## API

The Categorical Types Object provides the following API:

```typescript
// Type interface
export interface Type<T> {
  kind: string;
  name: string;
  validate: (value: any) => value is T;
  serialize: (value: T) => any;
  deserialize: (value: any) => T;
}

// Atomic types
export const stringType: Type<string>;
export const numberType: Type<number>;
export const booleanType: Type<boolean>;
export const nullType: Type<null>;
export const undefinedType: Type<undefined>;
export const neverType: Type<never>;

// Composite type factories
export function createProductType<T extends Record<string, any>>(
  name: string,
  fields: { [K in keyof T]: Type<T[K]> }
): Type<T>;

export function createSumType<T>(
  name: string,
  variants: Type<T>[]
): Type<T>;

export function createFunctionType<T extends (...args: any[]) => any>(
  name: string,
  parameters: Type<Parameters<T>[number]>[],
  returnType: Type<ReturnType<T>>
): Type<T>;

// Functional type factories
export function createFunctorType<F extends Functor<any>>(
  name: string,
  map: <A, B>(fa: F<A>, f: (a: A) => B) => F<B>,
  validate: (value: any) => value is F<any>,
  serialize: (value: F<any>) => any,
  deserialize: (value: any) => F<any>
): Type<F>;

export function createApplicativeType<F extends Applicative<any>>(
  name: string,
  map: <A, B>(fa: F<A>, f: (a: A) => B) => F<B>,
  of: <A>(a: A) => F<A>,
  ap: <A, B>(ff: F<(a: A) => B>, fa: F<A>) => F<B>,
  validate: (value: any) => value is F<any>,
  serialize: (value: F<any>) => any,
  deserialize: (value: any) => F<any>
): Type<F>;

export function createMonadType<F extends Monad<any>>(
  name: string,
  map: <A, B>(fa: F<A>, f: (a: A) => B) => F<B>,
  of: <A>(a: A) => F<A>,
  ap: <A, B>(ff: F<(a: A) => B>, fa: F<A>) => F<B>,
  chain: <A, B>(fa: F<A>, f: (a: A) => F<B>) => F<B>,
  validate: (value: any) => value is F<any>,
  serialize: (value: F<any>) => any,
  deserialize: (value: any) => F<any>
): Type<F>;

// Type operations
export function composeTypes<A, B>(a: Type<A>, b: Type<B>): Type<[A, B]>;
export function mapType<A, B>(type: Type<A>, f: (a: A) => B, g: (b: B) => A): Type<B>;
export function validateType<T>(type: Type<T>, value: any): value is T;
export function serializeType<T>(type: Type<T>, value: T): any;
export function deserializeType<T>(type: Type<T>, value: any): T;
```

## Examples

### Creating and Using Product Types

```typescript
import { createProductType, stringType, numberType } from '@spicetime/core/origin.rfr/cat_types.obj';

// Create a product type
const personType = createProductType('Person', {
  name: stringType,
  age: numberType
});

// Validate a value
const person = { name: 'John', age: 30 };
if (personType.validate(person)) {
  console.log('Valid person:', person);
} else {
  console.log('Invalid person');
}

// Serialize a value
const serialized = personType.serialize(person);
console.log('Serialized:', serialized);

// Deserialize a value
const deserialized = personType.deserialize(serialized);
console.log('Deserialized:', deserialized);
```

### Creating and Using Sum Types

```typescript
import { createSumType, stringType, numberType } from '@spicetime/core/origin.rfr/cat_types.obj';

// Create a sum type
const idType = createSumType('ID', [stringType, numberType]);

// Validate values
const stringId = 'abc123';
const numberId = 42;
const invalidId = true;

console.log('String ID valid:', idType.validate(stringId)); // true
console.log('Number ID valid:', idType.validate(numberId)); // true
console.log('Invalid ID valid:', idType.validate(invalidId)); // false

// Serialize values
const serializedStringId = idType.serialize(stringId);
const serializedNumberId = idType.serialize(numberId);

console.log('Serialized string ID:', serializedStringId);
console.log('Serialized number ID:', serializedNumberId);

// Deserialize values
const deserializedStringId = idType.deserialize(serializedStringId);
const deserializedNumberId = idType.deserialize(serializedNumberId);

console.log('Deserialized string ID:', deserializedStringId);
console.log('Deserialized number ID:', deserializedNumberId);
```

### Creating and Using Functor Types

```typescript
import { createFunctorType } from '@spicetime/core/origin.rfr/cat_types.obj';

// Define an array functor
const arrayFunctor = createFunctorType<Array<any>>(
  'Array',
  (fa, f) => fa.map(f),
  (value): value is Array<any> => Array.isArray(value),
  (value) => value,
  (value) => value
);

// Use the functor
const numbers = [1, 2, 3, 4, 5];
const doubled = arrayFunctor.map(numbers, x => x * 2);

console.log('Original:', numbers); // [1, 2, 3, 4, 5]
console.log('Doubled:', doubled); // [2, 4, 6, 8, 10]
```

## Next Steps

1. **Document the atomic types**: Create detailed documentation for the atomic types.
2. **Document the composite types**: Create detailed documentation for the composite types.
3. **Document the functional types**: Create detailed documentation for the functional types.
4. **Create API specifications**: Define the API for each module in the cat_types.obj.
5. **Create schema definitions**: Define the schemas for the data structures used in the cat_types.obj.
6. **Create examples**: Provide examples of using the cat_types.obj in different scenarios.
