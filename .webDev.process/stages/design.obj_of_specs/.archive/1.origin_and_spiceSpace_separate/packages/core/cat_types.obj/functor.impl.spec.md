# Functor Implementation Specification

## Overview

This specification defines the implementation details for functors in the cat_types package. A functor is a mapping between categories that preserves the categorical structure, mapping objects to objects and morphisms to morphisms while preserving identity and composition.

## Class Definition

```typescript
/**
 * Base implementation of a functor
 * 
 * @typeParam S - Type of objects in the source category
 * @typeParam SM - Type of morphisms in the source category
 * @typeParam T - Type of objects in the target category
 * @typeParam TM - Type of morphisms in the target category
 */
export class BaseFunctor<
  S extends CatObject,
  SM extends Morphism<S>,
  T extends CatObject,
  TM extends Morphism<T>
> implements Functor<S, SM, T, TM> {
  /**
   * Source category
   */
  readonly source: Category<S, SM>;
  
  /**
   * Target category
   */
  readonly target: Category<T, TM>;
  
  /**
   * Object mapping function
   */
  private objectMap: (obj: S) => T;
  
  /**
   * Morphism mapping function
   */
  private morphismMap: (morphism: SM) => TM;
  
  /**
   * Children of this functor (functors that inherit from it)
   */
  readonly children: Set<Functor<T, TM, any, any>> = new Set();
  
  /**
   * Storage provider for children
   */
  private childrenStorageProvider?: FunctorChildrenStorageProvider<S, SM, T, TM>;
  
  /**
   * Creates a new functor
   * 
   * @param source Source category
   * @param target Target category
   * @param objectMap Function that maps objects from source to target
   * @param morphismMap Function that maps morphisms from source to target
   */
  constructor(
    source: Category<S, SM>,
    target: Category<T, TM>,
    objectMap: (obj: S) => T,
    morphismMap: (morphism: SM) => TM
  ) {
    this.source = source;
    this.target = target;
    this.objectMap = objectMap;
    this.morphismMap = morphismMap;
  }
  
  /**
   * Maps an object from the source category to the target category
   * 
   * @param obj Object in the source category
   * @returns Corresponding object in the target category
   */
  mapObject(obj: S): T {
    return this.objectMap(obj);
  }
  
  /**
   * Maps a morphism from the source category to the target category
   * 
   * @param morphism Morphism in the source category
   * @returns Corresponding morphism in the target category
   */
  mapMorphism(morphism: SM): TM {
    return this.morphismMap(morphism);
  }
  
  /**
   * Validates that the functor laws hold
   * 
   * 1. F(id_A) = id_F(A) (Identity morphisms map to identity morphisms)
   * 2. F(g ∘ f) = F(g) ∘ F(f) (Composition is preserved)
   * 
   * @returns True if the functor laws are satisfied
   */
  validateLaws(): boolean {
    // Get all objects in the source category
    const sourceObjects = this.source.getObjects();
    
    // Check the identity law for each object
    for (const obj of sourceObjects) {
      const sourceId = this.source.id(obj);
      const targetObj = this.mapObject(obj);
      const targetId = this.target.id(targetObj);
      const mappedSourceId = this.mapMorphism(sourceId);
      
      // Check if F(id_A) = id_F(A)
      if (mappedSourceId.apply !== targetId.apply) {
        return false;
      }
    }
    
    // Get all morphisms in the source category
    const sourceMorphisms = this.source.getMorphisms();
    
    // Check the composition law for each pair of composable morphisms
    for (const [_, f] of sourceMorphisms) {
      for (const [__, g] of sourceMorphisms) {
        // Check if f and g are composable
        if (f.target.id.id === g.source.id.id) {
          // Compose in the source category
          const sourceComposition = this.source.compose(f, g);
          
          // Map the morphisms and compose in the target category
          const mappedF = this.mapMorphism(f);
          const mappedG = this.mapMorphism(g);
          const targetComposition = this.target.compose(mappedF, mappedG);
          
          // Map the composition
          const mappedSourceComposition = this.mapMorphism(sourceComposition);
          
          // Check if F(g ∘ f) = F(g) ∘ F(f)
          if (mappedSourceComposition.apply !== targetComposition.apply) {
            return false;
          }
        }
      }
    }
    
    return true;
  }
  
  /**
   * Adds a child functor
   * 
   * @param child Child functor
   */
  addChild(child: Functor<T, TM, any, any>): void {
    if (this.childrenStorageProvider) {
      this.childrenStorageProvider.addChild(this, child);
    } else {
      this.children.add(child);
    }
  }
  
  /**
   * Sets the storage provider for children
   * 
   * @param provider Storage provider
   */
  setChildrenStorageProvider(provider: FunctorChildrenStorageProvider<S, SM, T, TM>): void {
    // If we already have children, migrate them to the new provider
    if (this.childrenStorageProvider === undefined && this.children.size > 0) {
      for (const child of this.children) {
        provider.addChild(this, child);
      }
      this.children.clear();
    }
    
    this.childrenStorageProvider = provider;
  }
}
```

## Storage Provider Implementation

```typescript
/**
 * In-memory storage provider for functor children
 * 
 * @typeParam S - Type of objects in the source category
 * @typeParam SM - Type of morphisms in the source category
 * @typeParam T - Type of objects in the target category
 * @typeParam TM - Type of morphisms in the target category
 */
export class InMemoryFunctorChildrenStorageProvider<
  S extends CatObject,
  SM extends Morphism<S>,
  T extends CatObject,
  TM extends Morphism<T>
> implements FunctorChildrenStorageProvider<S, SM, T, TM> {
  /**
   * Map of functors to their children
   */
  private childrenMap: Map<Functor<S, SM, T, TM>, Set<Functor<T, TM, any, any>>> = new Map();
  
  /**
   * Gets all children of a functor
   * 
   * @param functor The functor
   * @returns The children of the functor
   */
  getChildren(functor: Functor<S, SM, T, TM>): Set<Functor<T, TM, any, any>> {
    if (!this.childrenMap.has(functor)) {
      this.childrenMap.set(functor, new Set());
    }
    
    return this.childrenMap.get(functor)!;
  }
  
  /**
   * Adds a child to a functor
   * 
   * @param functor The functor
   * @param child The child functor
   */
  addChild(functor: Functor<S, SM, T, TM>, child: Functor<T, TM, any, any>): void {
    const children = this.getChildren(functor);
    children.add(child);
  }
  
  /**
   * Removes a child from a functor
   * 
   * @param functor The functor
   * @param child The child functor
   */
  removeChild(functor: Functor<S, SM, T, TM>, child: Functor<T, TM, any, any>): void {
    const children = this.getChildren(functor);
    children.delete(child);
  }
}
```

## Utility Functions

```typescript
/**
 * Creates an endofunctor (a functor from a category to itself)
 * 
 * @param category The category
 * @param objectMap Function that maps objects within the category
 * @param morphismMap Function that maps morphisms within the category
 * @returns An endofunctor on the category
 */
export function createEndofunctor<
  O extends CatObject,
  M extends Morphism<O>
>(
  category: Category<O, M>,
  objectMap: (obj: O) => O,
  morphismMap: (morphism: M) => M
): Functor<O, M, O, M> {
  return new BaseFunctor(
    category,
    category,
    objectMap,
    morphismMap
  );
}

/**
 * Identity functor (maps each object and morphism to itself)
 * 
 * @param category The category
 * @returns The identity functor on the category
 */
export function identityFunctor<
  O extends CatObject,
  M extends Morphism<O>
>(category: Category<O, M>): Functor<O, M, O, M> {
  return createEndofunctor(
    category,
    obj => obj,
    morphism => morphism
  );
}
```

## Implementation Details

### Child Tracking

The functor implementation includes child tracking with these key features:

1. **In-Memory Storage by Default**: Children are stored in a `Set` by default
2. **Pluggable Storage**: The storage can be replaced with any implementation of `FunctorChildrenStorageProvider`
3. **Migration**: When a storage provider is set, existing children are migrated to the new provider
4. **Extensibility**: The storage provider interface is simple and can be implemented for any storage solution

This approach allows for:
- Simple in-memory tracking during development
- Persistent storage in production
- Easy integration with any storage solution
- Clear parent-child relationships between functors

### Storage Provider Interface

The `FunctorChildrenStorageProvider` interface is designed to be simple and flexible:

1. **getChildren**: Gets all children of a functor
2. **addChild**: Adds a child to a functor
3. **removeChild**: Removes a child from a functor

This interface can be implemented for any storage solution, from in-memory to databases to distributed systems.

## File Location

This implementation should be placed in:
```
spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/functor.ts
```

## Dependencies

- `__types/functor.type.ts`: Type definitions for functors
- `cat.ts`: Category implementation
- `catObject.ts`: Category object implementation
- `morphism.ts`: Morphism implementation
