# Sequence Functor Implementation Specification

## Overview

This specification defines the implementation details for Sequence Functors in the cat_types package. A Sequence Functor is a functor that operates on arrays (sequences), providing a categorical view of sequential data structures. This is a fundamental concept for time.rfr.

## Dependencies

```typescript
import { Category } from '../cat';
import { CatObject } from '../catObject';
import { Morphism } from '../morphism';
import { BaseMorphism } from '../morphism';
import { Functor } from '../__types/functor.type';
import { BaseFunctor } from '../functor';
import { SequenceFunctor } from '../__types/sequence.type';
```

## Class Definition

```typescript
/**
 * Implementation of a Sequence Functor
 * 
 * @typeParam T - Type of objects in the target category
 * @typeParam TM - Type of morphisms in the target category
 */
export class SequenceFunctorImpl<
  T extends CatObject,
  TM extends Morphism<T>
> extends BaseFunctor<CatObject, Morphism<CatObject>, T, TM> 
  implements SequenceFunctor<T, TM> {
  
  /**
   * The underlying sequence
   */
  readonly sequence: any[];
  
  /**
   * Map of sequence indices to category objects
   */
  private elementMap: Map<number, T> = new Map();
  
  /**
   * Creates a new sequence functor
   * 
   * @param target Target category
   * @param objectMap Function that maps sequence elements to target objects
   * @param morphismMap Function that maps sequence indices to target morphisms
   * @param initialSequence Initial sequence (default: [])
   */
  constructor(
    target: Category<T, TM>,
    objectMap: (element: any, index: number) => T,
    morphismMap: (source: number, target: number) => TM,
    initialSequence: any[] = []
  ) {
    // Create a simple source category for sequence elements
    const sourceCategory = createSequenceCategory();
    
    // Store the sequence
    this.sequence = [...initialSequence];
    
    // Create the functor
    super(
      sourceCategory,
      target,
      (obj) => {
        const index = obj.value.index;
        const element = this.sequence[index];
        const mappedObj = objectMap(element, index);
        this.elementMap.set(index, mappedObj);
        return mappedObj;
      },
      (morphism) => {
        const sourceIndex = morphism.source.value.index;
        const targetIndex = morphism.target.value.index;
        return morphismMap(sourceIndex, targetIndex);
      }
    );
    
    // Initialize with the initial sequence
    for (let i = 0; i < this.sequence.length; i++) {
      const element = this.sequence[i];
      const mappedObj = objectMap(element, i);
      this.elementMap.set(i, mappedObj);
      target.addObject(mappedObj);
      
      // Add morphisms between adjacent elements
      if (i > 0) {
        const prevObj = this.elementMap.get(i - 1)!;
        const morphism = new BaseMorphism(
          prevObj,
          mappedObj,
          (prev) => mappedObj
        ) as TM;
        
        target.addMorphism(morphism);
      }
    }
  }
  
  /**
   * Gets an element by its index
   * 
   * @param index Element index
   * @returns The element at the given index, or undefined if not found
   */
  getElement(index: number): T | undefined {
    return this.elementMap.get(index);
  }
  
  /**
   * Adds an element to the sequence
   * 
   * @param value Element value
   * @returns The new element
   */
  addElement(value: any): T {
    // Add the element to the sequence
    const index = this.sequence.length;
    this.sequence.push(value);
    
    // Map the element to a target object
    const element = this.mapObject(createCatObject({ index, value }));
    
    // Add the element to the target category
    this.target.addObject(element);
    
    // Add a morphism from the previous element, if any
    if (index > 0) {
      const prevElement = this.elementMap.get(index - 1)!;
      const morphism = new BaseMorphism(
        prevElement,
        element,
        (prev) => element
      ) as TM;
      
      this.target.addMorphism(morphism);
    }
    
    // Store the element in the element map
    this.elementMap.set(index, element);
    
    return element;
  }
  
  /**
   * Removes an element from the sequence
   * 
   * @param index Element index
   * @returns True if the element was removed
   */
  removeElement(index: number): boolean {
    // Check if the index is valid
    if (index < 0 || index >= this.sequence.length) {
      return false;
    }
    
    // Remove the element from the sequence
    this.sequence.splice(index, 1);
    
    // Remove the element from the element map
    this.elementMap.delete(index);
    
    // Update the indices of the remaining elements
    for (let i = index; i < this.sequence.length; i++) {
      const element = this.elementMap.get(i + 1);
      if (element) {
        this.elementMap.set(i, element);
        this.elementMap.delete(i + 1);
      }
    }
    
    return true;
  }
  
  /**
   * Maps a function over the sequence
   * 
   * @param fn Function to map
   * @returns A new sequence functor with the mapped elements
   */
  map<U extends CatObject, UM extends Morphism<U>>(
    fn: (element: T, index: number) => U
  ): SequenceFunctor<U, UM> {
    // Create a new target category
    const newTarget = createCategory<U, UM>();
    
    // Map the sequence
    const mappedSequence = this.sequence.map((element, index) => {
      const obj = this.elementMap.get(index)!;
      return fn(obj, index).value;
    });
    
    // Create a new sequence functor
    return new SequenceFunctorImpl<U, UM>(
      newTarget,
      (element, index) => createCatObject(element) as U,
      (source, target) => new BaseMorphism(
        createCatObject(mappedSequence[source]) as U,
        createCatObject(mappedSequence[target]) as U,
        (src) => createCatObject(mappedSequence[target]) as U
      ) as UM,
      mappedSequence
    );
  }
  
  /**
   * Filters the sequence
   * 
   * @param predicate Predicate function
   * @returns A new sequence functor with the filtered elements
   */
  filter(predicate: (element: T, index: number) => boolean): SequenceFunctor<T, TM> {
    // Filter the sequence
    const filteredIndices: number[] = [];
    const filteredSequence: any[] = [];
    
    for (let i = 0; i < this.sequence.length; i++) {
      const element = this.elementMap.get(i)!;
      if (predicate(element, i)) {
        filteredIndices.push(i);
        filteredSequence.push(this.sequence[i]);
      }
    }
    
    // Create a new sequence functor
    return new SequenceFunctorImpl<T, TM>(
      this.target,
      (element, index) => this.elementMap.get(filteredIndices[index])!,
      (source, target) => new BaseMorphism(
        this.elementMap.get(filteredIndices[source])!,
        this.elementMap.get(filteredIndices[target])!,
        (src) => this.elementMap.get(filteredIndices[target])!
      ) as TM,
      filteredSequence
    );
  }
  
  /**
   * Reduces the sequence to a single value
   * 
   * @param fn Reducer function
   * @param initialValue Initial value
   * @returns The reduced value
   */
  reduce<U>(fn: (accumulator: U, element: T, index: number) => U, initialValue: U): U {
    let accumulator = initialValue;
    
    for (let i = 0; i < this.sequence.length; i++) {
      const element = this.elementMap.get(i)!;
      accumulator = fn(accumulator, element, i);
    }
    
    return accumulator;
  }
}
```

## Helper Functions

```typescript
/**
 * Creates a category for sequence elements
 * 
 * @returns A category for sequence elements
 */
function createSequenceCategory(): Category<CatObject, Morphism<CatObject>> {
  // Implementation details...
}

/**
 * Creates a category object from a sequence element
 * 
 * @param element Sequence element
 * @returns Category object
 */
function createCatObject(element: any): CatObject {
  return {
    id: { id: Symbol(typeof element === 'object' ? element.index : element), name: String(element) },
    value: element
  };
}

/**
 * Creates a new category
 * 
 * @returns A new category
 */
function createCategory<O extends CatObject, M extends Morphism<O>>(): Category<O, M> {
  // Implementation details...
}
```

## Utility Function

```typescript
/**
 * Creates a sequence functor
 * 
 * @param target Target category
 * @param objectMap Function that maps sequence elements to target objects
 * @param morphismMap Function that maps sequence indices to target morphisms
 * @param initialSequence Initial sequence
 * @returns A new sequence functor
 */
export function createSequenceFunctor<
  T extends CatObject,
  TM extends Morphism<T>
>(
  target: Category<T, TM>,
  objectMap: (element: any, index: number) => T,
  morphismMap: (source: number, target: number) => TM,
  initialSequence: any[] = []
): SequenceFunctor<T, TM> {
  return new SequenceFunctorImpl(
    target,
    objectMap,
    morphismMap,
    initialSequence
  );
}
```

## Implementation Details

### Constructor

The constructor takes four parameters:
1. `target`: The target category
2. `objectMap`: A function that maps sequence elements to target objects
3. `morphismMap`: A function that maps sequence indices to target morphisms
4. `initialSequence`: The initial sequence (default: [])

It creates a source category for sequence elements, stores the sequence, and initializes the functor.

### getElement

The `getElement` method gets an element by its index:

1. It looks up the element in the element map
2. It returns the corresponding target object, or undefined if not found

### addElement

The `addElement` method adds an element to the sequence:

1. It adds the element to the sequence
2. It maps the element to a target object
3. It adds the element to the target category
4. It adds a morphism from the previous element, if any
5. It stores the element in the element map
6. It returns the element

### removeElement

The `removeElement` method removes an element from the sequence:

1. It checks if the index is valid
2. It removes the element from the sequence
3. It removes the element from the element map
4. It updates the indices of the remaining elements
5. It returns true if the element was removed, false otherwise

### map

The `map` method maps a function over the sequence:

1. It creates a new target category
2. It maps the sequence using the provided function
3. It creates a new sequence functor with the mapped sequence
4. It returns the new sequence functor

### filter

The `filter` method filters the sequence:

1. It filters the sequence using the provided predicate
2. It creates a new sequence functor with the filtered sequence
3. It returns the new sequence functor

### reduce

The `reduce` method reduces the sequence to a single value:

1. It initializes the accumulator with the provided initial value
2. It iterates over the sequence, applying the reducer function to each element
3. It returns the final accumulator value

## File Location

This implementation should be placed in:
```
spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/functors/sequence.fr.ts
```

## Dependencies

- `__types/sequence.type.ts`: Type definitions for sequence functors
- `functor.ts`: Functor implementation
- `cat.ts`: Category implementation
- `catObject.ts`: Category object implementation
- `morphism.ts`: Morphism implementation
