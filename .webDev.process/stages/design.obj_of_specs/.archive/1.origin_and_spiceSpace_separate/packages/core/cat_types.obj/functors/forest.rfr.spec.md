# Forest Functor Implementation Specification

## Overview

This specification defines the implementation details for Forest Functors in the cat_types package. A Forest Functor represents a collection of trees, where each tree is a TreeFunctor. This enables managing multiple versions of SpiceTime as separate projections of a graph.

## Dependencies

```typescript
import { Category } from '../cat';
import { CatObject } from '../catObject';
import { Morphism } from '../morphism';
import { BaseMorphism } from '../morphism';
import { Functor } from '../__types/functor.type';
import { BaseFunctor } from '../functor';
import { TreeFunctor } from '../__types/tree.type';
import { createTreeFunctor } from './tree.rfr';
import { ForestFunctor, MergeStrategy } from '../__types/forest.type';
```

## Class Definition

```typescript
/**
 * Implementation of a Forest Functor
 * 
 * @typeParam T - Type of objects in the target category
 * @typeParam TM - Type of morphisms in the target category
 */
export class ForestFunctorImpl<
  T extends CatObject,
  TM extends Morphism<T>
> extends BaseFunctor<CatObject, Morphism<CatObject>, T, TM> 
  implements ForestFunctor<T, TM> {
  
  /**
   * The trees in the forest
   */
  readonly trees: Map<string, TreeFunctor<any, any>> = new Map();
  
  /**
   * Creates a new forest functor
   * 
   * @param target Target category
   * @param objectMap Function that maps forest nodes to target objects
   * @param morphismMap Function that maps forest morphisms to target morphisms
   */
  constructor(
    target: Category<T, TM>,
    objectMap: (forestNode: { treeId: string, nodeId: string }) => T,
    morphismMap: (source: { treeId: string, nodeId: string }, target: { treeId: string, nodeId: string }) => TM
  ) {
    // Create a simple source category for forest nodes
    const sourceCategory = createForestCategory();
    
    // Create the functor
    super(
      sourceCategory,
      target,
      (obj) => {
        const forestNode = obj.value as { treeId: string, nodeId: string };
        return objectMap(forestNode);
      },
      (morphism) => {
        const sourceNode = morphism.source.value as { treeId: string, nodeId: string };
        const targetNode = morphism.target.value as { treeId: string, nodeId: string };
        return morphismMap(sourceNode, targetNode);
      }
    );
  }
  
  /**
   * Gets a tree by its ID
   * 
   * @param id Tree ID
   * @returns The tree with the given ID, or undefined if not found
   */
  getTree(id: string): TreeFunctor<any, any> | undefined {
    return this.trees.get(id);
  }
  
  /**
   * Adds a tree to the forest
   * 
   * @param id Tree ID
   * @param tree Tree to add
   * @returns The added tree
   */
  addTree(id: string, tree: TreeFunctor<any, any>): TreeFunctor<any, any> {
    this.trees.set(id, tree);
    
    // Register this forest as a parent of the tree
    this.addChild(tree);
    
    return tree;
  }
  
  /**
   * Removes a tree from the forest
   * 
   * @param id Tree ID
   * @returns True if the tree was removed
   */
  removeTree(id: string): boolean {
    return this.trees.delete(id);
  }
  
  /**
   * Creates a new tree in the forest
   * 
   * @param id Tree ID
   * @param rootValue Root value
   * @returns The new tree
   */
  createTree(id: string, rootValue: any): TreeFunctor<any, any> {
    // Create a new tree
    const tree = createTreeFunctor(
      this.target as any,
      (node) => this.mapObject(createCatObject({ treeId: id, nodeId: node.id })),
      (source, target) => new BaseMorphism(
        this.mapObject(createCatObject({ treeId: id, nodeId: source.id })),
        this.mapObject(createCatObject({ treeId: id, nodeId: target.id })),
        (src) => this.mapObject(createCatObject({ treeId: id, nodeId: target.id }))
      ) as any,
      rootValue
    );
    
    // Add the tree to the forest
    return this.addTree(id, tree);
  }
  
  /**
   * Finds common subtrees between trees
   * 
   * @param treeId1 First tree ID
   * @param treeId2 Second tree ID
   * @returns Map of node IDs to common subtrees
   */
  findCommonSubtrees(treeId1: string, treeId2: string): Map<string, TreeFunctor<any, any>> {
    const tree1 = this.getTree(treeId1);
    const tree2 = this.getTree(treeId2);
    
    if (!tree1 || !tree2) {
      return new Map();
    }
    
    // Find common subtrees
    const commonSubtrees = new Map<string, TreeFunctor<any, any>>();
    
    // Implementation would compare tree structures to find common subtrees
    // This is a complex algorithm that would need to be implemented based on the specific tree structure
    // For now, we'll return an empty map
    
    return commonSubtrees;
  }
  
  /**
   * Merges two trees
   * 
   * @param targetTreeId Target tree ID
   * @param sourceTreeId Source tree ID
   * @param mergeStrategy Strategy for resolving conflicts
   * @returns The merged tree
   */
  mergeTrees(
    targetTreeId: string,
    sourceTreeId: string,
    mergeStrategy: MergeStrategy
  ): TreeFunctor<any, any> {
    const targetTree = this.getTree(targetTreeId);
    const sourceTree = this.getTree(sourceTreeId);
    
    if (!targetTree || !sourceTree) {
      throw new Error(`Trees not found: ${targetTreeId}, ${sourceTreeId}`);
    }
    
    // Create a new tree for the merged result
    const mergedTreeId = `${targetTreeId}_${sourceTreeId}_merged`;
    const mergedTree = this.createTree(mergedTreeId, targetTree.tree.getNode('root')!.value);
    
    // Find common subtrees
    const commonSubtrees = this.findCommonSubtrees(targetTreeId, sourceTreeId);
    
    // Merge the trees based on the strategy
    // This is a complex algorithm that would need to be implemented based on the specific tree structure
    // For now, we'll just return the target tree
    
    return mergedTree;
  }
}
```

## Helper Functions

```typescript
/**
 * Creates a category for forest nodes
 * 
 * @returns A category for forest nodes
 */
function createForestCategory(): Category<CatObject, Morphism<CatObject>> {
  // Implementation details...
}

/**
 * Creates a category object from a forest node
 * 
 * @param node Forest node
 * @returns Category object
 */
function createCatObject(node: { treeId: string, nodeId: string }): CatObject {
  return {
    id: { id: Symbol(`${node.treeId}:${node.nodeId}`), name: `${node.treeId}:${node.nodeId}` },
    value: node
  };
}
```

## Utility Function

```typescript
/**
 * Creates a forest functor
 * 
 * @param target Target category
 * @param objectMap Function that maps forest nodes to target objects
 * @param morphismMap Function that maps forest morphisms to target morphisms
 * @returns A new forest functor
 */
export function createForestFunctor<
  T extends CatObject,
  TM extends Morphism<T>
>(
  target: Category<T, TM>,
  objectMap: (forestNode: { treeId: string, nodeId: string }) => T,
  morphismMap: (source: { treeId: string, nodeId: string }, target: { treeId: string, nodeId: string }) => TM
): ForestFunctor<T, TM> {
  return new ForestFunctorImpl(
    target,
    objectMap,
    morphismMap
  );
}
```

## Implementation Details

### Constructor

The constructor takes three parameters:
1. `target`: The target category
2. `objectMap`: A function that maps forest nodes to target objects
3. `morphismMap`: A function that maps forest morphisms to target morphisms

It creates a source category for forest nodes and initializes the functor.

### getTree

The `getTree` method gets a tree by its ID:

1. It looks up the tree in the trees map
2. It returns the tree, or undefined if not found

### addTree

The `addTree` method adds a tree to the forest:

1. It adds the tree to the trees map
2. It registers the forest as a parent of the tree
3. It returns the added tree

### removeTree

The `removeTree` method removes a tree from the forest:

1. It removes the tree from the trees map
2. It returns true if the tree was removed, false otherwise

### createTree

The `createTree` method creates a new tree in the forest:

1. It creates a new tree with the given root value
2. It adds the tree to the forest
3. It returns the new tree

### findCommonSubtrees

The `findCommonSubtrees` method finds common subtrees between two trees:

1. It gets the two trees by their IDs
2. It compares the tree structures to find common subtrees
3. It returns a map of node IDs to common subtrees

### mergeTrees

The `mergeTrees` method merges two trees:

1. It gets the two trees by their IDs
2. It creates a new tree for the merged result
3. It finds common subtrees
4. It merges the trees based on the strategy
5. It returns the merged tree

## File Location

This implementation should be placed in:
```
spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/functors/forest.rfr.ts
```

## Dependencies

- `__types/forest.type.ts`: Type definitions for forest functors
- `functors/tree.rfr.ts`: Tree functor implementation
- `functor.ts`: Functor implementation
- `cat.ts`: Category implementation
- `catObject.ts`: Category object implementation
- `morphism.ts`: Morphism implementation
