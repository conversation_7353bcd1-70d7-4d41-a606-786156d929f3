# Spec Writing Methodology

## The Entanglement Problem

When writing specs for sibling nodes (pragmas, services, components), we encounter entanglement:
- Node A's behavior depends on Node B's API
- Node B's API depends on Node C's data structures  
- Node C's data structures depend on Node A's events
- **Result**: Circular dependencies that prevent progress

## Solution: Schema-First, Round Robin Approach

### Phase 1: Behavioral Specifications
Start with high-level behavioral requirements for all sibling nodes:
- What does each node need to accomplish?
- How do nodes interact with each other?
- What are the key data flows between nodes?

### Phase 2: API Schema Design
Design APIs as OpenAPI schemas based on behavioral specs:
- Define endpoints and operations
- Specify request/response data structures
- Establish event schemas for inter-node communication
- Create placeholder schemas for complex types

### Phase 3: Round Robin Iteration
Go round robin through sibling nodes, iteratively refining:

```
Round 1: Basic API structure for all nodes
├── Node A: Core endpoints + basic types
├── Node B: Core endpoints + basic types  
├── Node C: Core endpoints + basic types
└── Validate: Do APIs support required interactions?

Round 2: Detailed data structures
├── Node A: Refine types based on Node B/C needs
├── Node B: Refine types based on Node A/C needs
├── Node C: Refine types based on Node A/B needs  
└── Validate: Are data flows complete?

Round 3: Error handling and edge cases
├── Node A: Error responses, validation rules
├── Node B: Error responses, validation rules
├── Node C: Error responses, validation rules
└── Validate: Are error scenarios covered?

Round N: Continue until APIs stabilize
```

### Phase 4: Implementation Specifications
Once APIs are stable, write detailed implementation specs:
- Internal architecture and algorithms
- Performance requirements
- Resource management
- Testing strategies

## Iterative API Adjustment

### API Evolution Process
1. **Identify Gap**: During implementation spec writing, discover API limitations
2. **Propose Change**: Document required API modifications
3. **Impact Analysis**: Assess impact on sibling nodes
4. **Coordinate Update**: Update all affected node APIs simultaneously
5. **Validate**: Ensure updated APIs still support all required interactions

### Change Management
- **Version APIs** using semantic versioning
- **Maintain compatibility** during transitions
- **Document breaking changes** and migration paths
- **Coordinate releases** across dependent nodes

## Example: Spec Pragma Sibling Nodes

### Behavioral Requirements
- **parse**: Transform specs → semantics
- **generate**: Transform semantics → code  
- **sync**: Maintain spec ↔ code consistency
- **events**: Coordinate pipeline through events
- **middleware**: Enable extensibility through plugins

### Round 1: Basic APIs
```yaml
# parse API
POST /parse
  input: { specText, format }
  output: { semantics }

# generate API  
POST /generate
  input: { semantics, target }
  output: { files }

# events API
POST /emit
  input: { eventType, data }
  output: { eventId }
```

### Round 2: Detailed Types
```yaml
# Refined based on sibling needs
SemanticStructure:
  components: ComponentSpec[]  # generate needs this
  behaviors: BehaviorSpec[]    # events needs this
  types: TypeDefinition[]     # sync needs this

GeneratedFile:
  path: string                # sync needs this
  content: string            # sync needs this
  checksum: string           # sync needs this for change detection
```

### Round 3: Integration Points
```yaml
# Events that coordinate siblings
beforeParse → middleware intercepts → parse executes
afterParse → generate triggered → beforeGenerate emitted
afterGenerate → sync triggered → files compared
```

## Benefits of This Approach

### 1. **Breaks Circular Dependencies**
- APIs provide stable contracts between nodes
- Each node can be spec'd independently once APIs exist
- Changes are coordinated rather than cascading

### 2. **Enables Parallel Development**
- Multiple teams can work on sibling nodes simultaneously
- API contracts prevent integration surprises
- Implementation details can vary without affecting siblings

### 3. **Manages Complexity**
- Complex interactions are broken into simple API calls
- Data structures are explicitly defined and versioned
- Error scenarios are handled systematically

### 4. **Supports Evolution**
- APIs can evolve through versioning
- Breaking changes are coordinated across siblings
- New functionality can be added incrementally

## Implementation Guidelines

### For Each Round Robin Iteration:
1. **Focus on one aspect** (structure, data, errors, etc.)
2. **Update all sibling APIs** for that aspect
3. **Validate interactions** still work
4. **Document changes** and rationale
5. **Move to next aspect** only when current is stable

### For API Design:
1. **Start simple** - basic request/response
2. **Add complexity gradually** - detailed types, validation, errors
3. **Prioritize common cases** - optimize for typical usage
4. **Plan for extension** - allow for future requirements

### For Change Management:
1. **Batch related changes** - don't change APIs piecemeal
2. **Communicate impact** - notify all affected node owners
3. **Test integration** - validate sibling interactions
4. **Document migration** - provide clear upgrade paths

This methodology transforms the chaotic entanglement problem into a structured, manageable process that scales to complex systems with many interdependent components.
