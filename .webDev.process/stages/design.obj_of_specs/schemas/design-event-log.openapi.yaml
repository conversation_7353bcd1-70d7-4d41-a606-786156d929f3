openapi: 3.0.3
info:
  title: Design Event Log Format
  version: 1.0.0
  description: Event log format for capturing design process iterations and enabling time travel debugging

components:
  schemas:
    # Core Design Event
    DesignEvent:
      type: object
      required:
        - id
        - timestamp
        - type
        - stage
        - actor
        - context
        - payload
      properties:
        id:
          type: string
          format: uuid
          description: Unique event identifier
        timestamp:
          type: number
          description: Unix timestamp with microsecond precision
        type:
          type: string
          enum: [
            iteration-start, iteration-complete, iteration-blocked,
            api-schema-created, api-schema-modified, api-schema-validated,
            sibling-dependency-identified, sibling-dependency-resolved,
            round-robin-cycle-start, round-robin-cycle-complete,
            design-decision-made, design-decision-reversed,
            blocker-encountered, blocker-resolved,
            validation-passed, validation-failed,
            spec-generated, spec-modified, spec-approved
          ]
        stage:
          type: string
          enum: [ideation, design, implementation, testing, deployment]
        actor:
          $ref: '#/components/schemas/Actor'
        context:
          $ref: '#/components/schemas/DesignContext'
        payload:
          $ref: '#/components/schemas/EventPayload'
        correlationId:
          type: string
          format: uuid
          description: Links related events in a design session
        causedBy:
          type: array
          items:
            type: string
            format: uuid
          description: Event IDs that caused this event
        effects:
          type: array
          items:
            type: string
            format: uuid
          description: Event IDs caused by this event

    # Actor Information
    Actor:
      type: object
      required:
        - type
        - id
      properties:
        type:
          type: string
          enum: [human, ai-agent, automated-process, kernel-scheduler]
        id:
          type: string
          description: Actor identifier
        name:
          type: string
          description: Human-readable actor name
        role:
          type: string
          enum: [architect, developer, designer, reviewer, system]
        session:
          type: string
          description: Design session identifier

    # Design Context
    DesignContext:
      type: object
      required:
        - project
        - component
        - iteration
      properties:
        project:
          type: string
          description: Project identifier (e.g., 'spicetime-architecture')
        component:
          type: string
          description: Component being designed (e.g., 'spec-pragma')
        iteration:
          $ref: '#/components/schemas/IterationInfo'
        scope:
          type: string
          enum: [system, service, component, function, api]
        phase:
          type: string
          enum: [exploration, definition, refinement, validation, finalization]
        dependencies:
          type: array
          items:
            type: string
          description: Components this design depends on
        siblingNodes:
          type: array
          items:
            type: string
          description: Sibling components in current design scope

    # Iteration Information
    IterationInfo:
      type: object
      required:
        - round
        - focus
      properties:
        round:
          type: number
          description: Round robin iteration number
        focus:
          type: string
          enum: [
            basic-api-structure,
            detailed-data-structures, 
            error-handling,
            performance-optimization,
            integration-points,
            edge-cases
          ]
        methodology:
          type: string
          enum: [round-robin, waterfall, agile-sprint, exploratory]
        startTime:
          type: number
        estimatedDuration:
          type: number
        actualDuration:
          type: number

    # Event Payload (polymorphic based on event type)
    EventPayload:
      oneOf:
        - $ref: '#/components/schemas/APISchemaPayload'
        - $ref: '#/components/schemas/DesignDecisionPayload'
        - $ref: '#/components/schemas/ValidationPayload'
        - $ref: '#/components/schemas/BlockerPayload'
        - $ref: '#/components/schemas/IterationPayload'
        - $ref: '#/components/schemas/SiblingInteractionPayload'

    # API Schema Events
    APISchemaPayload:
      type: object
      required:
        - schemaName
        - action
      properties:
        schemaName:
          type: string
          description: Name of the API schema
        action:
          type: string
          enum: [created, modified, deleted, validated, published]
        schemaVersion:
          type: string
          description: Schema version
        changes:
          type: array
          items:
            $ref: '#/components/schemas/SchemaChange'
        validationResults:
          $ref: '#/components/schemas/ValidationResults'
        affectedSiblings:
          type: array
          items:
            type: string
          description: Sibling components affected by this change

    SchemaChange:
      type: object
      required:
        - type
        - path
        - operation
      properties:
        type:
          type: string
          enum: [endpoint, schema, property, enum, reference]
        path:
          type: string
          description: JSON path to the changed element
        operation:
          type: string
          enum: [add, modify, delete, rename]
        oldValue:
          description: Previous value (for modify/delete)
        newValue:
          description: New value (for add/modify)
        reason:
          type: string
          description: Reason for the change

    # Design Decision Events
    DesignDecisionPayload:
      type: object
      required:
        - decision
        - rationale
        - alternatives
      properties:
        decision:
          type: string
          description: The decision made
        rationale:
          type: string
          description: Why this decision was made
        alternatives:
          type: array
          items:
            $ref: '#/components/schemas/Alternative'
        impact:
          $ref: '#/components/schemas/DecisionImpact'
        reversible:
          type: boolean
          description: Whether this decision can be easily reversed
        confidence:
          type: number
          minimum: 0
          maximum: 1
          description: Confidence level in this decision
        reviewers:
          type: array
          items:
            type: string
          description: Who reviewed/approved this decision

    Alternative:
      type: object
      required:
        - option
        - pros
        - cons
      properties:
        option:
          type: string
        pros:
          type: array
          items:
            type: string
        cons:
          type: array
          items:
            type: string
        effort:
          type: string
          enum: [low, medium, high]
        risk:
          type: string
          enum: [low, medium, high]

    DecisionImpact:
      type: object
      properties:
        affectedComponents:
          type: array
          items:
            type: string
        breakingChanges:
          type: boolean
        migrationRequired:
          type: boolean
        performanceImpact:
          type: string
          enum: [positive, negative, neutral, unknown]
        complexityChange:
          type: string
          enum: [increased, decreased, neutral]

    # Validation Events
    ValidationPayload:
      type: object
      required:
        - validationType
        - result
      properties:
        validationType:
          type: string
          enum: [
            api-consistency,
            data-flow-completeness,
            sibling-interaction,
            schema-compliance,
            integration-readiness
          ]
        result:
          type: string
          enum: [passed, failed, warning, skipped]
        details:
          $ref: '#/components/schemas/ValidationResults'
        fixRequired:
          type: boolean
        autoFixable:
          type: boolean

    ValidationResults:
      type: object
      properties:
        errors:
          type: array
          items:
            $ref: '#/components/schemas/ValidationError'
        warnings:
          type: array
          items:
            $ref: '#/components/schemas/ValidationWarning'
        metrics:
          type: object
          additionalProperties:
            type: number
        suggestions:
          type: array
          items:
            type: string

    ValidationError:
      type: object
      required:
        - code
        - message
        - severity
      properties:
        code:
          type: string
        message:
          type: string
        severity:
          type: string
          enum: [critical, high, medium, low]
        location:
          type: string
          description: Where the error occurred
        suggestion:
          type: string
          description: How to fix the error

    ValidationWarning:
      type: object
      required:
        - code
        - message
      properties:
        code:
          type: string
        message:
          type: string
        location:
          type: string
        recommendation:
          type: string

    # Blocker Events
    BlockerPayload:
      type: object
      required:
        - blockerType
        - description
      properties:
        blockerType:
          type: string
          enum: [
            circular-dependency,
            missing-specification,
            conflicting-requirements,
            resource-constraint,
            technical-limitation,
            coordination-issue
          ]
        description:
          type: string
        affectedComponents:
          type: array
          items:
            type: string
        severity:
          type: string
          enum: [blocking, high, medium, low]
        estimatedResolutionTime:
          type: number
          description: Estimated time to resolve in milliseconds
        resolutionStrategy:
          type: string
        dependencies:
          type: array
          items:
            type: string
          description: What needs to happen to resolve this blocker

    # Iteration Events
    IterationPayload:
      type: object
      required:
        - iterationType
        - status
      properties:
        iterationType:
          type: string
          enum: [round-robin-cycle, design-sprint, validation-cycle]
        status:
          type: string
          enum: [started, in-progress, completed, failed, cancelled]
        objectives:
          type: array
          items:
            type: string
        completedObjectives:
          type: array
          items:
            type: string
        blockers:
          type: array
          items:
            type: string
        nextSteps:
          type: array
          items:
            type: string
        metrics:
          $ref: '#/components/schemas/IterationMetrics'

    IterationMetrics:
      type: object
      properties:
        duration:
          type: number
        componentsModified:
          type: number
        apiChanges:
          type: number
        validationsPassed:
          type: number
        validationsFailed:
          type: number
        blockersEncountered:
          type: number
        blockersResolved:
          type: number
        decisionsReversed:
          type: number

    # Sibling Interaction Events
    SiblingInteractionPayload:
      type: object
      required:
        - sourceComponent
        - targetComponent
        - interactionType
      properties:
        sourceComponent:
          type: string
        targetComponent:
          type: string
        interactionType:
          type: string
          enum: [
            api-call,
            data-flow,
            event-emission,
            dependency,
            composition,
            inheritance
          ]
        dataStructures:
          type: array
          items:
            type: string
          description: Data structures involved in the interaction
        apiEndpoints:
          type: array
          items:
            type: string
          description: API endpoints involved
        eventTypes:
          type: array
          items:
            type: string
          description: Event types involved
        validationStatus:
          type: string
          enum: [valid, invalid, incomplete, unknown]
        issues:
          type: array
          items:
            type: string

    # Time Travel Query Support
    TimeRangeQuery:
      type: object
      properties:
        start:
          type: number
          description: Start timestamp
        end:
          type: number
          description: End timestamp
        component:
          type: string
        eventTypes:
          type: array
          items:
            type: string
        actor:
          type: string
        correlationId:
          type: string

    # Event Log Metadata
    EventLogMetadata:
      type: object
      required:
        - logVersion
        - createdAt
        - project
      properties:
        logVersion:
          type: string
          description: Event log format version
        createdAt:
          type: number
        project:
          type: string
        totalEvents:
          type: number
        timeRange:
          type: object
          properties:
            earliest:
              type: number
            latest:
              type: number
        components:
          type: array
          items:
            type: string
        actors:
          type: array
          items:
            $ref: '#/components/schemas/Actor'
