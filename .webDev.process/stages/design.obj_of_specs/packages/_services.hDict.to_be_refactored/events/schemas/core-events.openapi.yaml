openapi: 3.0.3
info:
  title: SpiceTime Core Events
  version: 1.0.0
  description: Foundational event schemas for the SpiceTime ecosystem

components:
  schemas:
    # Base Event Structure
    BaseEvent:
      type: object
      required:
        - id
        - type
        - timestamp
        - source
      properties:
        id:
          type: string
          format: uuid
          description: Unique event identifier
        type:
          type: string
          description: Event type identifier
        timestamp:
          type: number
          description: Unix timestamp when event occurred
        source:
          type: string
          description: Source service/package that emitted the event
        correlationId:
          type: string
          format: uuid
          description: Correlation ID for tracking related events
        metadata:
          type: object
          additionalProperties: true
          description: Additional event metadata

    # Lifecycle Events
    LifecycleEvent:
      allOf:
        - $ref: '#/components/schemas/BaseEvent'
        - type: object
          properties:
            type:
              type: string
              enum: [start, stop, pause, resume, restart]
            data:
              $ref: '#/components/schemas/LifecycleEventData'

    LifecycleEventData:
      type: object
      properties:
        service:
          type: string
          description: Service name
        version:
          type: string
          description: Service version
        config:
          type: object
          additionalProperties: true
          description: Service configuration
        dependencies:
          type: array
          items:
            type: string
          description: Service dependencies

    # State Events
    StateEvent:
      allOf:
        - $ref: '#/components/schemas/BaseEvent'
        - type: object
          properties:
            type:
              type: string
              enum: [created, updated, deleted, synced]
            data:
              $ref: '#/components/schemas/StateEventData'

    StateEventData:
      type: object
      required:
        - entityType
        - entityId
      properties:
        entityType:
          type: string
          description: Type of entity (e.g., 'component', 'spec', 'pragma')
        entityId:
          type: string
          description: Unique identifier for the entity
        previousState:
          type: object
          additionalProperties: true
          description: Previous state (for update/delete events)
        currentState:
          type: object
          additionalProperties: true
          description: Current state (for create/update events)
        changes:
          type: array
          items:
            $ref: '#/components/schemas/StateChange'
          description: List of specific changes made

    StateChange:
      type: object
      required:
        - field
        - operation
      properties:
        field:
          type: string
          description: Field that changed
        operation:
          type: string
          enum: [create, update, delete]
        oldValue:
          description: Previous value
        newValue:
          description: New value

    # Error Events
    ErrorEvent:
      allOf:
        - $ref: '#/components/schemas/BaseEvent'
        - type: object
          properties:
            type:
              type: string
              enum: [error, warning, recovery, timeout]
            data:
              $ref: '#/components/schemas/ErrorEventData'

    ErrorEventData:
      type: object
      required:
        - severity
        - message
      properties:
        severity:
          type: string
          enum: [low, medium, high, critical]
        message:
          type: string
          description: Human-readable error message
        code:
          type: string
          description: Error code for programmatic handling
        stack:
          type: string
          description: Stack trace (for errors)
        context:
          type: object
          additionalProperties: true
          description: Additional context about the error
        recovery:
          type: object
          properties:
            possible:
              type: boolean
              description: Whether automatic recovery is possible
            strategy:
              type: string
              description: Recovery strategy to attempt
            attempts:
              type: number
              description: Number of recovery attempts made

    # Pipeline Events
    PipelineEvent:
      allOf:
        - $ref: '#/components/schemas/BaseEvent'
        - type: object
          properties:
            type:
              type: string
              enum: [beforeParse, afterParse, parseError, beforeGenerate, afterGenerate, generateError, beforeTransform, afterTransform, transformError]
            data:
              $ref: '#/components/schemas/PipelineEventData'

    PipelineEventData:
      type: object
      required:
        - stage
        - operation
      properties:
        stage:
          type: string
          enum: [parse, generate, transform, sync, compose]
          description: Pipeline stage
        operation:
          type: string
          description: Specific operation within the stage
        input:
          type: object
          additionalProperties: true
          description: Input data for the operation
        output:
          type: object
          additionalProperties: true
          description: Output data from the operation (for 'after' events)
        duration:
          type: number
          description: Operation duration in milliseconds (for 'after' events)
        performance:
          $ref: '#/components/schemas/PerformanceMetrics'

    PerformanceMetrics:
      type: object
      properties:
        cpuTime:
          type: number
          description: CPU time used in milliseconds
        memoryUsed:
          type: number
          description: Memory used in bytes
        diskIO:
          type: number
          description: Disk I/O operations
        networkIO:
          type: number
          description: Network I/O in bytes

    # Event Collection
    EventCollection:
      type: object
      required:
        - events
        - metadata
      properties:
        events:
          type: array
          items:
            oneOf:
              - $ref: '#/components/schemas/LifecycleEvent'
              - $ref: '#/components/schemas/StateEvent'
              - $ref: '#/components/schemas/ErrorEvent'
              - $ref: '#/components/schemas/PipelineEvent'
        metadata:
          type: object
          properties:
            totalCount:
              type: number
            timeRange:
              type: object
              properties:
                start:
                  type: number
                end:
                  type: number
            filters:
              type: object
              additionalProperties: true
