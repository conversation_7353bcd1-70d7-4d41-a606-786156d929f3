this is a hanging folder - services of what?
where do this services belong? - who provides them?

kernel is not a service but a proto of a spicetime node
the spice.space.0.0.0 - the origin of spice.space uses kernel
as a proto for its node
that package would live in myPublicSpicetime repo

but kernel package will live in components folder
and kernel will provide many services
letrs just place all of services in this folder as kernel services
then webdev process will be accessing these services per fs node

this folder needs to be refactored as prearranged by design stage structure