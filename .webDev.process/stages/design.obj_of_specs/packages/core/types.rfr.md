## context
- it extends st.pragma
- its based on internal def of types.rfr
  - it maintains subjects of its cat in an object called t
  - the subjects are basic ts types like obj,arr, num,int,str
  - these are basic ts types
- for each type in types cat
    - has a specific filename ext, just as stated above
    - .obj node exports js object with props as a getter 
  it basically operates as an object full of getters.each term is the module that has a .prop ext
wrapped in a getter func as per getter func def. just a normal js object, no games
    - arrays are folders with sequence numbers as prefixes - 1.arrayMember
      - alternitively, if other sequences are present item_1 can be used, or i1, or item1
  whatever module exoprts as default, is what array item must represent. so, it takes a wrappe func
  but since this pragma is extension of st.pragma, that wrapper is normal st pragma wrapper that provides the module scope
    - numbers and strings and other leaf types dont leverage any structure inside nodes
  its up to the index file to arrange the number to be exported by the node. that number is fed to pragma func as a prop
  
  
