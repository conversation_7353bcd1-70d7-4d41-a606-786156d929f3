# SpiceTime Space Component Specification

## Overview

The `spice.space.fr.tsx` component is a foundational React component that provides space-time coordinate functionality to the SpiceTime architecture. It serves as a key component in the HOC chain pattern, extending the context provided by the kernel component with space-time coordinates and frame relationships. This component enables the creation of related reference frames through an ultra-minimal, elegant syntax using template literals.

## Core Functionality

The `spice.space.fr.tsx` component provides the following core functionality:

1. **Space-Time Coordinates**: Provides a coordinate system for positioning in space-time
2. **Reference Frame Relationships**: Enables the creation of related reference frames
3. **Context Extension**: Extends the kernel context with space-time functionality
4. **Linguistic Scoped Tree**: Implements a linguistic scoped tree for context access
5. **Template Literal Syntax**: Provides an ultra-minimal syntax using template literals
6. **Unified Context Access**: Enables access to all context through a single hook

## Component Structure

### Template Literal Tag

```tsx
/**
 * Template literal tag for creating SpiceTime components
 *
 * @param strings - Template strings
 * @param values - Template values
 * @returns A function that takes a component and returns a wrapped component
 */
export function st(strings: TemplateStringsArray, ...values: any[]) {
  // Parse the template string to extract coordinates or relationships
  const spec = strings.raw[0];

  return function(Component: React.ComponentType<any>): React.FC<any> {
    return function STWrapper(props: any): JSX.Element {
      // Create the complete HOC chain with all providers
      const WrappedComponent = createHOCChain(spec, Component);

      // Return the wrapped component
      return <WrappedComponent {...props} />;
    };
  };
}

/**
 * Create the complete HOC chain based on the specification
 *
 * @param spec - Specification string from template literal
 * @param Component - Component to wrap
 * @returns Wrapped component with all providers
 */
function createHOCChain(spec: string, Component: React.ComponentType<any>): React.FC<any> {
  // Parse the specification
  const parsedSpec = parseSpec(spec);

  // Create the HOC chain
  return node(createSpaceComponent(parsedSpec)(kernel(cat_types(Component))));
}

/**
 * Parse the specification string
 *
 * @param spec - Specification string from template literal
 * @returns Parsed specification
 */
function parseSpec(spec: string): SpaceSpec {
  // Simple coordinate format: "0,0,0"
  if (/^\d+,\d+,\d+$/.test(spec)) {
    const [x, y, z] = spec.split(',').map(Number);
    return { type: 'coordinates', x, y, z };
  }

  // Renamed axes format: "x:a,y:b,z:c"
  if (/^[xyz]:[a-zA-Z0-9]+,[xyz]:[a-zA-Z0-9]+,[xyz]:[a-zA-Z0-9]+$/.test(spec)) {
    const axes = {};
    spec.split(',').forEach(part => {
      const [original, renamed] = part.split(':');
      axes[original] = renamed;
    });
    return { type: 'renamed', axes };
  }

  // Interdependency format: "x>y2,z2"
  if (/^[xyz]>[a-zA-Z0-9]+,[a-zA-Z0-9]+$/.test(spec)) {
    const [source, newAxes] = spec.split('>');
    return {
      type: 'interdependency-1',
      source,
      newAxes: newAxes.split(',')
    };
  }

  // Complete interdependency format: ">a,b,c"
  if (/^>[a-zA-Z0-9]+,[a-zA-Z0-9]+,[a-zA-Z0-9]+$/.test(spec)) {
    return {
      type: 'interdependency-0',
      newAxes: spec.substring(1).split(',')
    };
  }

  // Default to origin coordinates
  return { type: 'coordinates', x: 0, y: 0, z: 0 };
}
```

### Unified Context Type

```tsx
/**
 * Unified context value for all SpiceTime functionality
 */
export interface STContextValue {
  // Coordinates
  x: number;
  y: number;
  z: number;

  // Categorical types functionality
  createCategory: <O extends CatObject, M extends Morphism<O>>() => Category<O, M>;
  createObject: <T>(value: T, name?: string) => CatObject<T>;
  createMorphism: <S, T>(
    source: CatObject<S>,
    target: CatObject<T>,
    transform: (value: S) => T
  ) => Morphism<CatObject>;

  // Kernel functionality
  requestTic: () => Promise<TicInfo | null>;
  releaseTic: (ticId: string) => void;

  // Process functionality
  startProcess: () => Promise<void>;
  stopProcess: () => Promise<void>;

  // Node functionality
  render: () => void;
  update: () => void;

  // Access to other components
  kernel: any;
  process: any;
  time: any;

  // Linguistic tree
  linguisticTree: LinguisticTree;
}

## Core Implementations

### Reference Frame Relationships

The component implements two types of reference frame relationships:

1. **Translation**: Reusing two axes from the progenitor frame
   ```tsx
   /**
    * Create a translated frame
    *
    * @param x - X coordinate
    * @param y - Y coordinate
    * @param z - Z coordinate
    * @returns A function that takes a component and returns a wrapped component
    */
   export function translate(x: number, y: number, z: number) {
     return spiceSpace(x, y, z);
   }
   ```

2. **Interdependency**: Creating relationships between axes (includes what might be conceptualized as "rotation" at a higher level)
   ```tsx
   /**
    * Create a related frame by specifying which axes to reuse and which new axes to add
    *
    * @param progenitorAxes - Axes to reuse from the progenitor frame
    * @param newAxes - New axes to add
    * @returns A function that takes a component and returns a wrapped component
    */
   export function relateFrame(
     progenitorAxes: Array<'x' | 'y' | 'z'>,
     newAxes: string[]
   ) {
     return function(Component: React.ComponentType<any>): React.FC<any> {
       return function RelatedFrameWrapper(props: any): JSX.Element {
         // Access the space context
         const spaceContext = useSpace();

         // Determine the frame type based on which axes are reused
         const frameType = determineFrameType(progenitorAxes);

         // Create coordinates for the related frame
         const coordinates = {};

         // Copy coordinates from progenitor axes
         progenitorAxes.forEach(axis => {
           coordinates[axis] = spaceContext.coordinates[axis];
         });

         // Initialize new axes
         newAxes.forEach(axis => {
           coordinates[axis] = 0;
         });

         // Create the related frame context
         const relatedContext = {
           ...spaceContext,
           coordinates,
           frameType,
           progenitorAxes,
           newAxes,
           // Additional functionality based on frame type
         };

         // Provide the related context
         return (
           <RelatedFrameContext.Provider value={relatedContext}>
             <Component {...props} />
           </RelatedFrameContext.Provider>
         );
       };
     };
   }

   /**
    * Determine the type of relationship between frames
    *
    * @param progenitorAxes - Axes reused from the progenitor frame
    * @returns The type of frame relationship
    */
   function determineFrameType(progenitorAxes: Array<'x' | 'y' | 'z'>): string {
     switch (progenitorAxes.length) {
       case 3:
         return 'identity'; // All axes reused - same frame
       case 2:
         return 'translation'; // Two axes reused - translation
       case 1:
         return 'interdependency-1'; // One axis reused - interdependency (conceptually similar to rotation)
       case 0:
         return 'interdependency-0'; // No axes reused - complete interdependency
       default:
         return 'unknown';
     }
   }
   ```

### Linguistic Scoped Tree

The component implements a linguistic scoped tree for context access:

```tsx
/**
 * Linguistic tree for context access
 */
export interface LinguisticTree {
  /**
   * Get a node in the tree
   */
  getNode: (path: string) => any;

  /**
   * Set a node in the tree
   */
  setNode: (path: string, value: any) => void;

  /**
   * Check if a node exists in the tree
   */
  hasNode: (path: string) => boolean;

  /**
   * Get all children of a node
   */
  getChildren: (path: string) => string[];

  /**
   * Get the parent of a node
   */
  getParent: (path: string) => string | null;

  /**
   * Get the root of the tree
   */
  getRoot: () => any;
}
```

## Hook Interface

The component includes a unified hook for accessing all SpiceTime functionality:

```tsx
/**
 * Hook for accessing all SpiceTime functionality
 *
 * @returns Unified SpiceTime context
 */
export function useST(): STContextValue {
  // Access all contexts
  const catTypesContext = React.useContext(CatTypesContext);
  const kernelContext = React.useContext(KernelContext);
  const spaceContext = React.useContext(SpaceContext);
  const nodeContext = React.useContext(NodeContext);

  // Check if we're in a SpiceTime component
  if (!spaceContext) {
    throw new Error('useST must be used within a component wrapped by st`...`');
  }

  // Create the unified context
  const stContext: STContextValue = {
    // Coordinates
    x: spaceContext.coordinates.x,
    y: spaceContext.coordinates.y,
    z: spaceContext.coordinates.z,

    // Categorical types functionality
    ...catTypesContext,

    // Kernel functionality
    ...kernelContext,

    // Process functionality
    ...spaceContext,

    // Node functionality
    ...nodeContext,

    // Access to other components
    kernel: kernelContext,
    process: spaceContext,
    time: nodeContext,

    // Linguistic tree
    linguisticTree: spaceContext.linguisticTree
  };

  return stContext;
}
```

## Usage with Template Literals

The `spice.space.fr.tsx` component is designed to be used with template literals:

```tsx
// Import the components
import { st } from '@spicetime/core/spice.space.fr';

// Define a regular React component
function MyComponent(props) {
  // Use the unified context
  const st = useST();

  // Component implementation
  return (
    <div>
      <h1>My Component</h1>
      <p>Coordinates: {st.x}, {st.y}, {st.z}</p>
    </div>
  );
}

// Create a SpiceTime component using template literals
const MySpiceTimeComponent = st`0,0,0`(MyComponent);

// Use in an application
function App() {
  return <MySpiceTimeComponent />;
}

// Create multiple components at different coordinates
function MultiComponentApp() {
  return (
    <>
      {st`0,0,0`(MyComponent)}
      {st`1,0,0`(MyComponent)}
      {st`0,1,0`(MyComponent)}
    </>
  );
}

## Creating Related Frames

The component enables the creation of related frames using the template literal syntax:

```tsx
// Create components with different space relationships
function SpaceApp() {
  return (
    <>
      {/* Basic coordinate component */}
      {st`0,0,0`(MyComponent)}

      {/* Translated component */}
      {st`1,2,3`(MyComponent)}

      {/* Component with renamed axes */}
      {st`x:a,y:b,z:c`(MyComponent)}

      {/* Component with interdependency (conceptually similar to rotation) */}
      {st`x>y2,z2`(MyComponent)}

      {/* Component with complete interdependency */}
      {st`>a,b,c`(MyComponent)}
    </>
  );
}

## Accessing Context

The component provides unified access to all context through a single hook:

```tsx
function MyComponent() {
  // Access all context with a single hook
  const st = useST();

  // Access coordinates
  console.log(st.x, st.y, st.z);

  // Access categorical types functionality
  const category = st.createCategory();
  const obj = st.createObject('value');

  // Access kernel functionality
  st.requestTic();

  // Access process functionality
  st.startProcess();

  // Access node functionality
  st.render();

  // Access functionality from other components using dot notation
  st.kernel.someFunction();
  st.process.someFunction();
  st.time.someFunction();

  // Component implementation
  return <div>My Component</div>;
}

## File Structure

```
spice.space.fr.tsx/
├── src/
│   ├── index.tsx              # Main exports
│   ├── st.tsx                 # Template literal tag implementation
│   ├── context.tsx            # Context definition
│   ├── hooks.tsx              # Hooks for accessing context
│   ├── parser/                # Template string parser
│   │   ├── coordinates.tsx    # Coordinate parser
│   │   ├── renamed.tsx        # Renamed axes parser
│   │   └── interdependency.tsx # Interdependency parser
│   ├── relationships/         # Frame relationship implementations
│   │   ├── translation.tsx    # Translation implementation
│   │   └── interdependency.tsx # Interdependency implementation
│   ├── linguistic/            # Linguistic tree implementation
│   │   ├── tree.tsx           # Tree implementation
│   │   ├── node.tsx           # Node implementation
│   │   └── path.tsx           # Path utilities
│   └── utils/                 # Utility functions
│       ├── coordinates.tsx    # Coordinate utilities
│       ├── frames.tsx         # Frame utilities
│       └── relationships.tsx  # Relationship utilities
└── test/                      # Tests
    ├── st.test.tsx           # Template literal tag tests
    ├── context.test.tsx       # Context tests
    ├── hooks.test.tsx         # Hook tests
    ├── parser/                # Parser tests
    │   ├── coordinates.test.tsx # Coordinate parser tests
    │   ├── renamed.test.tsx    # Renamed axes parser tests
    │   └── interdependency.test.tsx # Interdependency parser tests
    ├── relationships/         # Relationship tests
    │   ├── translation.test.tsx # Translation tests
    │   └── interdependency.test.tsx # Interdependency tests
    └── linguistic/            # Linguistic tree tests
        ├── tree.test.tsx      # Tree tests
        ├── node.test.tsx      # Node tests
        └── path.test.tsx      # Path tests
```

## Implementation Guidelines

1. **Ultra-Minimal Syntax**: Provide the simplest possible syntax for creating SpiceTime components
2. **Flat Structure**: Keep the compound structure flat with no nesting
3. **JavaScript Scoping Rules**: Follow JavaScript scoping rules for accessing context
4. **Unified Context Access**: Provide a single hook for accessing all functionality
5. **Linguistic Approach**: Use a linguistic scoped tree for context access
6. **Relationship Types**: Implement frame relationships based on which axes are reused
7. **No Precise Angles**: Avoid expressing interdependencies with precise angles or matrices
8. **Extensibility**: Design for extensibility to support future frame types
9. **Documentation**: Document all public APIs with JSDoc comments
10. **Testing**: Write comprehensive tests for all functionality

## Conclusion

The `spice.space.fr.tsx` component provides the space-time coordinate functionality for the SpiceTime architecture through an ultra-minimal, elegant syntax using template literals. By implementing a coordinate system and frame relationships, it enables the creation of complex space-time structures with minimal boilerplate.

The template literal syntax (`st\`0,0,0\`(MyComponent)`) completely hides the complexity of the HOC chain, providing a clean, readable way to create SpiceTime components. The unified context access through the `useST()` hook simplifies component implementation, giving access to all functionality through a single object.

The component uses a simplified approach to frame relationships, focusing on which axes are reused from the progenitor frame rather than using precise angles or matrices. This allows the system to adapt to the semantic relationships in the entire ecosystem, with the understanding that what might be conceptualized as "rotation" at a higher level is expressed as interdependencies at our granular level.

The linguistic scoped tree approach provides a natural way to access context across the component hierarchy, while the flat compound structure ensures that all components are accessible in the parent scope. This component serves as a key part of the SpiceTime architecture, providing an elegant, minimal interface for creating and using space-time components.
