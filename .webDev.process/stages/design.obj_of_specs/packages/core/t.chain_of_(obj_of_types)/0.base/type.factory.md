# RTYPE Functor - Specification

## Overview

The RTYPE functor is the root functor in the Runtime Type System, implemented as a class from cat_types. It follows a categorical approach where each node extends its parent's class and adds its own prototype layer with additional terms. This approach provides mathematical rigor and enforces categorical laws while maintaining a familiar API for developers.

## Categorical Structure

- **RTYPE as a Functor**
  - Implemented as a class from cat_types
  - Maps between categories of types
  - Preserves the structure of types
  - Example: `const RTYPE = new TreeFunctor()`

- **Inheritance and Extension**
  - Each node extends its parent's class
  - Adds its own prototype layer with additional terms
  - Composes terms from previous layers
  - Example: `class UserRTYPE extends RTYPE { /* ... */ }`

- **Linguistic Orchestration**
  - Terms are orchestrated by the l namespace
  - Uses l tools to compose terms
  - Merges scope with linguistic functor
  - Example: `l.compose(RTYPE, UserRTYPE)`

## Factory Methods

### Creating rt Instances

- **RTYPE.of()**
  - Creates a new rt instance with the core API
  - Returns an rt instance that can be extended with specific types
  - Follows the categorical pattern of functors
  - Example: `const rt = RTYPE.of()`

- **RTYPE.withAdapter(adapter)**
  - Creates a new rt instance with a specific adapter
  - Allows plugging in different validation libraries
  - Maintains the categorical structure
  - Example: `const rt = RTYPE.withAdapter(zodAdapter)`

- **RTYPE.extend(baseRt, extensions)**
  - Creates a new rt instance that extends an existing one
  - Adds new terms to the rt instance
  - Preserves the categorical structure
  - Example: `const extendedRt = RTYPE.extend(baseRt, { User: userType })`

## Core API

### Primitive Types

- **string()**
  - Creates a string type
  - Optional constraints: min, max, pattern, email, url, etc.
  - Example: `rt.string().min(1).max(100).pattern(/^[a-z]+$/)`

- **number()**
  - Creates a number type
  - Optional constraints: min, max, integer, positive, negative, etc.
  - Example: `rt.number().min(0).max(100).integer()`

- **boolean()**
  - Creates a boolean type
  - Example: `rt.boolean()`

- **literal(value)**
  - Creates a literal value type
  - Example: `rt.literal('admin')`

### Complex Types

- **object(shape)**
  - Creates an object type with a specific shape
  - Shape is an object with property types
  - Example: `rt.object({ name: rt.string(), age: rt.number() })`

- **array(type)**
  - Creates an array type with a specific element type
  - Optional constraints: min, max, nonempty, etc.
  - Example: `rt.array(rt.string()).min(1).max(10)`

- **union(types)**
  - Creates a union type
  - Example: `rt.union([rt.string(), rt.number()])`

- **intersection(types)**
  - Creates an intersection type
  - Example: `rt.intersection([rt.object({ name: rt.string() }), rt.object({ age: rt.number() })])`

- **record(keyType, valueType)**
  - Creates a record type with specific key and value types
  - Example: `rt.record(rt.string(), rt.number())`

- **tuple(types)**
  - Creates a tuple type with specific element types
  - Example: `rt.tuple([rt.string(), rt.number(), rt.boolean()])`

- **enum(values)**
  - Creates an enumeration type
  - Example: `rt.enum(['admin', 'user', 'guest'])`

### Modifiers

- **optional(type)**
  - Makes a type optional
  - Example: `rt.optional(rt.string())`

- **nullable(type)**
  - Makes a type nullable
  - Example: `rt.nullable(rt.string())`

- **default(type, defaultValue)**
  - Sets a default value for a type
  - Example: `rt.default(rt.string(), 'unknown')`

## Adapter Interface

The RTYPE factory provides an adapter interface for plugging in different validation libraries:

- **Adapter Interface**
  - `validate(schema, value)` - Validates a value against a schema
  - `parse(schema, value)` - Parses a value according to a schema
  - `create(schema, partial)` - Creates a value from a partial input
  - `extend(schema, extension)` - Extends a schema with additional properties

- **Built-in Adapters**
  - `zodAdapter` - Adapter for Zod
  - `ioTsAdapter` - Adapter for io-ts
  - `runtypesAdapter` - Adapter for Runtypes
  - `customAdapter` - Custom adapter implementation

## Test Specifications

The following test specifications define the expected behavior of the RTYPE factory:

### Factory Method Tests

- **RTYPE.create()**
  - Creates an rt instance with the core API
  - Returns an object with all type definition methods
  - Example:
    ```typescript
    const rt = RTYPE.create();
    expect(rt.string).toBeFunction();
    expect(rt.number).toBeFunction();
    expect(rt.object).toBeFunction();
    ```

- **RTYPE.createWithAdapter(adapter)**
  - Creates an rt instance with a specific adapter
  - Uses the adapter for validation and creation
  - Example:
    ```typescript
    const adapter = { validate: jest.fn(), parse: jest.fn(), create: jest.fn(), extend: jest.fn() };
    const rt = RTYPE.createWithAdapter(adapter);
    const schema = rt.string();
    schema.validate('test');
    expect(adapter.validate).toHaveBeenCalledWith(schema, 'test');
    ```

- **RTYPE.extend(baseRt, extensions)**
  - Creates a new rt instance that extends an existing one
  - Includes all types from the base rt
  - Adds new types from the extensions
  - Example:
    ```typescript
    const baseRt = RTYPE.create();
    const userType = baseRt.object({ name: baseRt.string() });
    const extendedRt = RTYPE.extend(baseRt, { User: userType });
    expect(extendedRt.string).toBe(baseRt.string);
    expect(extendedRt.User).toBe(userType);
    ```

### Core API Tests

- **Primitive Types**
  - Create string, number, boolean, and literal types
  - Apply constraints to types
  - Example:
    ```typescript
    const rt = RTYPE.create();
    const stringType = rt.string().min(1).max(100);
    expect(stringType.validate('test')).toBe(true);
    expect(stringType.validate('')).toBe(false);
    ```

- **Complex Types**
  - Create object, array, union, intersection, record, tuple, and enum types
  - Compose types into complex structures
  - Example:
    ```typescript
    const rt = RTYPE.create();
    const userType = rt.object({
      name: rt.string().min(1),
      age: rt.number().min(0),
      role: rt.enum(['admin', 'user', 'guest'])
    });
    expect(userType.validate({ name: 'John', age: 30, role: 'admin' })).toBe(true);
    expect(userType.validate({ name: '', age: 30, role: 'admin' })).toBe(false);
    ```

- **Modifiers**
  - Apply optional, nullable, and default modifiers to types
  - Example:
    ```typescript
    const rt = RTYPE.create();
    const userType = rt.object({
      name: rt.string().min(1),
      age: rt.optional(rt.number().min(0)),
      email: rt.nullable(rt.string().email())
    });
    expect(userType.validate({ name: 'John' })).toBe(true);
    expect(userType.validate({ name: 'John', age: 30 })).toBe(true);
    expect(userType.validate({ name: 'John', email: null })).toBe(true);
    ```

### Adapter Interface Tests

- **Adapter Integration**
  - Integrate with different validation libraries
  - Use the adapter for validation and creation
  - Example:
    ```typescript
    const adapter = {
      validate: (schema, value) => schema._type === 'string' && typeof value === 'string',
      parse: (schema, value) => schema._type === 'string' && typeof value === 'string' ? value : null,
      create: (schema, partial) => partial,
      extend: (schema, extension) => ({ ...schema, ...extension })
    };
    const rt = RTYPE.createWithAdapter(adapter);
    const stringType = rt.string();
    expect(stringType.validate('test')).toBe(true);
    expect(stringType.validate(123)).toBe(false);
    ```

## Implementation Approach

The RTYPE factory will be implemented as a class with static methods for creating rt instances and instance methods for defining types. The implementation will follow a functional approach with immutable data structures and pure functions.

```typescript
class RTYPE {
  // Static methods for creating rt instances
  static create() { /* ... */ }
  static createWithAdapter(adapter) { /* ... */ }
  static extend(baseRt, extensions) { /* ... */ }

  // Instance methods for defining types
  string() { /* ... */ }
  number() { /* ... */ }
  boolean() { /* ... */ }
  object(shape) { /* ... */ }
  // Other type definition methods...
}
```

The RTYPE factory will be the foundation of the Runtime Type System, providing a consistent API for defining, validating, and creating types across the SpiceTime architecture.
