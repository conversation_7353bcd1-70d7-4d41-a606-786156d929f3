# Integration System - Specification

## Overview

The Integration System provides seamless integration between the Runtime Type System and other components of the SpiceTime architecture, including stPragma and forestry_cat_types. It follows a categorical approach, where types are objects and validations are morphisms between types.

## Integration with stPragma

The Integration System integrates with stPragma through unified `.type.ts` files:

- **Unified Type Files**
  - Define both static TypeScript types and runtime types in the same file
  - Static types are available in the `t` namespace
  - Runtime types are available in the `rt` namespace
  - Example:
    ```
    // In _.pragma.type.ts
    // Static type definitions (t namespace)
    export interface User {
      id: string;
      name: string;
      email: string;
    }

    // Runtime type definitions (rt namespace)
    export const User = rt.object({
      id: rt.string().min(1),
      name: rt.string().min(1),
      email: rt.string().email()
    });
    ```

- **Scope Integration**
  - Types defined in `.type.ts` files are available in the scope
  - Types follow the same namespace structure as the scope
  - Types follow the same visibility rules as other pragmas
  - Example:
    ```
    // In a component file
    import { rt } from './scope';

    // Use the User type
    const user = rt.User.create({
      id: '1',
      name: '<PERSON>',
      email: '<EMAIL>'
    });
    ```

- **Pragma Validation**
  - Pragmas can be validated using runtime types
  - Pragma operators can be validated against their specifications
  - Example:
    ```
    // In a pragma operator file
    import { rt } from './scope';

    // Validate a pragma operator
    if (rt.PragmaOperator.validate(operator)) {
      // Operator is valid
    }
    ```

## Integration with forestry_cat_types

The Integration System integrates with forestry_cat_types through a categorical mapping:

- **Categorical Mapping**
  - RTYPE is implemented as a class from TreeFunctor
  - rt extends RTYPE and adds its own prototype layer
  - Values are leaf objects in the categorical structure
  - Example:
    ```
    // RTYPE is already a TreeFunctor
    const RTYPE = new TreeFunctor();

    // rt extends RTYPE
    class UserRTYPE extends RTYPE {
      // Add user-specific types
    }
    const rt = new UserRTYPE();

    // Create a leaf object
    const user = rt.User.create({ name: 'John' });
    ```

- **Categorical Operations**
  - Types support categorical operations like composition and product
  - Validations are morphisms between types
  - Example:
    ```
    // Composition of types
    const NameAndAge = rt.intersection([
      rt.object({ name: rt.string() }),
      rt.object({ age: rt.number() })
    ]);

    // Product of types
    const UserAndPost = rt.tuple([rt.User, rt.Post]);
    ```

- **Functor Mapping**
  - Types can be mapped between categories
  - Validations preserve the structure of types
  - Example:
    ```
    // Map a type to a different category
    const UserDTO = rt.User.transform(user => ({
      id: user.id,
      fullName: `${user.firstName} ${user.lastName}`,
      email: user.email
    }));
    ```

## Integration with Other Systems

The Integration System integrates with other systems in the SpiceTime architecture:

- **API Integration**
  - APIs can be defined as schemas with validation
  - API requests and responses can be validated
  - Example:
    ```
    // Define an API schema
    const UserAPI = {
      getUser: {
        request: rt.object({ id: rt.string() }),
        response: rt.User
      },
      createUser: {
        request: rt.object({
          name: rt.string().min(1),
          email: rt.string().email()
        }),
        response: rt.User
      }
    };

    // Validate API requests and responses
    const request = { id: '1' };
    if (UserAPI.getUser.request.validate(request)) {
      // Request is valid
      const response = await getUser(request);
      if (UserAPI.getUser.response.validate(response)) {
        // Response is valid
        return response;
      }
    }
    ```

- **Database Integration**
  - Database schemas can be defined with validation
  - Database queries and results can be validated
  - Example:
    ```
    // Define a database schema
    const UserSchema = {
      table: 'users',
      fields: {
        id: rt.string().min(1),
        name: rt.string().min(1),
        email: rt.string().email(),
        createdAt: rt.date()
      }
    };

    // Validate database queries and results
    const query = { id: '1' };
    if (rt.object({ id: UserSchema.fields.id }).validate(query)) {
      // Query is valid
      const result = await db.query(UserSchema.table, query);
      if (rt.User.validate(result)) {
        // Result is valid
        return result;
      }
    }
    ```

- **Event Integration**
  - Events can be defined as schemas with validation
  - Event payloads can be validated
  - Example:
    ```
    // Define an event schema
    const UserCreatedEvent = rt.object({
      type: rt.literal('user.created'),
      payload: rt.User
    });

    // Validate event payloads
    const event = {
      type: 'user.created',
      payload: {
        id: '1',
        name: 'John',
        email: '<EMAIL>'
      }
    };
    if (UserCreatedEvent.validate(event)) {
      // Event is valid
      handleUserCreated(event.payload);
    }
    ```

## Test Specifications

The following test specifications define the expected behavior of the Integration System:

### stPragma Integration Tests

- **should define both static and runtime types in unified type files**
  - Creates a unified type file with static and runtime types
  - Imports the types in a component file
  - Verifies that both static and runtime types are available
  - Verifies that the types have the correct structure

- **should make types available in the scope**
  - Defines types in a `.type.ts` file
  - Creates a scope with the types
  - Verifies that the types are available in the scope
  - Verifies that the types follow the same namespace structure as the scope

- **should follow the same visibility rules as other pragmas**
  - Defines public and private types in `.type.ts` files
  - Creates a scope with the types
  - Verifies that public types are available in the scope
  - Verifies that private types are only available within the node

- **should validate pragmas using runtime types**
  - Defines pragma types in a `.type.ts` file
  - Creates pragma operators
  - Validates the operators against the types
  - Verifies that valid operators pass validation
  - Verifies that invalid operators fail validation

### forestry_cat_types Integration Tests

- **should map RTYPE to TreeFunctor**
  - Creates an RTYPE instance
  - Maps it to a TreeFunctor
  - Verifies that the mapping preserves the structure
  - Verifies that operations on the RTYPE are reflected in the TreeFunctor

- **should map rt to BranchFunctor**
  - Creates an rt instance
  - Maps it to a BranchFunctor
  - Verifies that the mapping preserves the structure
  - Verifies that operations on the rt are reflected in the BranchFunctor

- **should map values to LeafObjects**
  - Creates values using rt types
  - Maps them to LeafObjects
  - Verifies that the mapping preserves the structure
  - Verifies that operations on the values are reflected in the LeafObjects

- **should support categorical operations on types**
  - Creates types with categorical operations like composition and product
  - Validates values against the types
  - Verifies that the operations behave correctly
  - Verifies that the types maintain their categorical properties

- **should map types between categories**
  - Creates types in one category
  - Maps them to another category
  - Verifies that the mapping preserves the structure
  - Verifies that validations in one category correspond to validations in the other

### Other Systems Integration Tests

- **should integrate with APIs**
  - Defines API schemas with request and response types
  - Validates API requests and responses
  - Verifies that valid requests and responses pass validation
  - Verifies that invalid requests and responses fail validation

- **should integrate with databases**
  - Defines database schemas with field types
  - Validates database queries and results
  - Verifies that valid queries and results pass validation
  - Verifies that invalid queries and results fail validation

- **should integrate with events**
  - Defines event schemas with payload types
  - Validates event payloads
  - Verifies that valid payloads pass validation
  - Verifies that invalid payloads fail validation
