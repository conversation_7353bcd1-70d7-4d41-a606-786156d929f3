# Categorical Types Component Specification

## Overview

The cat_types component provides a categorical foundation for the SpiceTime architecture. It defines categorical objects, morphisms, functors, and other categorical concepts as both static TypeScript types and runtime types with validation and creation capabilities. This specification defines the API, behavior, and integration points of the cat_types component.

## Core Concepts

### Dual Namespace Structure

The cat_types component defines two parallel namespace structures:

- **t namespace**: Contains static TypeScript type definitions (available at compile time)
- **T namespace**: Contains runtime type operations (available at runtime)

Each type defined in the `t` namespace has a corresponding runtime type in the `T` namespace with the same name, providing `is`, `validate`, and `create` methods.

### Categorical Objects

Categorical objects are the basic building blocks of category theory:

```typescript
// Static type definition in t namespace
namespace t {
  export interface CatObject {
    id: string;
    value: any;
    type: string;
    metadata?: Record<string, any>;
  }
}

// Runtime type operations in T namespace
namespace T {
  export const CatObject = {
    is(value: any): value is t.CatObject {
      return typeof value === 'object' && value !== null &&
             typeof value.id === 'string' &&
             'value' in value &&
             typeof value.type === 'string';
    },
    
    validate(value: any): boolean {
      if (!T.CatObject.is(value)) {
        return false;
      }
      
      // Additional validation logic
      if (value.id.length === 0) {
        return false;
      }
      
      if (value.type.length === 0) {
        return false;
      }
      
      return true;
    },
    
    create(props: Partial<t.CatObject> = {}): t.CatObject {
      // Default values
      const defaults: t.CatObject = {
        id: '',
        value: null,
        type: ''
      };
      
      // Merge defaults with provided props
      const obj = { ...defaults, ...props };
      
      // Validate the created object
      if (!T.CatObject.validate(obj)) {
        throw new Error('Invalid CatObject created');
      }
      
      return obj;
    }
  };
}
```

### Morphisms

Morphisms represent mappings between categorical objects:

```typescript
// Static type definition in t namespace
namespace t {
  export interface Morphism<A extends CatObject = CatObject> {
    id: string;
    source: A;
    target: A;
    map: (value: any) => any;
    metadata?: Record<string, any>;
  }
}

// Runtime type operations in T namespace
namespace T {
  export const Morphism = {
    is(value: any): value is t.Morphism {
      return typeof value === 'object' && value !== null &&
             typeof value.id === 'string' &&
             T.CatObject.is(value.source) &&
             T.CatObject.is(value.target) &&
             typeof value.map === 'function';
    },
    
    validate(value: any): boolean {
      if (!T.Morphism.is(value)) {
        return false;
      }
      
      // Additional validation logic
      if (value.id.length === 0) {
        return false;
      }
      
      if (!T.CatObject.validate(value.source)) {
        return false;
      }
      
      if (!T.CatObject.validate(value.target)) {
        return false;
      }
      
      return true;
    },
    
    create(props: Partial<t.Morphism> = {}): t.Morphism {
      // Default values
      const defaults: t.Morphism = {
        id: '',
        source: T.CatObject.create(),
        target: T.CatObject.create(),
        map: (value) => value
      };
      
      // Merge defaults with provided props
      const morphism = { ...defaults, ...props };
      
      // Validate the created morphism
      if (!T.Morphism.validate(morphism)) {
        throw new Error('Invalid Morphism created');
      }
      
      return morphism;
    }
  };
}
```

### Categories

Categories consist of objects and morphisms with composition and identity operations:

```typescript
// Static type definition in t namespace
namespace t {
  export interface Category<O extends CatObject = CatObject, M extends Morphism<O> = Morphism<O>> {
    id: string;
    objects: Map<string, O>;
    morphisms: Map<string, M>;
    compose: (f: M, g: M) => M;
    identity: (obj: O) => M;
    metadata?: Record<string, any>;
  }
}

// Runtime type operations in T namespace
namespace T {
  export const Category = {
    is(value: any): value is t.Category {
      return typeof value === 'object' && value !== null &&
             typeof value.id === 'string' &&
             value.objects instanceof Map &&
             value.morphisms instanceof Map &&
             typeof value.compose === 'function' &&
             typeof value.identity === 'function';
    },
    
    validate(value: any): boolean {
      if (!T.Category.is(value)) {
        return false;
      }
      
      // Additional validation logic
      if (value.id.length === 0) {
        return false;
      }
      
      // Check that all objects are valid
      for (const obj of value.objects.values()) {
        if (!T.CatObject.validate(obj)) {
          return false;
        }
      }
      
      // Check that all morphisms are valid
      for (const morphism of value.morphisms.values()) {
        if (!T.Morphism.validate(morphism)) {
          return false;
        }
      }
      
      return true;
    },
    
    create(props: Partial<t.Category> = {}): t.Category {
      // Default values
      const defaults: t.Category = {
        id: '',
        objects: new Map(),
        morphisms: new Map(),
        compose: (f, g) => {
          if (f.target.id !== g.source.id) {
            throw new Error('Cannot compose morphisms: target of first does not match source of second');
          }
          
          return T.Morphism.create({
            id: `${f.id}_${g.id}`,
            source: f.source,
            target: g.target,
            map: (value) => g.map(f.map(value))
          });
        },
        identity: (obj) => T.Morphism.create({
          id: `identity_${obj.id}`,
          source: obj,
          target: obj,
          map: (value) => value
        })
      };
      
      // Merge defaults with provided props
      const category = { ...defaults, ...props };
      
      // Validate the created category
      if (!T.Category.validate(category)) {
        throw new Error('Invalid Category created');
      }
      
      return category;
    }
  };
}
```

### Functors

Functors map between categories, preserving structure:

```typescript
// Static type definition in t namespace
namespace t {
  export interface Functor<
    A extends CatObject = CatObject,
    AM extends Morphism<A> = Morphism<A>,
    B extends CatObject = CatObject,
    BM extends Morphism<B> = Morphism<B>
  > {
    id: string;
    sourceCategory: Category<A, AM>;
    targetCategory: Category<B, BM>;
    mapObject: (obj: A) => B;
    mapMorphism: (morphism: AM) => BM;
    metadata?: Record<string, any>;
  }
}

// Runtime type operations in T namespace
namespace T {
  export const Functor = {
    is(value: any): value is t.Functor {
      return typeof value === 'object' && value !== null &&
             typeof value.id === 'string' &&
             T.Category.is(value.sourceCategory) &&
             T.Category.is(value.targetCategory) &&
             typeof value.mapObject === 'function' &&
             typeof value.mapMorphism === 'function';
    },
    
    validate(value: any): boolean {
      if (!T.Functor.is(value)) {
        return false;
      }
      
      // Additional validation logic
      if (value.id.length === 0) {
        return false;
      }
      
      if (!T.Category.validate(value.sourceCategory)) {
        return false;
      }
      
      if (!T.Category.validate(value.targetCategory)) {
        return false;
      }
      
      return true;
    },
    
    create(props: Partial<t.Functor> = {}): t.Functor {
      // Default values
      const defaults: t.Functor = {
        id: '',
        sourceCategory: T.Category.create(),
        targetCategory: T.Category.create(),
        mapObject: (obj) => T.CatObject.create({
          id: `mapped_${obj.id}`,
          value: obj.value,
          type: obj.type
        }),
        mapMorphism: (morphism) => {
          const sourceObj = morphism.source;
          const targetObj = morphism.target;
          
          return T.Morphism.create({
            id: `mapped_${morphism.id}`,
            source: T.CatObject.create({
              id: `mapped_${sourceObj.id}`,
              value: sourceObj.value,
              type: sourceObj.type
            }),
            target: T.CatObject.create({
              id: `mapped_${targetObj.id}`,
              value: targetObj.value,
              type: targetObj.type
            }),
            map: morphism.map
          });
        }
      };
      
      // Merge defaults with provided props
      const functor = { ...defaults, ...props };
      
      // Validate the created functor
      if (!T.Functor.validate(functor)) {
        throw new Error('Invalid Functor created');
      }
      
      return functor;
    }
  };
}
```

### Natural Transformations

Natural transformations map between functors:

```typescript
// Static type definition in t namespace
namespace t {
  export interface NaturalTransformation<
    A extends CatObject = CatObject,
    AM extends Morphism<A> = Morphism<A>,
    B extends CatObject = CatObject,
    BM extends Morphism<B> = Morphism<B>
  > {
    id: string;
    sourceFunctor: Functor<A, AM, B, BM>;
    targetFunctor: Functor<A, AM, B, BM>;
    components: Map<string, BM>;
    metadata?: Record<string, any>;
  }
}

// Runtime type operations in T namespace
namespace T {
  export const NaturalTransformation = {
    is(value: any): value is t.NaturalTransformation {
      return typeof value === 'object' && value !== null &&
             typeof value.id === 'string' &&
             T.Functor.is(value.sourceFunctor) &&
             T.Functor.is(value.targetFunctor) &&
             value.components instanceof Map;
    },
    
    validate(value: any): boolean {
      if (!T.NaturalTransformation.is(value)) {
        return false;
      }
      
      // Additional validation logic
      if (value.id.length === 0) {
        return false;
      }
      
      if (!T.Functor.validate(value.sourceFunctor)) {
        return false;
      }
      
      if (!T.Functor.validate(value.targetFunctor)) {
        return false;
      }
      
      // Check that all components are valid morphisms
      for (const component of value.components.values()) {
        if (!T.Morphism.validate(component)) {
          return false;
        }
      }
      
      return true;
    },
    
    create(props: Partial<t.NaturalTransformation> = {}): t.NaturalTransformation {
      // Default values
      const defaults: t.NaturalTransformation = {
        id: '',
        sourceFunctor: T.Functor.create(),
        targetFunctor: T.Functor.create(),
        components: new Map()
      };
      
      // Merge defaults with provided props
      const naturalTransformation = { ...defaults, ...props };
      
      // Validate the created natural transformation
      if (!T.NaturalTransformation.validate(naturalTransformation)) {
        throw new Error('Invalid NaturalTransformation created');
      }
      
      return naturalTransformation;
    }
  };
}
```

## API Specification

### Creating Categorical Objects

```typescript
// Using the T namespace
const obj = T.CatObject.create({
  id: 'obj1',
  value: 'Hello, world!',
  type: 'string'
});
```

Creates a new categorical object with the specified properties.

### Creating Morphisms

```typescript
// Using the T namespace
const source = T.CatObject.create({
  id: 'source',
  value: 'Hello, world!',
  type: 'string'
});

const target = T.CatObject.create({
  id: 'target',
  value: 42,
  type: 'number'
});

const morphism = T.Morphism.create({
  id: 'morphism1',
  source,
  target,
  map: (value) => value.length
});
```

Creates a new morphism between the specified source and target objects.

### Creating Categories

```typescript
// Using the T namespace
const category = T.Category.create({
  id: 'category1'
});

// Add objects to the category
category.objects.set('obj1', obj);
category.objects.set('source', source);
category.objects.set('target', target);

// Add morphisms to the category
category.morphisms.set('morphism1', morphism);
```

Creates a new category and adds objects and morphisms to it.

### Creating Functors

```typescript
// Using the T namespace
const sourceCategory = T.Category.create({
  id: 'sourceCategory'
});

const targetCategory = T.Category.create({
  id: 'targetCategory'
});

const functor = T.Functor.create({
  id: 'functor1',
  sourceCategory,
  targetCategory,
  mapObject: (obj) => T.CatObject.create({
    id: `mapped_${obj.id}`,
    value: obj.value,
    type: obj.type
  }),
  mapMorphism: (morphism) => {
    const sourceObj = morphism.source;
    const targetObj = morphism.target;
    
    return T.Morphism.create({
      id: `mapped_${morphism.id}`,
      source: T.CatObject.create({
        id: `mapped_${sourceObj.id}`,
        value: sourceObj.value,
        type: sourceObj.type
      }),
      target: T.CatObject.create({
        id: `mapped_${targetObj.id}`,
        value: targetObj.value,
        type: targetObj.type
      }),
      map: morphism.map
    });
  }
});
```

Creates a new functor between the specified source and target categories.

### Validating Categorical Objects

```typescript
// Using the T namespace
const isValidObj = T.CatObject.validate(obj);
```

Validates a categorical object against its schema.

### Checking Type

```typescript
// Using the T namespace
const isCatObject = T.CatObject.is(obj);
const isMorphism = T.Morphism.is(morphism);
const isCategory = T.Category.is(category);
const isFunctor = T.Functor.is(functor);
```

Checks if a value is of the specified type.

## Implementation Requirements

Implementations of this specification must:

1. Provide both static TypeScript types in the `t` namespace and runtime types in the `T` namespace
2. Implement `is`, `validate`, and `create` methods for each type in the `T` namespace
3. Ensure that the `t` and `T` namespaces have the same structure and type names
4. Validate categorical laws (e.g., associativity of composition, identity laws)
5. Provide efficient implementations of categorical operations

## Example Usage

### Basic Usage

```typescript
import { t, T } from '@future/cat_types';

// Create a categorical object
const obj = T.CatObject.create({
  id: 'obj1',
  value: 'Hello, world!',
  type: 'string'
});

// Check if it's a valid categorical object
if (T.CatObject.validate(obj)) {
  console.log('Valid categorical object');
} else {
  console.log('Invalid categorical object');
}

// Create a category
const category = T.Category.create({
  id: 'category1'
});

// Add the object to the category
category.objects.set(obj.id, obj);

// Create an identity morphism for the object
const identity = category.identity(obj);

// Add the morphism to the category
category.morphisms.set(identity.id, identity);
```

### Advanced Usage

```typescript
import { t, T } from '@future/cat_types';

// Create a source category
const sourceCategory = T.Category.create({
  id: 'sourceCategory'
});

// Create a target category
const targetCategory = T.Category.create({
  id: 'targetCategory'
});

// Create a functor between the categories
const functor = T.Functor.create({
  id: 'functor1',
  sourceCategory,
  targetCategory,
  mapObject: (obj) => T.CatObject.create({
    id: `mapped_${obj.id}`,
    value: obj.value,
    type: obj.type
  }),
  mapMorphism: (morphism) => {
    const sourceObj = morphism.source;
    const targetObj = morphism.target;
    
    return T.Morphism.create({
      id: `mapped_${morphism.id}`,
      source: T.CatObject.create({
        id: `mapped_${sourceObj.id}`,
        value: sourceObj.value,
        type: sourceObj.type
      }),
      target: T.CatObject.create({
        id: `mapped_${targetObj.id}`,
        value: targetObj.value,
        type: targetObj.type
      }),
      map: morphism.map
    });
  }
});

// Create an object in the source category
const sourceObj = T.CatObject.create({
  id: 'sourceObj',
  value: 'Hello, world!',
  type: 'string'
});

// Add the object to the source category
sourceCategory.objects.set(sourceObj.id, sourceObj);

// Map the object to the target category using the functor
const targetObj = functor.mapObject(sourceObj);

// Add the mapped object to the target category
targetCategory.objects.set(targetObj.id, targetObj);
```

## Integration with Other Components

### stPragma

The cat_types component integrates with stPragma by:
- Providing categorical types for pragma operators
- Enabling validatable schema APIs
- Supporting the TypeScope system with the same namespace structure

### forestry_cat_types

The cat_types component integrates with forestry_cat_types by:
- Providing the categorical foundation for tree and forest functors
- Enabling validation of categorical structures
- Supporting type composition for complex structures

### linguistics

The cat_types component integrates with linguistics by:
- Providing categorical types for linguistic terms
- Enabling validation of linguistic expressions
- Supporting type composition for complex linguistic structures

## Conclusion

The cat_types component provides a categorical foundation for the SpiceTime architecture, defining categorical objects, morphisms, functors, and other categorical concepts as both static TypeScript types and runtime types with validation and creation capabilities. By providing both the `t` namespace for static types and the `T` namespace for runtime types with the same names, it enables a consistent approach to type safety and validation across the entire system.
