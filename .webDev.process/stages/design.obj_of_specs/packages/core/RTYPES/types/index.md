# Types System - Specification

## Overview

The Types System is a core component of the Runtime Type System, providing a comprehensive set of type definitions, validation methods, and creation utilities. It follows a categorical approach, where types are objects and validations are morphisms between types.

## Type Hierarchy

The Types System defines a hierarchy of types:

- **Base Type**
  - The root of all types
  - Provides common methods for all types
  - Example: `Type`

- **Primitive Types**
  - String, Number, Boolean, Literal
  - Represent basic JavaScript types
  - Example: `StringType`, `NumberType`, `BooleanType`, `LiteralType`

- **Complex Types**
  - Object, Array, Union, Intersection, Record, Tuple, Enum
  - Represent composite JavaScript types
  - Example: `ObjectType`, `ArrayType`, `UnionType`, `IntersectionType`

- **Modified Types**
  - Optional, Nullable, Default
  - Modify the behavior of other types
  - Example: `OptionalType`, `NullableType`, `DefaultType`

## Type Methods

### Common Methods

All types provide the following methods:

- **is(value)**
  - Type guard function
  - Returns a boolean indicating if the value is of the type
  - Narrows the type in TypeScript
  - Example: `if (rt.string().is(value)) { /* value is string */ }`

- **validate(value)**
  - Deep validation returning boolean
  - Checks all constraints
  - Example: `if (rt.string().validate(value)) { /* value is valid string */ }`

- **safeParse(value)**
  - Returns result object with success/error
  - Provides detailed error information
  - Example: `const result = rt.string().safeParse(value); if (result.success) { /* use result.data */ }`

- **parse(value)**
  - Throws on error
  - Returns the value if valid
  - Example: `try { const str = rt.string().parse(value); } catch (error) { /* handle error */ }`

- **create(partial)**
  - Creates a value with defaults
  - Fills in missing properties with default values
  - Example: `const user = rt.object({ name: rt.string() }).create({ name: 'John' })`

### Type-Specific Methods

Different types provide additional methods specific to their behavior:

- **String Type**
  - `min(length)` - Minimum length constraint
  - `max(length)` - Maximum length constraint
  - `pattern(regex)` - Regular expression constraint
  - `email()` - Email format constraint
  - `url()` - URL format constraint
  - Example: `rt.string().min(1).max(100).pattern(/^[a-z]+$/)`

- **Number Type**
  - `min(value)` - Minimum value constraint
  - `max(value)` - Maximum value constraint
  - `integer()` - Integer constraint
  - `positive()` - Positive number constraint
  - `negative()` - Negative number constraint
  - Example: `rt.number().min(0).max(100).integer()`

- **Object Type**
  - `extend(extension)` - Extends with additional properties
  - `pick(keys)` - Creates a new type with only the specified keys
  - `omit(keys)` - Creates a new type without the specified keys
  - `partial()` - Makes all properties optional
  - `required()` - Makes all properties required
  - Example: `rt.object({ name: rt.string() }).extend({ age: rt.number() })`

- **Array Type**
  - `min(length)` - Minimum length constraint
  - `max(length)` - Maximum length constraint
  - `nonempty()` - Non-empty array constraint
  - `unique()` - Unique elements constraint
  - Example: `rt.array(rt.string()).min(1).max(10).nonempty()`

## Type Composition

Types can be composed to create complex type structures:

- **Union Types**
  - Represent values that can be one of several types
  - Example: `rt.union([rt.string(), rt.number()])`

- **Intersection Types**
  - Represent values that satisfy multiple types
  - Example: `rt.intersection([rt.object({ name: rt.string() }), rt.object({ age: rt.number() })])`

- **Record Types**
  - Represent objects with keys of one type and values of another
  - Example: `rt.record(rt.string(), rt.number())`

- **Tuple Types**
  - Represent arrays with elements of specific types at specific positions
  - Example: `rt.tuple([rt.string(), rt.number(), rt.boolean()])`

- **Enum Types**
  - Represent values that must be one of a set of literals
  - Example: `rt.enum(['admin', 'user', 'guest'])`

## Type Refinement

Types can be refined with additional constraints:

- **Refinement**
  - Adds custom validation logic
  - Returns a new type
  - Example: `rt.number().refine(n => n > 0, { message: 'Must be positive' })`

- **Transformation**
  - Transforms the value during validation
  - Returns a new type
  - Example: `rt.string().transform(s => s.trim())`

## Test Specifications

The following test specifications define the expected behavior of the Types System:

### Base Type Tests

- **Common Methods**
  - Test is, validate, safeParse, and parse methods
  - Example:
    ```typescript
    const type = rt.string();
    
    // is
    expect(type.is('test')).toBe(true);
    expect(type.is(123)).toBe(false);
    
    // validate
    expect(type.validate('test')).toBe(true);
    expect(type.validate(123)).toBe(false);
    
    // safeParse
    const result1 = type.safeParse('test');
    expect(result1.success).toBe(true);
    expect(result1.data).toBe('test');
    
    const result2 = type.safeParse(123);
    expect(result2.success).toBe(false);
    expect(result2.error).toBeDefined();
    
    // parse
    expect(() => type.parse('test')).not.toThrow();
    expect(() => type.parse(123)).toThrow();
    ```

### Primitive Type Tests

- **String Type**
  - Test string-specific methods
  - Example:
    ```typescript
    const type = rt.string().min(3).max(10).pattern(/^[a-z]+$/);
    
    expect(type.validate('test')).toBe(true);
    expect(type.validate('te')).toBe(false); // Too short
    expect(type.validate('testtesttest')).toBe(false); // Too long
    expect(type.validate('Test')).toBe(false); // Invalid pattern
    ```

- **Number Type**
  - Test number-specific methods
  - Example:
    ```typescript
    const type = rt.number().min(0).max(100).integer();
    
    expect(type.validate(50)).toBe(true);
    expect(type.validate(-10)).toBe(false); // Too small
    expect(type.validate(200)).toBe(false); // Too large
    expect(type.validate(50.5)).toBe(false); // Not an integer
    ```

- **Boolean Type**
  - Test boolean validation
  - Example:
    ```typescript
    const type = rt.boolean();
    
    expect(type.validate(true)).toBe(true);
    expect(type.validate(false)).toBe(true);
    expect(type.validate('true')).toBe(false);
    expect(type.validate(1)).toBe(false);
    ```

- **Literal Type**
  - Test literal validation
  - Example:
    ```typescript
    const type = rt.literal('admin');
    
    expect(type.validate('admin')).toBe(true);
    expect(type.validate('user')).toBe(false);
    expect(type.validate(123)).toBe(false);
    ```

### Complex Type Tests

- **Object Type**
  - Test object-specific methods
  - Example:
    ```typescript
    const type = rt.object({
      name: rt.string().min(1),
      age: rt.number().min(0)
    });
    
    expect(type.validate({ name: 'John', age: 30 })).toBe(true);
    expect(type.validate({ name: '', age: 30 })).toBe(false); // Invalid name
    expect(type.validate({ name: 'John', age: -10 })).toBe(false); // Invalid age
    expect(type.validate({ name: 'John' })).toBe(false); // Missing age
    
    const extended = type.extend({ email: rt.string().email() });
    expect(extended.validate({ name: 'John', age: 30, email: '<EMAIL>' })).toBe(true);
    expect(extended.validate({ name: 'John', age: 30 })).toBe(false); // Missing email
    
    const picked = type.pick(['name']);
    expect(picked.validate({ name: 'John' })).toBe(true);
    expect(picked.validate({ name: 'John', age: 30 })).toBe(false); // Extra property
    
    const omitted = type.omit(['age']);
    expect(omitted.validate({ name: 'John' })).toBe(true);
    expect(omitted.validate({ name: 'John', age: 30 })).toBe(false); // Extra property
    
    const partial = type.partial();
    expect(partial.validate({ name: 'John', age: 30 })).toBe(true);
    expect(partial.validate({ name: 'John' })).toBe(true); // Age is optional
    expect(partial.validate({ age: 30 })).toBe(true); // Name is optional
    expect(partial.validate({})).toBe(true); // All properties are optional
    
    const required = partial.required();
    expect(required.validate({ name: 'John', age: 30 })).toBe(true);
    expect(required.validate({ name: 'John' })).toBe(false); // Missing age
    ```

- **Array Type**
  - Test array-specific methods
  - Example:
    ```typescript
    const type = rt.array(rt.string()).min(1).max(3).nonempty();
    
    expect(type.validate(['a', 'b'])).toBe(true);
    expect(type.validate([])).toBe(false); // Empty array
    expect(type.validate(['a', 'b', 'c', 'd'])).toBe(false); // Too many elements
    expect(type.validate(['a', 123])).toBe(false); // Invalid element
    
    const uniqueType = rt.array(rt.number()).unique();
    expect(uniqueType.validate([1, 2, 3])).toBe(true);
    expect(uniqueType.validate([1, 2, 2])).toBe(false); // Duplicate element
    ```

- **Union Type**
  - Test union validation
  - Example:
    ```typescript
    const type = rt.union([rt.string(), rt.number()]);
    
    expect(type.validate('test')).toBe(true);
    expect(type.validate(123)).toBe(true);
    expect(type.validate(true)).toBe(false);
    expect(type.validate({})).toBe(false);
    ```

- **Intersection Type**
  - Test intersection validation
  - Example:
    ```typescript
    const type = rt.intersection([
      rt.object({ name: rt.string() }),
      rt.object({ age: rt.number() })
    ]);
    
    expect(type.validate({ name: 'John', age: 30 })).toBe(true);
    expect(type.validate({ name: 'John' })).toBe(false); // Missing age
    expect(type.validate({ age: 30 })).toBe(false); // Missing name
    ```

- **Record Type**
  - Test record validation
  - Example:
    ```typescript
    const type = rt.record(rt.string(), rt.number());
    
    expect(type.validate({ a: 1, b: 2 })).toBe(true);
    expect(type.validate({ a: 'test' })).toBe(false); // Invalid value
    expect(type.validate({ 1: 1 })).toBe(false); // Invalid key
    ```

- **Tuple Type**
  - Test tuple validation
  - Example:
    ```typescript
    const type = rt.tuple([rt.string(), rt.number(), rt.boolean()]);
    
    expect(type.validate(['test', 123, true])).toBe(true);
    expect(type.validate(['test', 123])).toBe(false); // Too few elements
    expect(type.validate(['test', 123, true, 'extra'])).toBe(false); // Too many elements
    expect(type.validate(['test', 'test', true])).toBe(false); // Invalid element
    ```

- **Enum Type**
  - Test enum validation
  - Example:
    ```typescript
    const type = rt.enum(['admin', 'user', 'guest']);
    
    expect(type.validate('admin')).toBe(true);
    expect(type.validate('user')).toBe(true);
    expect(type.validate('guest')).toBe(true);
    expect(type.validate('other')).toBe(false);
    expect(type.validate(123)).toBe(false);
    ```

### Modified Type Tests

- **Optional Type**
  - Test optional validation
  - Example:
    ```typescript
    const type = rt.optional(rt.string());
    
    expect(type.validate('test')).toBe(true);
    expect(type.validate(undefined)).toBe(true);
    expect(type.validate(null)).toBe(false);
    expect(type.validate(123)).toBe(false);
    ```

- **Nullable Type**
  - Test nullable validation
  - Example:
    ```typescript
    const type = rt.nullable(rt.string());
    
    expect(type.validate('test')).toBe(true);
    expect(type.validate(null)).toBe(true);
    expect(type.validate(undefined)).toBe(false);
    expect(type.validate(123)).toBe(false);
    ```

- **Default Type**
  - Test default creation
  - Example:
    ```typescript
    const type = rt.string().default('unknown');
    
    expect(type.create(undefined)).toBe('unknown');
    expect(type.create('test')).toBe('test');
    ```

### Refinement Tests

- **Refinement**
  - Test custom validation
  - Example:
    ```typescript
    const type = rt.number().refine(n => n > 0, { message: 'Must be positive' });
    
    expect(type.validate(5)).toBe(true);
    expect(type.validate(0)).toBe(false);
    expect(type.validate(-5)).toBe(false);
    
    const result = type.safeParse(-5);
    expect(result.success).toBe(false);
    expect(result.error.message).toBe('Must be positive');
    ```

- **Transformation**
  - Test value transformation
  - Example:
    ```typescript
    const type = rt.string().transform(s => s.trim());
    
    const result = type.parse('  test  ');
    expect(result).toBe('test');
    ```

## Implementation Approach

The Types System will be implemented as a set of classes that represent different types, with methods for validation, creation, and refinement. The implementation will follow a functional approach with immutable data structures and pure functions.

```typescript
// Base Type
class Type {
  is(value: unknown): boolean { /* ... */ }
  validate(value: unknown): boolean { /* ... */ }
  safeParse(value: unknown): Result { /* ... */ }
  parse(value: unknown): any { /* ... */ }
  create(partial?: any): any { /* ... */ }
}

// String Type
class StringType extends Type {
  min(length: number): StringType { /* ... */ }
  max(length: number): StringType { /* ... */ }
  pattern(regex: RegExp): StringType { /* ... */ }
  email(): StringType { /* ... */ }
  url(): StringType { /* ... */ }
}

// Number Type
class NumberType extends Type {
  min(value: number): NumberType { /* ... */ }
  max(value: number): NumberType { /* ... */ }
  integer(): NumberType { /* ... */ }
  positive(): NumberType { /* ... */ }
  negative(): NumberType { /* ... */ }
}

// Object Type
class ObjectType extends Type {
  extend(extension: Record<string, Type>): ObjectType { /* ... */ }
  pick(keys: string[]): ObjectType { /* ... */ }
  omit(keys: string[]): ObjectType { /* ... */ }
  partial(): ObjectType { /* ... */ }
  required(): ObjectType { /* ... */ }
}

// Array Type
class ArrayType extends Type {
  min(length: number): ArrayType { /* ... */ }
  max(length: number): ArrayType { /* ... */ }
  nonempty(): ArrayType { /* ... */ }
  unique(): ArrayType { /* ... */ }
}
```

The Types System will be the foundation of the Runtime Type System, providing a comprehensive set of types and methods for validation and creation.
