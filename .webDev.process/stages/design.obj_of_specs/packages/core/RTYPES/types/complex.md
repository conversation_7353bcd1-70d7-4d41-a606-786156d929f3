# Complex Types - Specification

## Overview

Complex types are composite types in the Runtime Type System, representing structured data like objects, arrays, unions, intersections, records, tuples, and enums. They provide methods for validating, refining, and creating complex data structures.

## Object Type

The Object type represents object values with a specific shape and provides methods for validating and refining objects.

### Creation

- **rt.object(shape)**
  - Creates an object type with a specific shape
  - Shape is an object with property types
  - Example: `rt.object({ name: rt.string(), age: rt.number() })`

### Validation Methods

- **is(value)**
  - Type guard function
  - Returns true if the value is an object matching the shape
  - Example: `if (rt.object({ name: rt.string() }).is(value)) { /* value is { name: string } */ }`

- **validate(value)**
  - Deep validation returning boolean
  - Checks all constraints
  - Example: `if (rt.object({ name: rt.string() }).validate(value)) { /* value is valid object */ }`

- **safeParse(value)**
  - Returns result object with success/error
  - Provides detailed error information
  - Example: `const result = rt.object({ name: rt.string() }).safeParse(value)`

- **parse(value)**
  - Throws on error
  - Returns the value if valid
  - Example: `const obj = rt.object({ name: rt.string() }).parse(value)`

### Refinement Methods

- **extend(extension)**
  - Extends the object type with additional properties
  - Returns a new object type
  - Example: `rt.object({ name: rt.string() }).extend({ age: rt.number() })`

- **pick(keys)**
  - Creates a new object type with only the specified keys
  - Returns a new object type
  - Example: `rt.object({ name: rt.string(), age: rt.number() }).pick(['name'])`

- **omit(keys)**
  - Creates a new object type without the specified keys
  - Returns a new object type
  - Example: `rt.object({ name: rt.string(), age: rt.number() }).omit(['age'])`

- **partial()**
  - Makes all properties optional
  - Returns a new object type
  - Example: `rt.object({ name: rt.string(), age: rt.number() }).partial()`

- **required()**
  - Makes all properties required
  - Returns a new object type
  - Example: `rt.object({ name: rt.string(), age: rt.optional(rt.number()) }).required()`

- **deepPartial()**
  - Makes all properties and nested properties optional
  - Returns a new object type
  - Example: `rt.object({ user: rt.object({ name: rt.string() }) }).deepPartial()`

### Creation Methods

- **create(partial)**
  - Creates an object with default values
  - Returns a new object
  - Example: `rt.object({ name: rt.string(), age: rt.number().default(0) }).create({ name: 'John' })`

## Array Type

The Array type represents array values with a specific element type and provides methods for validating and refining arrays.

### Creation

- **rt.array(elementType)**
  - Creates an array type with a specific element type
  - Example: `rt.array(rt.string())`

### Validation Methods

- **is(value)**
  - Type guard function
  - Returns true if the value is an array with elements of the specified type
  - Example: `if (rt.array(rt.string()).is(value)) { /* value is string[] */ }`

- **validate(value)**
  - Deep validation returning boolean
  - Checks all constraints
  - Example: `if (rt.array(rt.string()).validate(value)) { /* value is valid string[] */ }`

- **safeParse(value)**
  - Returns result object with success/error
  - Provides detailed error information
  - Example: `const result = rt.array(rt.string()).safeParse(value)`

- **parse(value)**
  - Throws on error
  - Returns the value if valid
  - Example: `const arr = rt.array(rt.string()).parse(value)`

### Refinement Methods

- **min(length)**
  - Minimum length constraint
  - Returns a new array type
  - Example: `rt.array(rt.string()).min(1)`

- **max(length)**
  - Maximum length constraint
  - Returns a new array type
  - Example: `rt.array(rt.string()).max(10)`

- **length(length)**
  - Exact length constraint
  - Returns a new array type
  - Example: `rt.array(rt.string()).length(5)`

- **nonempty()**
  - Non-empty array constraint
  - Returns a new array type
  - Example: `rt.array(rt.string()).nonempty()`

- **unique()**
  - Unique elements constraint
  - Returns a new array type
  - Example: `rt.array(rt.string()).unique()`

### Creation Methods

- **create(elements)**
  - Creates an array with the specified elements
  - Returns a new array
  - Example: `rt.array(rt.string()).create(['a', 'b', 'c'])`

## Union Type

The Union type represents values that can be one of several types and provides methods for validating and refining unions.

### Creation

- **rt.union(types)**
  - Creates a union type with the specified types
  - Example: `rt.union([rt.string(), rt.number()])`

### Validation Methods

- **is(value)**
  - Type guard function
  - Returns true if the value matches any of the types
  - Example: `if (rt.union([rt.string(), rt.number()]).is(value)) { /* value is string | number */ }`

- **validate(value)**
  - Deep validation returning boolean
  - Checks if the value matches any of the types
  - Example: `if (rt.union([rt.string(), rt.number()]).validate(value)) { /* value is valid string | number */ }`

- **safeParse(value)**
  - Returns result object with success/error
  - Provides detailed error information
  - Example: `const result = rt.union([rt.string(), rt.number()]).safeParse(value)`

- **parse(value)**
  - Throws on error
  - Returns the value if valid
  - Example: `const val = rt.union([rt.string(), rt.number()]).parse(value)`

### Creation Methods

- **create(value)**
  - Creates a value of one of the union types
  - Returns a new value
  - Example: `rt.union([rt.string(), rt.number()]).create('test')`

## Intersection Type

The Intersection type represents values that satisfy multiple types and provides methods for validating and refining intersections.

### Creation

- **rt.intersection(types)**
  - Creates an intersection type with the specified types
  - Example: `rt.intersection([rt.object({ name: rt.string() }), rt.object({ age: rt.number() })])`

### Validation Methods

- **is(value)**
  - Type guard function
  - Returns true if the value matches all of the types
  - Example: `if (rt.intersection([rt.object({ name: rt.string() }), rt.object({ age: rt.number() })]).is(value)) { /* value is { name: string } & { age: number } */ }`

- **validate(value)**
  - Deep validation returning boolean
  - Checks if the value matches all of the types
  - Example: `if (rt.intersection([rt.object({ name: rt.string() }), rt.object({ age: rt.number() })]).validate(value)) { /* value is valid { name: string } & { age: number } */ }`

- **safeParse(value)**
  - Returns result object with success/error
  - Provides detailed error information
  - Example: `const result = rt.intersection([rt.object({ name: rt.string() }), rt.object({ age: rt.number() })]).safeParse(value)`

- **parse(value)**
  - Throws on error
  - Returns the value if valid
  - Example: `const val = rt.intersection([rt.object({ name: rt.string() }), rt.object({ age: rt.number() })]).parse(value)`

### Creation Methods

- **create(value)**
  - Creates a value that satisfies all of the intersection types
  - Returns a new value
  - Example: `rt.intersection([rt.object({ name: rt.string() }), rt.object({ age: rt.number() })]).create({ name: 'John', age: 30 })`

## Record Type

The Record type represents objects with keys of one type and values of another and provides methods for validating and refining records.

### Creation

- **rt.record(keyType, valueType)**
  - Creates a record type with the specified key and value types
  - Example: `rt.record(rt.string(), rt.number())`

### Validation Methods

- **is(value)**
  - Type guard function
  - Returns true if the value is an object with keys and values of the specified types
  - Example: `if (rt.record(rt.string(), rt.number()).is(value)) { /* value is Record<string, number> */ }`

- **validate(value)**
  - Deep validation returning boolean
  - Checks if the value is an object with keys and values of the specified types
  - Example: `if (rt.record(rt.string(), rt.number()).validate(value)) { /* value is valid Record<string, number> */ }`

- **safeParse(value)**
  - Returns result object with success/error
  - Provides detailed error information
  - Example: `const result = rt.record(rt.string(), rt.number()).safeParse(value)`

- **parse(value)**
  - Throws on error
  - Returns the value if valid
  - Example: `const rec = rt.record(rt.string(), rt.number()).parse(value)`

### Creation Methods

- **create(record)**
  - Creates a record with the specified key-value pairs
  - Returns a new record
  - Example: `rt.record(rt.string(), rt.number()).create({ a: 1, b: 2 })`

## Tuple Type

The Tuple type represents arrays with elements of specific types at specific positions and provides methods for validating and refining tuples.

### Creation

- **rt.tuple(types)**
  - Creates a tuple type with the specified element types
  - Example: `rt.tuple([rt.string(), rt.number(), rt.boolean()])`

### Validation Methods

- **is(value)**
  - Type guard function
  - Returns true if the value is an array with elements of the specified types at the specified positions
  - Example: `if (rt.tuple([rt.string(), rt.number()]).is(value)) { /* value is [string, number] */ }`

- **validate(value)**
  - Deep validation returning boolean
  - Checks if the value is an array with elements of the specified types at the specified positions
  - Example: `if (rt.tuple([rt.string(), rt.number()]).validate(value)) { /* value is valid [string, number] */ }`

- **safeParse(value)**
  - Returns result object with success/error
  - Provides detailed error information
  - Example: `const result = rt.tuple([rt.string(), rt.number()]).safeParse(value)`

- **parse(value)**
  - Throws on error
  - Returns the value if valid
  - Example: `const tup = rt.tuple([rt.string(), rt.number()]).parse(value)`

### Creation Methods

- **create(elements)**
  - Creates a tuple with the specified elements
  - Returns a new tuple
  - Example: `rt.tuple([rt.string(), rt.number()]).create(['test', 123])`

## Enum Type

The Enum type represents values that must be one of a set of literals and provides methods for validating and refining enums.

### Creation

- **rt.enum(values)**
  - Creates an enum type with the specified values
  - Example: `rt.enum(['admin', 'user', 'guest'])`

### Validation Methods

- **is(value)**
  - Type guard function
  - Returns true if the value is one of the enum values
  - Example: `if (rt.enum(['admin', 'user', 'guest']).is(value)) { /* value is 'admin' | 'user' | 'guest' */ }`

- **validate(value)**
  - Deep validation returning boolean
  - Checks if the value is one of the enum values
  - Example: `if (rt.enum(['admin', 'user', 'guest']).validate(value)) { /* value is valid 'admin' | 'user' | 'guest' */ }`

- **safeParse(value)**
  - Returns result object with success/error
  - Provides detailed error information
  - Example: `const result = rt.enum(['admin', 'user', 'guest']).safeParse(value)`

- **parse(value)**
  - Throws on error
  - Returns the value if valid
  - Example: `const val = rt.enum(['admin', 'user', 'guest']).parse(value)`

### Creation Methods

- **create(value)**
  - Creates a value that is one of the enum values
  - Returns a new value
  - Example: `rt.enum(['admin', 'user', 'guest']).create('admin')`

## Test Specifications

The following test specifications define the expected behavior of the complex types:

### Object Type Tests

- **should validate objects with the correct shape**
  - Creates an object type with a specific shape
  - Validates objects against the shape
  - Verifies that valid objects pass validation
  - Verifies that invalid objects fail validation

- **should validate nested objects**
  - Creates an object type with nested object properties
  - Validates objects with nested properties
  - Verifies that valid nested objects pass validation
  - Verifies that invalid nested objects fail validation

- **should extend object types with additional properties**
  - Creates an object type with a specific shape
  - Extends the object type with additional properties
  - Validates objects against the extended type
  - Verifies that objects with the additional properties pass validation

- **should pick properties from object types**
  - Creates an object type with multiple properties
  - Creates a new object type with only the picked properties
  - Validates objects against the picked type
  - Verifies that objects with only the picked properties pass validation
  - Verifies that objects with additional properties fail validation

- **should omit properties from object types**
  - Creates an object type with multiple properties
  - Creates a new object type without the omitted properties
  - Validates objects against the omitted type
  - Verifies that objects without the omitted properties pass validation
  - Verifies that objects with the omitted properties fail validation

- **should make all properties optional with partial**
  - Creates an object type with required properties
  - Creates a new object type with all properties optional
  - Validates objects against the partial type
  - Verifies that objects with missing properties pass validation

- **should make all properties required with required**
  - Creates an object type with optional properties
  - Creates a new object type with all properties required
  - Validates objects against the required type
  - Verifies that objects with all properties pass validation
  - Verifies that objects with missing properties fail validation

- **should make all nested properties optional with deepPartial**
  - Creates an object type with nested object properties
  - Creates a new object type with all nested properties optional
  - Validates objects against the deep partial type
  - Verifies that objects with missing nested properties pass validation

- **should create objects with default values**
  - Creates an object type with default values
  - Creates an object with the create method
  - Verifies that missing properties are filled with default values

### Array Type Tests

- **should validate arrays with the correct element type**
  - Creates an array type with a specific element type
  - Validates arrays against the element type
  - Verifies that arrays with valid elements pass validation
  - Verifies that arrays with invalid elements fail validation

- **should validate arrays with minimum length**
  - Creates an array type with a minimum length constraint
  - Validates arrays against the constraint
  - Verifies that arrays with sufficient length pass validation
  - Verifies that arrays with insufficient length fail validation

- **should validate arrays with maximum length**
  - Creates an array type with a maximum length constraint
  - Validates arrays against the constraint
  - Verifies that arrays with acceptable length pass validation
  - Verifies that arrays with excessive length fail validation

- **should validate arrays with exact length**
  - Creates an array type with an exact length constraint
  - Validates arrays against the constraint
  - Verifies that arrays with the exact length pass validation
  - Verifies that arrays with different length fail validation

- **should validate non-empty arrays**
  - Creates an array type with a non-empty constraint
  - Validates arrays against the constraint
  - Verifies that non-empty arrays pass validation
  - Verifies that empty arrays fail validation

- **should validate arrays with unique elements**
  - Creates an array type with a unique elements constraint
  - Validates arrays against the constraint
  - Verifies that arrays with unique elements pass validation
  - Verifies that arrays with duplicate elements fail validation

- **should create arrays with the specified elements**
  - Creates an array type with a specific element type
  - Creates an array with the create method
  - Verifies that the array has the specified elements

### Union Type Tests

- **should validate values that match any of the types**
  - Creates a union type with multiple types
  - Validates values against the union type
  - Verifies that values matching any of the types pass validation
  - Verifies that values matching none of the types fail validation

- **should provide type narrowing with is method**
  - Creates a union type with multiple types
  - Uses the is method to narrow the type
  - Verifies that the type is correctly narrowed

- **should create values of one of the union types**
  - Creates a union type with multiple types
  - Creates a value with the create method
  - Verifies that the value matches one of the union types

### Intersection Type Tests

- **should validate values that match all of the types**
  - Creates an intersection type with multiple types
  - Validates values against the intersection type
  - Verifies that values matching all of the types pass validation
  - Verifies that values matching only some of the types fail validation

- **should provide type narrowing with is method**
  - Creates an intersection type with multiple types
  - Uses the is method to narrow the type
  - Verifies that the type is correctly narrowed

- **should create values that satisfy all of the intersection types**
  - Creates an intersection type with multiple types
  - Creates a value with the create method
  - Verifies that the value satisfies all of the intersection types

### Record Type Tests

- **should validate objects with keys and values of the specified types**
  - Creates a record type with specific key and value types
  - Validates objects against the record type
  - Verifies that objects with valid keys and values pass validation
  - Verifies that objects with invalid keys or values fail validation

- **should create records with the specified key-value pairs**
  - Creates a record type with specific key and value types
  - Creates a record with the create method
  - Verifies that the record has the specified key-value pairs

### Tuple Type Tests

- **should validate arrays with elements of the specified types at the specified positions**
  - Creates a tuple type with specific element types
  - Validates arrays against the tuple type
  - Verifies that arrays with valid elements at the correct positions pass validation
  - Verifies that arrays with invalid elements or positions fail validation

- **should create tuples with the specified elements**
  - Creates a tuple type with specific element types
  - Creates a tuple with the create method
  - Verifies that the tuple has the specified elements at the correct positions

### Enum Type Tests

- **should validate values that are one of the enum values**
  - Creates an enum type with specific values
  - Validates values against the enum type
  - Verifies that values that are one of the enum values pass validation
  - Verifies that values that are not one of the enum values fail validation

- **should provide type narrowing with is method**
  - Creates an enum type with specific values
  - Uses the is method to narrow the type
  - Verifies that the type is correctly narrowed

- **should create values that are one of the enum values**
  - Creates an enum type with specific values
  - Creates a value with the create method
  - Verifies that the value is one of the enum values
