# RTYPES Error System - Specification

## Overview

The RTYPES Error System provides a comprehensive set of error classes for the Runtime Type System, leveraging the existing error package from the core directory. It follows a categorical approach to error handling, where errors are organized by type and provide detailed information about validation failures.

## Integration with Core Error Package

The RTYPES Error System integrates with the core error package:

- **STError Base Class**
  - Uses the `STError` class from `packages/core/error`
  - Extends with RTYPES-specific error information
  - Maintains the same error structure and location tracking

- **Error Information**
  - Uses the `ErrorInfo` interface from the core error package
  - Adds RTYPES-specific error details
  - Provides detailed validation error information

## Error Types

The RTYPES Error System defines the following error types:

- **RTYPEError**
  - Base error class for all RTYPES errors
  - Extends `STError` from the core error package

- **ValidationError**
  - Error class for validation failures
  - Includes path to the invalid value

- **TypeMismatchError**
  - Error class for type mismatches
  - Includes expected and actual types

- **ConstraintError**
  - Error class for constraint violations
  - Includes constraint details

- **SchemaError**
  - Error class for schema validation failures
  - Includes nested validation errors

- **AdapterError**
  - Error class for adapter failures
  - Includes adapter-specific error details

## Result Types

The RTYPES Error System defines the following result types:

- **ValidationResult**
  - Result type for validation operations
  - Includes success flag, data, and error

- **ParseResult**
  - Result type for parse operations
  - Includes success flag, data, and error

## Test Specifications

The following test specifications define the expected behavior of the RTYPES Error System:

### RTYPEError Tests

- **should create a base error with the correct name and message**
  - Creates an RTYPEError with a message
  - Verifies the error has the correct name and message
  - Verifies the error extends STError

- **should include extended information in the error**
  - Creates an RTYPEError with extended information
  - Verifies the extended information is accessible

### ValidationError Tests

- **should create a validation error with path and value information**
  - Creates a ValidationError with a path and value
  - Verifies the error has the correct path and value
  - Verifies the error extends RTYPEError

- **should include additional context in the validation error**
  - Creates a ValidationError with additional context
  - Verifies the additional context is accessible

### TypeMismatchError Tests

- **should create a type mismatch error with expected and actual type information**
  - Creates a TypeMismatchError with expected and actual types
  - Verifies the error has the correct expected and actual types
  - Verifies the error extends ValidationError

- **should format the error message with expected and actual types**
  - Creates a TypeMismatchError with expected and actual types
  - Verifies the error message includes the expected and actual types

### ConstraintError Tests

- **should create a constraint error with constraint information**
  - Creates a ConstraintError with a constraint
  - Verifies the error has the correct constraint
  - Verifies the error extends ValidationError

- **should format the error message with the constraint**
  - Creates a ConstraintError with a constraint
  - Verifies the error message includes the constraint

### SchemaError Tests

- **should create a schema error with nested validation errors**
  - Creates a SchemaError with nested validation errors
  - Verifies the error has the correct nested errors
  - Verifies the error extends ValidationError

- **should format the error message with the number of errors**
  - Creates a SchemaError with nested validation errors
  - Verifies the error message includes the number of errors

### AdapterError Tests

- **should create an adapter error with adapter information**
  - Creates an AdapterError with adapter name, operation, and cause
  - Verifies the error has the correct adapter name, operation, and cause
  - Verifies the error extends RTYPEError

- **should format the error message with adapter name, operation, and cause message**
  - Creates an AdapterError with adapter name, operation, and cause
  - Verifies the error message includes the adapter name, operation, and cause message

### ValidationResult Tests

- **should create a successful validation result**
  - Creates a successful ValidationResult with data
  - Verifies the result has success=true and the correct data
  - Verifies the result has no error

- **should create a failed validation result**
  - Creates a failed ValidationResult with an error
  - Verifies the result has success=false and the correct error
  - Verifies the result has no data

### ParseResult Tests

- **should create a successful parse result**
  - Creates a successful ParseResult with data
  - Verifies the result has success=true and the correct data
  - Verifies the result has no error

- **should create a failed parse result**
  - Creates a failed ParseResult with an error
  - Verifies the result has success=false and the correct error
  - Verifies the result has no data

### Error Handling Tests

- **should create type mismatch errors with the correct information**
  - Creates a type mismatch error with expected and actual types
  - Verifies the error has the correct information

- **should create constraint errors with the correct information**
  - Creates a constraint error with a constraint
  - Verifies the error has the correct information

- **should create schema errors with the correct information**
  - Creates a schema error with nested validation errors
  - Verifies the error has the correct information

- **should create adapter errors with the correct information**
  - Creates an adapter error with adapter name, operation, and cause
  - Verifies the error has the correct information

- **should aggregate validation errors into a schema error**
  - Creates multiple validation errors
  - Aggregates them into a schema error
  - Verifies the schema error has the correct nested errors
