# rt Component - Specification

## Overview

The rt component is an extension of the RTYPE functor, representing a branch functor in the Runtime Type System. Each node extends its parent's class and adds its own prototype layer with additional terms. The rt component contains type definitions specific to its scope and provides methods for validating and creating values, while maintaining the categorical structure and enforcing categorical laws.

## Component Structure

### Inheritance and Extension

- **Parent Inheritance**
  - Each rt instance inherits all types from its parent
  - Parent types are accessible through the same API
  - Example: `childRt.string` inherits from `parentRt.string`

- **Scope Extension**
  - Each rt instance can define new types specific to its scope
  - New types are added to the rt instance
  - Example: `childRt.User = childRt.object({ name: childRt.string() })`

- **Type Composition**
  - Types can be composed from other types
  - Composition follows categorical laws
  - Example: `rt.AdminUser = rt.User.extend({ role: rt.literal('admin') })`

### Scope Integration

- **Scope Availability**
  - The rt instance is available in the scope
  - Types defined in rt follow the same namespace structure as the scope
  - Example: `scope.rt.User` corresponds to `scope.User`

- **Namespace Structure**
  - Types in rt follow the same namespace structure as the terms they validate
  - Nested namespaces are supported
  - Example: `rt.Auth.User` validates `Auth.User` objects

- **Visibility Rules**
  - Types follow the same visibility rules as other pragmas
  - Private types are prefixed with `_`
  - Example: `rt._privateType` is only visible within the node

## Type Methods

### Validation Methods

- **is(value)**
  - Type guard function
  - Returns a boolean indicating if the value is of the type
  - Narrows the type in TypeScript
  - Example: `if (rt.User.is(value)) { /* value is User */ }`

- **validate(value)**
  - Deep validation returning boolean
  - Checks all constraints
  - Example: `if (rt.User.validate(value)) { /* value is valid */ }`

- **safeParse(value)**
  - Returns result object with success/error
  - Provides detailed error information
  - Example: `const result = rt.User.safeParse(value); if (result.success) { /* use result.data */ }`

- **parse(value)**
  - Throws on error
  - Returns the value if valid
  - Example: `try { const user = rt.User.parse(value); } catch (error) { /* handle error */ }`

### Creation Methods

- **create(partial)**
  - Creates a value with defaults
  - Fills in missing properties with default values
  - Example: `const user = rt.User.create({ name: 'John' })`

- **extend(extension)**
  - Extends a type with additional properties
  - Returns a new type
  - Example: `const AdminUser = rt.User.extend({ role: rt.literal('admin') })`

### Refinement Methods

- **refine(predicate)**
  - Adds custom validation
  - Returns a new type
  - Example: `const PositiveNumber = rt.number().refine(n => n > 0)`

- **transform(fn)**
  - Transforms value
  - Returns a new type
  - Example: `const Trimmed = rt.string().transform(s => s.trim())`

## Test Specifications

The following test specifications define the expected behavior of the rt component:

### Inheritance and Extension Tests

- **Parent Inheritance**
  - Child rt inherits all types from parent rt
  - Example:
    ```typescript
    const parentRt = RTYPE.create();
    const childRt = RTYPE.extend(parentRt, {});
    expect(childRt.string).toBe(parentRt.string);
    expect(childRt.number).toBe(parentRt.number);
    ```

- **Scope Extension**
  - Child rt can define new types
  - New types are added to the rt instance
  - Example:
    ```typescript
    const parentRt = RTYPE.create();
    const userType = parentRt.object({ name: parentRt.string() });
    const childRt = RTYPE.extend(parentRt, { User: userType });
    expect(childRt.User).toBe(userType);
    expect(parentRt.User).toBeUndefined();
    ```

- **Type Composition**
  - Types can be composed from other types
  - Example:
    ```typescript
    const rt = RTYPE.create();
    const User = rt.object({ name: rt.string() });
    const AdminUser = User.extend({ role: rt.literal('admin') });
    expect(AdminUser.validate({ name: 'John', role: 'admin' })).toBe(true);
    expect(AdminUser.validate({ name: 'John' })).toBe(false);
    ```

### Scope Integration Tests

- **Scope Availability**
  - The rt instance is available in the scope
  - Example:
    ```typescript
    const scope = createScope();
    scope.rt = RTYPE.create();
    expect(scope.rt.string).toBeFunction();
    expect(scope.rt.number).toBeFunction();
    ```

- **Namespace Structure**
  - Types in rt follow the same namespace structure as the terms they validate
  - Example:
    ```typescript
    const scope = createScope();
    scope.rt = RTYPE.create();
    scope.rt.Auth = {};
    scope.rt.Auth.User = scope.rt.object({ name: scope.rt.string() });
    expect(scope.rt.Auth.User.validate({ name: 'John' })).toBe(true);
    ```

- **Visibility Rules**
  - Types follow the same visibility rules as other pragmas
  - Example:
    ```typescript
    const scope = createScope();
    scope.rt = RTYPE.create();
    scope.rt._privateType = scope.rt.string();
    expect(scope.rt._privateType.validate('test')).toBe(true);
    ```

### Type Method Tests

- **Validation Methods**
  - Test is, validate, safeParse, and parse methods
  - Example:
    ```typescript
    const rt = RTYPE.create();
    const User = rt.object({ name: rt.string().min(1) });

    // is
    const value: unknown = { name: 'John' };
    if (User.is(value)) {
      expect(value.name).toBe('John');
    }

    // validate
    expect(User.validate({ name: 'John' })).toBe(true);
    expect(User.validate({ name: '' })).toBe(false);

    // safeParse
    const result1 = User.safeParse({ name: 'John' });
    expect(result1.success).toBe(true);
    expect(result1.data.name).toBe('John');

    const result2 = User.safeParse({ name: '' });
    expect(result2.success).toBe(false);
    expect(result2.error).toBeDefined();

    // parse
    expect(() => User.parse({ name: 'John' })).not.toThrow();
    expect(() => User.parse({ name: '' })).toThrow();
    ```

- **Creation Methods**
  - Test create and extend methods
  - Example:
    ```typescript
    const rt = RTYPE.create();
    const User = rt.object({
      name: rt.string().min(1),
      age: rt.optional(rt.number().min(0)).default(0)
    });

    // create
    const user1 = User.create({ name: 'John' });
    expect(user1.name).toBe('John');
    expect(user1.age).toBe(0);

    const user2 = User.create({ name: 'Jane', age: 30 });
    expect(user2.name).toBe('Jane');
    expect(user2.age).toBe(30);

    // extend
    const AdminUser = User.extend({ role: rt.literal('admin') });
    expect(AdminUser.validate({ name: 'John', age: 0, role: 'admin' })).toBe(true);
    expect(AdminUser.validate({ name: 'John', age: 0 })).toBe(false);
    ```

- **Refinement Methods**
  - Test refine and transform methods
  - Example:
    ```typescript
    const rt = RTYPE.create();

    // refine
    const PositiveNumber = rt.number().refine(n => n > 0);
    expect(PositiveNumber.validate(5)).toBe(true);
    expect(PositiveNumber.validate(0)).toBe(false);
    expect(PositiveNumber.validate(-5)).toBe(false);

    // transform
    const Trimmed = rt.string().transform(s => s.trim());
    const result = Trimmed.parse('  hello  ');
    expect(result).toBe('hello');
    ```

## Implementation Approach

The rt component will be implemented as an instance of the RTYPE factory, with methods for defining, validating, and creating types. The implementation will follow a functional approach with immutable data structures and pure functions.

```typescript
// Create an rt instance
const rt = RTYPE.create();

// Define types
rt.User = rt.object({
  name: rt.string().min(1),
  age: rt.optional(rt.number().min(0)).default(0)
});

// Validate values
if (rt.User.validate(value)) {
  // Value is valid
}

// Create values
const user = rt.User.create({ name: 'John' });
```

The rt component will be the primary interface for defining and using types in the Runtime Type System, providing a consistent API across the SpiceTime architecture.
