# Runtime Type System (RTYPES) - Specification

## Overview

The Runtime Type System (RTYPES) provides a categorical approach to runtime type validation in the SpiceTime architecture. It implements a functor/component/instance pattern using classes from cat_types, enabling type-safe operations and validations at runtime while enforcing categorical laws.

## Key Components

### Functor/Component/Instance Pattern

- **RTYPE (Root Functor)**
  - Implemented as a class from cat_types
  - Maps between categories of types
  - Defines the core API and adapter pattern
  - Represents the root functor in categorical terms

- **rt (Branch Functor)**
  - Extensions of RTYPE with specific types
  - Each node extends its parent's class and adds its own prototype layer
  - Inherits and composes terms from previous layers
  - Contains type definitions specific to its scope

- **Values (Leaf Objects)**
  - Actual data validated by the types
  - Represent instantiations of morphisms in each component
  - Validated against the type definitions in rt
  - Can be transformed through morphisms

## Core API

### Type Definitions

- **Primitive Types**
  - `rt.string()` - String type with optional constraints
  - `rt.number()` - Number type with optional constraints
  - `rt.boolean()` - Boolean type
  - `rt.literal(value)` - Literal value type

- **Complex Types**
  - `rt.object({...})` - Object type with shape
  - `rt.array(type)` - Array type with element type
  - `rt.union([...])` - Union of types
  - `rt.intersection([...])` - Intersection of types
  - `rt.record(keyType, valueType)` - Record type
  - `rt.tuple([...])` - Tuple type
  - `rt.enum([...])` - Enumeration type

- **Modifiers**
  - `rt.optional(type)` - Optional type
  - `rt.nullable(type)` - Nullable type
  - `rt.default(type, defaultValue)` - Type with default value

### Validation Methods

- `rt.Type.is(value)` - Type guard function
- `rt.Type.validate(value)` - Deep validation returning boolean
- `rt.Type.safeParse(value)` - Returns result object with success/error
- `rt.Type.parse(value)` - Throws on error

### Creation Methods

- `rt.Type.create(partial)` - Creates with defaults
- `rt.Type.extend(extension)` - Extends type with additional properties

### Refinement Methods

- `rt.Type.refine(predicate)` - Adds custom validation
- `rt.Type.transform(fn)` - Transforms value

## Integration with Other Systems

### Integration with stPragma

- Unified `.type.ts` files define both static and runtime types
- Types available in both t and rt namespaces
- Follows the same visibility rules as other pragmas

### Integration with forestry_cat_types

- RTYPE (Root Functor) maps to TreeFunctor
- rt (Branch Functor) maps to BranchFunctor
- Values (Leaves) map to LeafObjects

## Test Specifications

The following test specifications define the expected behavior of the RTYPES system:

- **RTYPE Factory Tests**
  - Creates rt instances with core API
  - Provides adapter interface for different implementations
  - Maintains factory/component/instance pattern

- **rt Component Tests**
  - Inherits and extends parent's rt
  - Contains type definitions specific to its scope
  - Integrates with scope system

- **Type Definition Tests**
  - Creates primitive and complex types
  - Composes types through unions, intersections, etc.
  - Applies modifiers to types

- **Validation Method Tests**
  - Validates values against types
  - Provides detailed error information
  - Handles nested validation

- **Creation Method Tests**
  - Creates values with defaults
  - Extends types with additional properties
  - Transforms values through refinements

- **Integration Tests**
  - Integrates with stPragma
  - Integrates with forestry_cat_types
  - Maintains categorical structure

## Implementation Approach

The RTYPES system will be implemented as:

1. **RTYPE Class**: The prototype class that defines the core API
2. **Adapter Interface**: The interface for plugging in different implementations
3. **Schema Classes**: Classes for different types (string, number, object, etc.)
4. **Integration with stPragma**: Through the unified `.type.ts` files

The implementation will follow a functional approach with immutable data structures and pure functions.
