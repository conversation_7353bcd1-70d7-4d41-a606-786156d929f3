# Linguistics Categorical Type System Specification

## Overview

This specification defines the Linguistics Categorical Type System for the SpiceTime architecture. It provides a React-based HOC/Provider component that inserts linguistics tools into the context, enabling linguistic operations as functional components. The system is built on top of Treenity core trees and integrates with <PERSON><PERSON><PERSON><PERSON> for parsing and Compromise for NLP capabilities.

The linguistics.stx package acts as a Higher-Order Component (HOC) that extends the cat_types component by extending its context with linguistics-specific categorical types, functors, and operations. This allows for seamless integration of linguistic concepts into the broader categorical framework of the SpiceTime architecture.

The package follows the Context Scoping Tree pattern described in concept #56, establishing bidirectional inheritance where it extends the cat_types context while simultaneously exposing its public API as a namespace within the parent context. It leverages the extensions defining namespaces approach from concept #57 to organize its API, and uses pragmas to compose extensions as needed.

## 1. Core Concepts

### 1.1 Integration with Treenity

The Linguistics system builds on Treenity core trees:

```typescript
/**
 * Represents a Linguistics Tree Node
 */
interface LinguisticsTreeNode {
  /**
   * Treenity node reference
   */
  treenityNode: TreenityNode;

  /**
   * Patches stream from Treenity
   */
  patches: PatchStream;

  /**
   * Linguistic operations
   */
  operations: Map<string, LinguisticOperation>;

  /**
   * Linguistic patterns
   */
  patterns: Map<string, LinguisticPattern>;
}
```

### 1.2 Linguistic Operations as Functional Components

Linguistic operations are implemented as pure functional components:

```typescript
/**
 * Linguistic operation as a functional component
 */
interface LinguisticOperation<P = any, R = any> {
  /**
   * Component name
   */
  name: string;

  /**
   * Component implementation
   */
  component: React.FC<P>;

  /**
   * Pure function implementation
   */
  fn: (props: P) => R;

  /**
   * Operation metadata
   */
  metadata: {
    description?: string;
    examples?: string[];
    category?: string;
  };
}
```

## 2. Provider Component

### 2.1 LinguisticsProvider

The LinguisticsProvider component serves as the entry point for the linguistics system:

```typescript
/**
 * Props for the LinguisticsProvider component
 */
interface LinguisticsProviderProps {
  /**
   * Child components
   */
  children: React.ReactNode;

  /**
   * Initial linguistic patterns
   */
  initialPatterns?: LinguisticPattern[];

  /**
   * Domain-specific configuration
   */
  domain?: string;

  /**
   * Treenity node for storing linguistic state
   */
  treenityNode?: TreenityNode;
}

/**
 * LinguisticsProvider component
 */
const LinguisticsProvider: React.FC<LinguisticsProviderProps> = ({
  children,
  initialPatterns = [],
  domain,
  treenityNode
}) => {
  // Create or use provided Treenity node
  const node = useMemo(() => {
    if (treenityNode) return treenityNode;

    return new TreenityNode({
      path: ['linguistics', domain || 'default'],
      initialState: {
        patterns: initialPatterns.reduce((acc, pattern) => {
          acc[pattern.name] = pattern;
          return acc;
        }, {})
      }
    });
  }, [treenityNode, domain, initialPatterns]);

  // Create linguistics context value
  const value = useMemo(() => ({
    // Core operations
    compose: createComposeOperation(node),
    word: createWordOperation(node),
    phrase: createPhraseOperation(node),
    pattern: createPatternOperation(node),
    parse: createParseOperation(node),
    match: createMatchOperation(node),

    // Advanced operations
    translate: createTranslateOperation(node),
    formality: createFormalityOperation(node),
    summarize: createSummarizeOperation(node),

    // Pattern management
    registerPattern: (pattern: LinguisticPattern) => {
      node.patches.apply({
        type: 'PATCH',
        path: node.path,
        operation: 'EDIT',
        value: {
          patterns: {
            ...node.state.patterns,
            [pattern.name]: pattern
          }
        }
      });
    },

    // Raw access to underlying systems
    parsimmon: P,
    compromise: nlp,
    treenityNode: node
  }), [node]);

  return (
    <CatTypesProvider>
      {/* Use CatTypesContext.Consumer to access the base context */}
      <CatTypesContext.Consumer>
        {(parentContext) => {
          // Create context that extends parent (following Context Scoping Tree pattern)
          const linguisticsContext = Object.create(parentContext);

          // Add linguistics API methods to linguistics context
          Object.assign(linguisticsContext, value);

          // Add linguistics namespace to parent context
          parentContext.linguistics = value;

          return (
            <LinguisticsContext.Provider value={linguisticsContext}>
              {children}
            </LinguisticsContext.Provider>
          );
        }}
      </CatTypesContext.Consumer>
    </CatTypesProvider>
  );
};
```

### 2.2 Context Interface

The LinguisticsContext provides access to linguistic operations:

```typescript
/**
 * Linguistics context interface
 */
interface LinguisticsContextType {
  /**
   * Core operations
   */
  compose: (tokens: Token[]) => Token;
  word: (value: string) => Token;
  phrase: (words: string[]) => Token;
  pattern: (pattern: string) => Token;
  parse: (text: string) => Token[];
  match: (pattern: Token, text: string) => boolean;

  /**
   * Advanced operations
   */
  translate: (text: string, to: string, from?: string) => string;
  formality: (text: string, level: 'formal' | 'neutral' | 'informal') => string;
  summarize: (text: string, length: 'short' | 'medium' | 'long') => string;

  /**
   * Pattern management
   */
  registerPattern: (pattern: LinguisticPattern) => void;

  /**
   * Raw access to underlying systems
   */
  parsimmon: typeof P;
  compromise: typeof nlp;
  treenityNode: TreenityNode;
}
```

## 3. Linguistic Operations Implementation

### 3.1 Core Operations

Core linguistic operations are implemented as both functional components and pure functions:

```typescript
/**
 * Creates a word operation
 */
function createWordOperation(node: TreenityNode): LinguisticOperation<string, Token> {
  const operation: LinguisticOperation<string, Token> = {
    name: 'word',
    component: ({ children }) => {
      return children;
    },
    fn: (value: string) => {
      return {
        type: 'word',
        value,
        metadata: {
          nlp: nlp(value).json()[0]
        }
      };
    },
    metadata: {
      description: 'Creates a word token',
      examples: ['word("hello")'],
      category: 'core'
    }
  };

  // Register operation with Treenity node
  node.patches.apply({
    type: 'PATCH',
    path: node.path,
    operation: 'EDIT',
    value: {
      operations: {
        ...node.state.operations,
        [operation.name]: operation
      }
    }
  });

  return operation;
}

/**
 * Creates a compose operation
 */
function createComposeOperation(node: TreenityNode): LinguisticOperation<Token[], Token> {
  const operation: LinguisticOperation<Token[], Token> = {
    name: 'compose',
    component: ({ children }) => {
      return children;
    },
    fn: (tokens: Token[]) => {
      return {
        type: 'composed',
        value: tokens,
        metadata: {
          composed: true,
          parts: tokens.map(token => token.metadata)
        }
      };
    },
    metadata: {
      description: 'Composes tokens into a single token',
      examples: ['compose([word("hello"), word("world")])'],
      category: 'core'
    }
  };

  // Register operation with Treenity node
  node.patches.apply({
    type: 'PATCH',
    path: node.path,
    operation: 'EDIT',
    value: {
      operations: {
        ...node.state.operations,
        [operation.name]: operation
      }
    }
  });

  return operation;
}
```

### 3.2 Advanced Operations

Advanced linguistic operations build on core operations:

```typescript
/**
 * Creates a translate operation
 */
function createTranslateOperation(node: TreenityNode): LinguisticOperation<{
  children: string;
  to: string;
  from?: string;
}, string> {
  const operation: LinguisticOperation<{
    children: string;
    to: string;
    from?: string;
  }, string> = {
    name: 'translate',
    component: ({ children, to, from = 'auto' }) => {
      // In a real implementation, this would call a translation service
      return `Translated to ${to}: ${children}`;
    },
    fn: ({ children, to, from = 'auto' }) => {
      // In a real implementation, this would call a translation service
      return `Translated to ${to}: ${children}`;
    },
    metadata: {
      description: 'Translates text to another language',
      examples: ['translate("Hello world", "es")'],
      category: 'advanced'
    }
  };

  // Register operation with Treenity node
  node.patches.apply({
    type: 'PATCH',
    path: node.path,
    operation: 'EDIT',
    value: {
      operations: {
        ...node.state.operations,
        [operation.name]: operation
      }
    }
  });

  return operation;
}
```

## 4. Pattern Matching System

### 4.1 Linguistic Patterns

Linguistic patterns combine Parsimmon parsers with Compromise NLP:

```typescript
/**
 * Linguistic pattern
 */
interface LinguisticPattern<T = any> {
  /**
   * Pattern name
   */
  name: string;

  /**
   * Parsimmon parser
   */
  parser: P.Parser<T>;

  /**
   * Whether to use Compromise NLP
   */
  useNlp?: boolean;

  /**
   * Pattern metadata
   */
  metadata: {
    type: string;
    composed?: boolean;
    parts?: any[];
    [key: string]: unknown;
  };
}

/**
 * Creates a pattern operation
 */
function createPatternOperation(node: TreenityNode): LinguisticOperation<string, LinguisticPattern> {
  const operation: LinguisticOperation<string, LinguisticPattern> = {
    name: 'pattern',
    component: ({ children }) => {
      return children;
    },
    fn: (pattern: string) => {
      return {
        name: `pattern-${Math.random().toString(36).substr(2, 9)}`,
        parser: P.regexp(new RegExp(pattern)),
        useNlp: false,
        metadata: {
          type: 'pattern',
          pattern
        }
      };
    },
    metadata: {
      description: 'Creates a pattern from a regular expression',
      examples: ['pattern("[A-Za-z]+")'],
      category: 'core'
    }
  };

  // Register operation with Treenity node
  node.patches.apply({
    type: 'PATCH',
    path: node.path,
    operation: 'EDIT',
    value: {
      operations: {
        ...node.state.operations,
        [operation.name]: operation
      }
    }
  });

  return operation;
}
```

### 4.2 Pattern Matching

The match operation uses both Parsimmon and Compromise:

```typescript
/**
 * Creates a match operation
 */
function createMatchOperation(node: TreenityNode): LinguisticOperation<{
  pattern: LinguisticPattern;
  text: string;
}, any> {
  const operation: LinguisticOperation<{
    pattern: LinguisticPattern;
    text: string;
  }, any> = {
    name: 'match',
    component: ({ pattern, text }) => {
      const result = operation.fn({ pattern, text });
      return JSON.stringify(result);
    },
    fn: ({ pattern, text }) => {
      // Parse with Parsimmon
      const parseResult = pattern.parser.parse(text);

      if (!parseResult.status) {
        return { success: false, expected: parseResult.expected };
      }

      let result = { success: true, value: parseResult.value };

      // Enhance with Compromise if needed
      if (pattern.useNlp) {
        const doc = nlp(text);
        result = {
          ...result,
          nlp: {
            tags: doc.json()[0]?.terms.map(t => t.tags),
            entities: doc.json()[0]?.entities,
            sentiment: doc.sentiment()
          }
        };
      }

      return result;
    },
    metadata: {
      description: 'Matches text against a pattern',
      examples: ['match(pattern("[A-Za-z]+"), "hello")'],
      category: 'core'
    }
  };

  // Register operation with Treenity node
  node.patches.apply({
    type: 'PATCH',
    path: node.path,
    operation: 'EDIT',
    value: {
      operations: {
        ...node.state.operations,
        [operation.name]: operation
      }
    }
  });

  return operation;
}
```

## 5. Integration with Treenity Trees

### 5.1 Treenity Node Mapping

Each linguistic operation and pattern is stored in a Treenity node:

```typescript
/**
 * Maps linguistic operations to Treenity node state
 *
 * @param operations Linguistic operations
 * @returns Treenity node state
 */
function mapOperationsToTreenityState(
  operations: Map<string, LinguisticOperation>
): Record<string, any> {
  return Array.from(operations.entries()).reduce((acc, [name, operation]) => {
    acc[name] = {
      name: operation.name,
      metadata: operation.metadata
    };
    return acc;
  }, {});
}

/**
 * Maps Treenity node state to linguistic operations
 *
 * @param state Treenity node state
 * @param implementations Operation implementations
 * @returns Linguistic operations
 */
function mapTreenityStateToOperations(
  state: Record<string, any>,
  implementations: Record<string, LinguisticOperation>
): Map<string, LinguisticOperation> {
  const operations = new Map<string, LinguisticOperation>();

  Object.entries(state.operations || {}).forEach(([name, data]) => {
    if (implementations[name]) {
      operations.set(name, {
        ...implementations[name],
        metadata: {
          ...implementations[name].metadata,
          ...(data as any).metadata
        }
      });
    }
  });

  return operations;
}
```

### 5.2 Patch Synchronization

Changes to linguistic operations and patterns are synchronized with Treenity nodes:

```typescript
/**
 * Synchronizes changes between linguistic operations and Treenity node
 *
 * @param operations Linguistic operations
 * @param node Treenity node
 */
function synchronizeOperationsWithTreenity(
  operations: Map<string, LinguisticOperation>,
  node: TreenityNode
): void {
  // Observe Treenity patches
  node.onPatch((patch) => {
    if (patch.operation === 'EDIT' && patch.value.operations) {
      // Update operations from Treenity
      Object.entries(patch.value.operations).forEach(([name, data]) => {
        if (operations.has(name)) {
          const operation = operations.get(name)!;
          operations.set(name, {
            ...operation,
            metadata: {
              ...operation.metadata,
              ...(data as any).metadata
            }
          });
        }
      });
    }
  });
}
```

## 6. Usage Examples

### 6.1 Basic Usage

```tsx
import { LinguisticsProvider, useLinguistics } from '@spicetime/linguistics';

// Component that uses linguistics
const LinguisticComponent = () => {
  const L = useLinguistics();

  // Use linguistics operations
  const helloToken = L.word('hello');
  const worldToken = L.word('world');
  const greeting = L.compose([helloToken, worldToken]);

  // Use pattern matching
  const wordPattern = L.pattern('[A-Za-z]+');
  const matches = L.match(wordPattern, 'hello');

  return (
    <div>
      <p>Greeting: {JSON.stringify(greeting)}</p>
      <p>Matches: {JSON.stringify(matches)}</p>
    </div>
  );
};

// App with linguistics provider
const App = () => {
  return (
    <LinguisticsProvider domain="example">
      <LinguisticComponent />
    </LinguisticsProvider>
  );
};
```

### 6.2 Using Linguistic Components

```tsx
import { LinguisticsProvider, Translate, Formality, Summarize } from '@spicetime/linguistics';

// Component that uses linguistic components
const DocumentProcessor = () => {
  return (
    <div>
      <Formality level="formal">
        <Translate to="spanish">
          Hello world
        </Translate>
      </Formality>

      <Summarize length="short">
        This is a long document that needs to be summarized.
        It contains multiple paragraphs and sentences.
        The summary should capture the main points.
      </Summarize>
    </div>
  );
};

// App with linguistics provider
const App = () => {
  return (
    <LinguisticsProvider domain="documents">
      <DocumentProcessor />
    </LinguisticsProvider>
  );
};
```

### 6.3 Using the `l` Tag

```tsx
import { LinguisticsProvider, l } from '@spicetime/linguistics';

// Component that uses the l tag
const LinguisticProcessor = () => {
  const result = l`
    <Formality level="formal">
      <Translate to="spanish">
        Hello world
      </Translate>
    </Formality>
  `;

  return (
    <div>
      <p>Result: {result}</p>
    </div>
  );
};

// App with linguistics provider
const App = () => {
  return (
    <LinguisticsProvider domain="processor">
      <LinguisticProcessor />
    </LinguisticsProvider>
  );
};
```

## 7. Integration with Parsimmon and Compromise

### 7.1 Parsimmon Integration

```typescript
import P from 'parsimmon';

/**
 * Creates base patterns using Parsimmon
 */
function createBasePatterns() {
  return P.createLanguage({
    word: () =>
      P.regexp(/[a-zA-Z]+/)
        .map(w => ({ type: 'word', value: w })),

    phrase: r =>
      r.word.sepBy(P.whitespace)
        .map(words => ({ type: 'phrase', values: words })),

    action: r =>
      r.word
        .map(w => ({ type: 'action', value: w }))
  });
}
```

### 7.2 Compromise Integration

```typescript
import nlp from 'compromise';

/**
 * Enhances a token with NLP information
 *
 * @param token Token to enhance
 * @returns Enhanced token
 */
function enhanceWithNlp(token: Token): Token {
  const doc = nlp(token.value);

  return {
    ...token,
    metadata: {
      ...token.metadata,
      nlp: {
        tags: doc.json()[0]?.terms.map(t => t.tags),
        entities: doc.json()[0]?.entities,
        sentiment: doc.sentiment()
      }
    }
  };
}
```

## 8. Future Rust Implementation

The system is designed to be compatible with a future Rust implementation:

```typescript
/**
 * Rust implementation of the linguistics system
 *
 * This is a placeholder for the future Rust implementation.
 * The API will remain the same, but the implementation will be in Rust.
 */
interface RustLinguistics {
  /**
   * Core operations
   */
  compose: (tokens: Token[]) => Token;
  word: (value: string) => Token;
  phrase: (words: string[]) => Token;
  pattern: (pattern: string) => Token;
  parse: (text: string) => Token[];
  match: (pattern: Token, text: string) => boolean;

  /**
   * Advanced operations
   */
  translate: (text: string, to: string, from?: string) => string;
  formality: (text: string, level: 'formal' | 'neutral' | 'informal') => string;
  summarize: (text: string, length: 'short' | 'medium' | 'long') => string;
}

/**
 * Creates a Rust implementation of the linguistics system
 *
 * @returns Rust implementation
 */
function createRustLinguistics(): RustLinguistics {
  // This would be implemented in Rust and exposed via WebAssembly
  return {
    compose: (tokens: Token[]) => {
      // Call Rust implementation
      return { type: 'composed', value: tokens, metadata: {} };
    },
    word: (value: string) => {
      // Call Rust implementation
      return { type: 'word', value, metadata: {} };
    },
    // Other operations...
  };
}
```

## 9. Implementation Requirements

Implementations of this specification must:

1. Extend Treenity core trees with linguistic capabilities
2. Provide a React HOC/Provider component for context injection
3. Implement linguistic operations as both functional components and pure functions
4. Integrate with Parsimmon for parsing and Compromise for NLP
5. Support the `l` tag for JSX-like linguistic expressions
6. Maintain compatibility with a future Rust implementation
7. Provide efficient pattern matching and linguistic processing

## 10. Integration with cat_types

### 10.1 Extending the cat_types Context

The linguistics.stx package extends the cat_types context with linguistics-specific operations and types:

```typescript
/**
 * Combined context type that includes both cat_types and linguistics
 */
interface LinguisticsCatTypesContextType extends CatTypesContextType, LinguisticsContextType {}

/**
 * Hook for accessing the combined context
 */
function useLinguisticsCatTypes(): LinguisticsCatTypesContextType {
  const context = useContext(LinguisticsContext);

  if (!context) {
    throw new Error('useLinguisticsCatTypes must be used within a LinguisticsProvider');
  }

  return context;
}

/**
 * HOC for injecting the combined context into a component
 */
function withLinguisticsCatTypes<P>(Component: React.ComponentType<P & LinguisticsCatTypesContextType>) {
  return function WithLinguisticsCatTypesComponent(props: P) {
    return (
      <LinguisticsContext.Consumer>
        {(context) => <Component {...props} {...context} />}
      </LinguisticsContext.Consumer>
    );
  };
}
```

### 10.2 Using cat_types and linguistics Together

```tsx
import { LinguisticsProvider, useLinguisticsCatTypes } from '@spicetime/linguistics';

// Component that uses both cat_types and linguistics
const CombinedComponent = () => {
  // Access both cat_types and linguistics operations
  const {
    // From cat_types
    createCategory,
    createCatObject,
    createMorphism,

    // From linguistics
    compose,
    word,
    phrase,
    pattern,
    parse,
    match
  } = useLinguisticsCatTypes();

  // Use cat_types operations
  const category = createCategory();
  const objA = createCatObject('A');
  const objB = createCatObject('B');
  const morphism = createMorphism(objA, objB, (a) => b);
  category.addObject(objA);
  category.addObject(objB);
  category.addMorphism(morphism);

  // Use linguistics operations
  const helloToken = word('hello');
  const worldToken = word('world');
  const greeting = compose([helloToken, worldToken]);

  return (
    <div>
      <p>Category: {category.id}</p>
      <p>Greeting: {JSON.stringify(greeting)}</p>
    </div>
  );
};

// App with linguistics provider
const App = () => {
  return (
    <LinguisticsProvider>
      <CombinedComponent />
    </LinguisticsProvider>
  );
};
```

### 10.3 Combining with Other Extensions

```tsx
import { LinguisticsProvider } from '@spicetime/linguistics';
import { TimeCatTypesProvider } from '@spicetime/time_cat_types';
import { ForestCatTypesProvider } from '@spicetime/forestry_cat_types';

// App with multiple cat types extensions
const App = () => {
  return (
    <LinguisticsProvider>
      <TimeCatTypesProvider>
        <ForestCatTypesProvider>
          <FullyExtendedComponent />
        </ForestCatTypesProvider>
      </TimeCatTypesProvider>
    </LinguisticsProvider>
  );
};
```

## 11. Conclusion

The Linguistics Categorical Type System provides a powerful mechanism for linguistic operations as functional components. By integrating with Treenity core trees and leveraging Parsimmon and Compromise, it enables a wide range of linguistic capabilities while maintaining a clean, composable API. The system is designed to be compatible with a future Rust implementation, ensuring performance and portability across different environments.

As a Higher-Order Component that extends the cat_types context, linguistics.stx seamlessly integrates linguistic concepts into the broader categorical framework, allowing for powerful compositions and transformations across different domains.
