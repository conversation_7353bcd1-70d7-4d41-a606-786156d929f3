openapi: 3.0.3
info:
  title: SpiceTime Service Registry
  version: 1.0.0
  description: Central registry for service discovery and schema management across the SpiceTime ecosystem

paths:
  /services:
    get:
      summary: List registered services
      operationId: listServices
      parameters:
        - name: type
          in: query
          schema:
            type: string
            enum: [core, pragma, middleware, utility]
        - name: status
          in: query
          schema:
            type: string
            enum: [active, inactive, deprecated]
      responses:
        '200':
          description: List of services
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ServiceInfo'

    post:
      summary: Register a new service
      operationId: registerService
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ServiceRegistration'
      responses:
        '201':
          description: Service registered successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServiceInfo'

  /services/{serviceId}:
    get:
      summary: Get service details
      operationId: getService
      parameters:
        - name: serviceId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Service details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServiceDetails'

    patch:
      summary: Update service
      operationId: updateService
      parameters:
        - name: serviceId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ServiceUpdate'
      responses:
        '200':
          description: Service updated successfully

    delete:
      summary: Unregister service
      operationId: unregisterService
      parameters:
        - name: serviceId
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: Service unregistered successfully

  /schemas:
    get:
      summary: List available schemas
      operationId: listSchemas
      parameters:
        - name: type
          in: query
          schema:
            type: string
            enum: [openapi, json-schema, event, middleware]
        - name: service
          in: query
          schema:
            type: string
      responses:
        '200':
          description: List of schemas
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SchemaInfo'

    post:
      summary: Register a new schema
      operationId: registerSchema
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SchemaRegistration'
      responses:
        '201':
          description: Schema registered successfully

  /schemas/{schemaId}:
    get:
      summary: Get schema definition
      operationId: getSchema
      parameters:
        - name: schemaId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Schema definition
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SchemaDefinition'

  /events/types:
    get:
      summary: List available event types
      operationId: listEventTypes
      responses:
        '200':
          description: List of event types
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/EventTypeInfo'

    post:
      summary: Register new event type
      operationId: registerEventType
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EventTypeRegistration'
      responses:
        '201':
          description: Event type registered successfully

  /dependencies:
    get:
      summary: Get service dependency graph
      operationId: getDependencyGraph
      responses:
        '200':
          description: Service dependency graph
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DependencyGraph'

components:
  schemas:
    # Service Management
    ServiceRegistration:
      type: object
      required:
        - name
        - version
        - type
        - endpoints
      properties:
        name:
          type: string
          description: Service name
        version:
          type: string
          description: Service version (semver)
        type:
          type: string
          enum: [core, pragma, middleware, utility]
        description:
          type: string
        endpoints:
          type: array
          items:
            $ref: '#/components/schemas/ServiceEndpoint'
        schemas:
          type: array
          items:
            type: string
          description: Schema IDs provided by this service
        events:
          type: object
          properties:
            produces:
              type: array
              items:
                type: string
              description: Event types this service produces
            consumes:
              type: array
              items:
                type: string
              description: Event types this service consumes
        dependencies:
          type: array
          items:
            $ref: '#/components/schemas/ServiceDependency'
        metadata:
          type: object
          additionalProperties: true

    ServiceInfo:
      allOf:
        - $ref: '#/components/schemas/ServiceRegistration'
        - type: object
          required:
            - id
            - status
            - registeredAt
          properties:
            id:
              type: string
              format: uuid
            status:
              type: string
              enum: [active, inactive, deprecated, error]
            registeredAt:
              type: number
            lastSeen:
              type: number
            health:
              $ref: '#/components/schemas/ServiceHealth'

    ServiceDetails:
      allOf:
        - $ref: '#/components/schemas/ServiceInfo'
        - type: object
          properties:
            metrics:
              $ref: '#/components/schemas/ServiceMetrics'
            configuration:
              type: object
              additionalProperties: true

    ServiceUpdate:
      type: object
      properties:
        version:
          type: string
        status:
          type: string
          enum: [active, inactive, deprecated]
        endpoints:
          type: array
          items:
            $ref: '#/components/schemas/ServiceEndpoint'
        metadata:
          type: object
          additionalProperties: true

    ServiceEndpoint:
      type: object
      required:
        - url
        - protocol
      properties:
        url:
          type: string
          format: uri
        protocol:
          type: string
          enum: [http, https, ws, wss, grpc]
        methods:
          type: array
          items:
            type: string
        authentication:
          type: object
          properties:
            type:
              type: string
              enum: [none, bearer, apikey, oauth2]
            required:
              type: boolean

    ServiceDependency:
      type: object
      required:
        - service
        - version
      properties:
        service:
          type: string
        version:
          type: string
        optional:
          type: boolean
          default: false
        reason:
          type: string

    ServiceHealth:
      type: object
      required:
        - status
        - lastCheck
      properties:
        status:
          type: string
          enum: [healthy, degraded, unhealthy, unknown]
        lastCheck:
          type: number
        responseTime:
          type: number
        uptime:
          type: number
        errors:
          type: array
          items:
            type: string

    ServiceMetrics:
      type: object
      properties:
        requests:
          type: object
          properties:
            total:
              type: number
            rate:
              type: number
            errors:
              type: number
        latency:
          type: object
          properties:
            average:
              type: number
            p95:
              type: number
            p99:
              type: number
        resources:
          type: object
          properties:
            cpu:
              type: number
            memory:
              type: number
            connections:
              type: number

    # Schema Management
    SchemaRegistration:
      type: object
      required:
        - name
        - version
        - type
        - definition
      properties:
        name:
          type: string
        version:
          type: string
        type:
          type: string
          enum: [openapi, json-schema, event, middleware]
        definition:
          type: object
          additionalProperties: true
        service:
          type: string
          description: Service that owns this schema
        dependencies:
          type: array
          items:
            type: string
          description: Schema dependencies

    SchemaInfo:
      allOf:
        - $ref: '#/components/schemas/SchemaRegistration'
        - type: object
          required:
            - id
            - registeredAt
          properties:
            id:
              type: string
              format: uuid
            registeredAt:
              type: number
            usage:
              type: object
              properties:
                services:
                  type: number
                requests:
                  type: number

    SchemaDefinition:
      type: object
      required:
        - schema
        - metadata
      properties:
        schema:
          type: object
          additionalProperties: true
        metadata:
          type: object
          properties:
            name:
              type: string
            version:
              type: string
            type:
              type: string
            service:
              type: string
            dependencies:
              type: array
              items:
                type: string

    # Event Type Management
    EventTypeRegistration:
      type: object
      required:
        - name
        - category
        - schema
      properties:
        name:
          type: string
          description: Event type name
        category:
          type: string
          enum: [lifecycle, state, error, pipeline, kernel, integration]
        schema:
          type: object
          additionalProperties: true
          description: Event data schema
        description:
          type: string
        producers:
          type: array
          items:
            type: string
          description: Services that can produce this event
        consumers:
          type: array
          items:
            type: string
          description: Services that consume this event

    EventTypeInfo:
      allOf:
        - $ref: '#/components/schemas/EventTypeRegistration'
        - type: object
          required:
            - id
            - registeredAt
          properties:
            id:
              type: string
              format: uuid
            registeredAt:
              type: number
            usage:
              type: object
              properties:
                produced:
                  type: number
                consumed:
                  type: number

    # Dependency Management
    DependencyGraph:
      type: object
      required:
        - nodes
        - edges
      properties:
        nodes:
          type: array
          items:
            $ref: '#/components/schemas/DependencyNode'
        edges:
          type: array
          items:
            $ref: '#/components/schemas/DependencyEdge'
        metadata:
          type: object
          properties:
            cycles:
              type: array
              items:
                type: array
                items:
                  type: string
            depth:
              type: number

    DependencyNode:
      type: object
      required:
        - id
        - type
        - name
      properties:
        id:
          type: string
        type:
          type: string
          enum: [service, schema, event]
        name:
          type: string
        version:
          type: string
        status:
          type: string

    DependencyEdge:
      type: object
      required:
        - from
        - to
        - type
      properties:
        from:
          type: string
        to:
          type: string
        type:
          type: string
          enum: [depends, produces, consumes, implements]
        optional:
          type: boolean
        version:
          type: string
