openapi: 3.0.3
info:
  title: SpiceTime Kernel Service
  version: 1.0.0
  description: Central coordination service for task scheduling, resource management, and priority synchronization

paths:
  /tasks:
    get:
      summary: List tasks
      operationId: listTasks
      parameters:
        - name: status
          in: query
          schema:
            type: string
            enum: [pending, approved, executing, completed, failed, cancelled]
        - name: priority
          in: query
          schema:
            type: string
            enum: [low, normal, high, critical]
        - name: limit
          in: query
          schema:
            type: number
            default: 100
      responses:
        '200':
          description: List of tasks
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/TaskInfo'

    post:
      summary: Schedule a new task
      operationId: scheduleTask
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TaskScheduleRequest'
      responses:
        '201':
          description: Task scheduled successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskInfo'

  /tasks/{taskId}:
    get:
      summary: Get task details
      operationId: getTask
      parameters:
        - name: taskId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Task details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskDetails'

    patch:
      summary: Update task
      operationId: updateTask
      parameters:
        - name: taskId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TaskUpdateRequest'
      responses:
        '200':
          description: Task updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskInfo'

    delete:
      summary: Cancel task
      operationId: cancelTask
      parameters:
        - name: taskId
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: Task cancelled successfully

  /tasks/{taskId}/approve:
    post:
      summary: Approve task for execution
      operationId: approveTask
      parameters:
        - name: taskId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Task approved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskApproval'

  /resources:
    get:
      summary: Get resource status
      operationId: getResourceStatus
      responses:
        '200':
          description: Current resource status
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResourceStatus'

    post:
      summary: Request resource allocation
      operationId: requestResources
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResourceRequest'
      responses:
        '201':
          description: Resource allocation created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResourceAllocation'

  /resources/{allocationId}:
    delete:
      summary: Release resource allocation
      operationId: releaseResources
      parameters:
        - name: allocationId
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: Resources released successfully

  /quotas:
    get:
      summary: Get resource quotas
      operationId: getQuotas
      responses:
        '200':
          description: Current resource quotas
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResourceQuotas'

    patch:
      summary: Update resource quotas
      operationId: updateQuotas
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/QuotaUpdateRequest'
      responses:
        '200':
          description: Quotas updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResourceQuotas'

  /metrics:
    get:
      summary: Get kernel metrics
      operationId: getMetrics
      parameters:
        - name: timeRange
          in: query
          schema:
            type: string
            enum: [1h, 6h, 24h, 7d, 30d]
            default: 1h
      responses:
        '200':
          description: Kernel performance metrics
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/KernelMetrics'

components:
  schemas:
    # Task Management
    TaskScheduleRequest:
      type: object
      required:
        - type
        - priority
        - payload
      properties:
        type:
          type: string
          description: Task type identifier
        priority:
          type: string
          enum: [low, normal, high, critical]
        payload:
          type: object
          additionalProperties: true
          description: Task-specific data
        resourceRequirements:
          type: array
          items:
            $ref: '#/components/schemas/ResourceRequirement'
        dependencies:
          type: array
          items:
            type: string
          description: Task IDs this task depends on
        timeout:
          type: number
          description: Task timeout in milliseconds
        retryPolicy:
          $ref: '#/components/schemas/RetryPolicy'
        metadata:
          type: object
          additionalProperties: true

    TaskInfo:
      type: object
      required:
        - id
        - type
        - status
        - priority
        - createdAt
      properties:
        id:
          type: string
          format: uuid
        type:
          type: string
        status:
          type: string
          enum: [pending, approved, executing, completed, failed, cancelled]
        priority:
          type: string
          enum: [low, normal, high, critical]
        createdAt:
          type: number
        approvedAt:
          type: number
        startedAt:
          type: number
        completedAt:
          type: number
        duration:
          type: number
          description: Execution duration in milliseconds
        progress:
          type: number
          minimum: 0
          maximum: 100
          description: Task progress percentage

    TaskDetails:
      allOf:
        - $ref: '#/components/schemas/TaskInfo'
        - type: object
          properties:
            payload:
              type: object
              additionalProperties: true
            result:
              type: object
              additionalProperties: true
            error:
              type: string
            resourceAllocations:
              type: array
              items:
                $ref: '#/components/schemas/ResourceAllocation'
            dependencies:
              type: array
              items:
                type: string
            dependents:
              type: array
              items:
                type: string
            retryCount:
              type: number
            logs:
              type: array
              items:
                $ref: '#/components/schemas/TaskLogEntry'

    TaskUpdateRequest:
      type: object
      properties:
        priority:
          type: string
          enum: [low, normal, high, critical]
        timeout:
          type: number
        metadata:
          type: object
          additionalProperties: true

    TaskApproval:
      type: object
      required:
        - taskId
        - approvedAt
        - allocatedResources
      properties:
        taskId:
          type: string
        approvedAt:
          type: number
        allocatedResources:
          type: array
          items:
            $ref: '#/components/schemas/ResourceAllocation'
        estimatedDuration:
          type: number

    TaskLogEntry:
      type: object
      required:
        - timestamp
        - level
        - message
      properties:
        timestamp:
          type: number
        level:
          type: string
          enum: [debug, info, warn, error]
        message:
          type: string
        data:
          type: object
          additionalProperties: true

    RetryPolicy:
      type: object
      properties:
        maxAttempts:
          type: number
          default: 3
        backoffStrategy:
          type: string
          enum: [fixed, exponential, linear]
          default: exponential
        baseDelay:
          type: number
          default: 1000
          description: Base delay in milliseconds
        maxDelay:
          type: number
          default: 30000
          description: Maximum delay in milliseconds

    # Resource Management
    ResourceRequirement:
      type: object
      required:
        - type
        - amount
      properties:
        type:
          type: string
          enum: [cpu, memory, storage, network, time]
        amount:
          type: number
        duration:
          type: number
          description: Required duration in milliseconds
        priority:
          type: string
          enum: [low, normal, high, critical]

    ResourceRequest:
      type: object
      required:
        - requirements
        - requesterId
      properties:
        requirements:
          type: array
          items:
            $ref: '#/components/schemas/ResourceRequirement'
        requesterId:
          type: string
          description: ID of the requesting entity
        justification:
          type: string
        timeout:
          type: number
          description: Request timeout in milliseconds

    ResourceAllocation:
      type: object
      required:
        - id
        - type
        - amount
        - allocatedAt
        - requesterId
      properties:
        id:
          type: string
          format: uuid
        type:
          type: string
          enum: [cpu, memory, storage, network, time]
        amount:
          type: number
        allocatedAt:
          type: number
        expiresAt:
          type: number
        requesterId:
          type: string
        taskId:
          type: string
        usage:
          $ref: '#/components/schemas/ResourceUsage'

    ResourceUsage:
      type: object
      properties:
        used:
          type: number
        peak:
          type: number
        average:
          type: number
        efficiency:
          type: number
          minimum: 0
          maximum: 1

    ResourceStatus:
      type: object
      required:
        - resources
        - timestamp
      properties:
        resources:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/ResourceInfo'
        timestamp:
          type: number

    ResourceInfo:
      type: object
      required:
        - type
        - total
        - allocated
        - available
      properties:
        type:
          type: string
          enum: [cpu, memory, storage, network, time]
        total:
          type: number
        allocated:
          type: number
        available:
          type: number
        reserved:
          type: number
        utilization:
          type: number
          minimum: 0
          maximum: 1

    ResourceQuotas:
      type: object
      properties:
        global:
          type: object
          additionalProperties:
            type: number
        perService:
          type: object
          additionalProperties:
            type: object
            additionalProperties:
              type: number
        perUser:
          type: object
          additionalProperties:
            type: object
            additionalProperties:
              type: number

    QuotaUpdateRequest:
      type: object
      properties:
        global:
          type: object
          additionalProperties:
            type: number
        perService:
          type: object
          additionalProperties:
            type: object
            additionalProperties:
              type: number
        perUser:
          type: object
          additionalProperties:
            type: object
            additionalProperties:
              type: number

    # Metrics and Monitoring
    KernelMetrics:
      type: object
      required:
        - timestamp
        - tasks
        - resources
        - performance
      properties:
        timestamp:
          type: number
        tasks:
          $ref: '#/components/schemas/TaskMetrics'
        resources:
          $ref: '#/components/schemas/ResourceMetrics'
        performance:
          $ref: '#/components/schemas/PerformanceMetrics'

    TaskMetrics:
      type: object
      properties:
        total:
          type: number
        byStatus:
          type: object
          additionalProperties:
            type: number
        byPriority:
          type: object
          additionalProperties:
            type: number
        averageWaitTime:
          type: number
        averageExecutionTime:
          type: number
        throughput:
          type: number
          description: Tasks per second
        successRate:
          type: number
          minimum: 0
          maximum: 1

    ResourceMetrics:
      type: object
      properties:
        utilization:
          type: object
          additionalProperties:
            type: number
        allocation:
          type: object
          additionalProperties:
            type: number
        contention:
          type: object
          additionalProperties:
            type: number
        efficiency:
          type: object
          additionalProperties:
            type: number

    PerformanceMetrics:
      type: object
      properties:
        latency:
          type: object
          properties:
            p50:
              type: number
            p95:
              type: number
            p99:
              type: number
        errorRate:
          type: number
          minimum: 0
          maximum: 1
        availability:
          type: number
          minimum: 0
          maximum: 1
