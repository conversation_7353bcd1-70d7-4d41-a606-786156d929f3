openapi: 3.0.3
info:
  title: SpiceTime Middleware Service
  version: 1.0.0
  description: Middleware plugin system for intercepting and modifying pipeline behavior

paths:
  /plugins:
    get:
      summary: List registered plugins
      operationId: listPlugins
      responses:
        '200':
          description: List of registered plugins
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PluginInfo'
    
    post:
      summary: Register a new plugin
      operationId: registerPlugin
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PluginRegistration'
      responses:
        '201':
          description: Plugin registered successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PluginInfo'
        '400':
          description: Invalid plugin registration
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /plugins/{pluginId}:
    get:
      summary: Get plugin details
      operationId: getPlugin
      parameters:
        - name: pluginId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Plugin details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PluginDetails'
    
    delete:
      summary: Unregister plugin
      operationId: unregisterPlugin
      parameters:
        - name: pluginId
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: Plugin unregistered successfully

  /events:
    post:
      summary: Process event through middleware pipeline
      operationId: processEvent
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EventProcessingRequest'
      responses:
        '200':
          description: Event processed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EventProcessingResponse'

  /hooks/{eventType}:
    get:
      summary: List hooks for event type
      operationId: listHooks
      parameters:
        - name: eventType
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: List of hooks for event type
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/HookInfo'

components:
  schemas:
    # Plugin Management
    PluginRegistration:
      type: object
      required:
        - name
        - version
        - hooks
      properties:
        name:
          type: string
          description: Plugin name
        version:
          type: string
          description: Plugin version (semver)
        description:
          type: string
          description: Plugin description
        author:
          type: string
          description: Plugin author
        priority:
          type: number
          default: 100
          description: Plugin execution priority (lower = higher priority)
        hooks:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/HookDefinition'
          description: Event hooks provided by the plugin
        config:
          type: object
          additionalProperties: true
          description: Plugin configuration
        dependencies:
          type: array
          items:
            type: string
          description: Plugin dependencies

    PluginInfo:
      allOf:
        - $ref: '#/components/schemas/PluginRegistration'
        - type: object
          required:
            - id
            - status
            - registeredAt
          properties:
            id:
              type: string
              format: uuid
              description: Unique plugin identifier
            status:
              type: string
              enum: [active, inactive, error]
              description: Plugin status
            registeredAt:
              type: number
              description: Registration timestamp
            lastExecuted:
              type: number
              description: Last execution timestamp
            executionCount:
              type: number
              description: Total execution count
            errorCount:
              type: number
              description: Total error count

    PluginDetails:
      allOf:
        - $ref: '#/components/schemas/PluginInfo'
        - type: object
          properties:
            metrics:
              $ref: '#/components/schemas/PluginMetrics'
            logs:
              type: array
              items:
                $ref: '#/components/schemas/PluginLogEntry'

    PluginMetrics:
      type: object
      properties:
        averageExecutionTime:
          type: number
          description: Average execution time in milliseconds
        successRate:
          type: number
          description: Success rate (0-1)
        resourceUsage:
          type: object
          properties:
            cpu:
              type: number
            memory:
              type: number
            network:
              type: number

    PluginLogEntry:
      type: object
      required:
        - timestamp
        - level
        - message
      properties:
        timestamp:
          type: number
        level:
          type: string
          enum: [debug, info, warn, error]
        message:
          type: string
        data:
          type: object
          additionalProperties: true

    # Hook System
    HookDefinition:
      type: object
      required:
        - handler
      properties:
        handler:
          type: string
          description: Handler function reference
        async:
          type: boolean
          default: true
          description: Whether hook is asynchronous
        timeout:
          type: number
          default: 30000
          description: Hook timeout in milliseconds
        retries:
          type: number
          default: 0
          description: Number of retries on failure
        conditions:
          type: array
          items:
            $ref: '#/components/schemas/HookCondition'
          description: Conditions for hook execution

    HookCondition:
      type: object
      required:
        - field
        - operator
        - value
      properties:
        field:
          type: string
          description: Field to check in event data
        operator:
          type: string
          enum: [equals, notEquals, contains, startsWith, endsWith, greaterThan, lessThan]
        value:
          description: Value to compare against

    HookInfo:
      type: object
      required:
        - pluginId
        - pluginName
        - hookName
        - priority
      properties:
        pluginId:
          type: string
        pluginName:
          type: string
        hookName:
          type: string
        priority:
          type: number
        conditions:
          type: array
          items:
            $ref: '#/components/schemas/HookCondition'

    # Event Processing
    EventProcessingRequest:
      type: object
      required:
        - event
        - context
      properties:
        event:
          type: object
          additionalProperties: true
          description: Event to process
        context:
          $ref: '#/components/schemas/ProcessingContext'

    ProcessingContext:
      type: object
      required:
        - taskId
        - stage
      properties:
        taskId:
          type: string
          description: Current task identifier
        stage:
          type: string
          description: Current pipeline stage
        priority:
          type: string
          enum: [low, normal, high]
        timeout:
          type: number
          description: Processing timeout
        metadata:
          type: object
          additionalProperties: true

    EventProcessingResponse:
      type: object
      required:
        - result
        - processedBy
      properties:
        result:
          $ref: '#/components/schemas/ProcessingResult'
        processedBy:
          type: array
          items:
            type: string
          description: List of plugins that processed the event
        duration:
          type: number
          description: Total processing duration
        errors:
          type: array
          items:
            $ref: '#/components/schemas/ProcessingError'

    ProcessingResult:
      type: object
      properties:
        action:
          type: string
          enum: [continue, abort, reschedule, modify]
        modifiedEvent:
          type: object
          additionalProperties: true
          description: Modified event (if action is 'modify')
        rescheduleOptions:
          $ref: '#/components/schemas/RescheduleOptions'
        abortReason:
          type: string
          description: Reason for abort (if action is 'abort')
        resourceRequests:
          type: array
          items:
            $ref: '#/components/schemas/ResourceRequest'

    RescheduleOptions:
      type: object
      properties:
        priority:
          type: string
          enum: [low, normal, high]
        delay:
          type: number
          description: Delay in milliseconds
        reason:
          type: string

    ResourceRequest:
      type: object
      required:
        - type
        - amount
      properties:
        type:
          type: string
          enum: [cpu, memory, storage, network, time]
        amount:
          type: number
        priority:
          type: string
          enum: [low, normal, high]
        justification:
          type: string

    ProcessingError:
      type: object
      required:
        - pluginId
        - error
      properties:
        pluginId:
          type: string
        error:
          type: string
        stack:
          type: string
        recoverable:
          type: boolean

    ErrorResponse:
      type: object
      required:
        - error
        - message
      properties:
        error:
          type: string
        message:
          type: string
        details:
          type: object
          additionalProperties: true
