# SpiceTime Event System

## Overview

Foundational event system for the SpiceTime ecosystem. Provides standardized event structures, schemas, and interfaces that all packages can use without reimplementing event handling.

## Service Architecture

```
_services.hDict/
├── events/                    # Event system service
│   ├── schemas/              # Event schemas
│   ├── types/                # Event type definitions
│   └── handlers/             # Event handling patterns
├── middleware/               # Middleware service
│   ├── schemas/              # Middleware schemas
│   ├── registry/             # Plugin registry
│   └── execution/            # Execution engine
├── kernel/                   # Kernel coordination service
│   ├── schemas/              # Kernel API schemas
│   ├── scheduling/           # Task scheduling
│   └── resources/            # Resource management
└── schemas/                  # Shared schema utilities
    ├── openapi/              # OpenAPI utilities
    ├── validation/           # Schema validation
    └── generation/           # Code generation from schemas
```

## Event Categories

### Core Events
- **Lifecycle Events**: start, stop, pause, resume
- **State Events**: created, updated, deleted, synced
- **Error Events**: error, warning, recovery, timeout

### Pipeline Events  
- **Parse Events**: beforeParse, afterParse, parseError
- **Generate Events**: beforeGenerate, afterGenerate, generateError
- **Transform Events**: beforeTransform, afterTransform, transformError

### Kernel Events
- **Task Events**: taskScheduled, taskApproved, taskCompleted, taskFailed
- **Resource Events**: resourceRequested, resourceAllocated, resourceExhausted
- **Priority Events**: priorityChanged, priorityEscalated

### Integration Events
- **Pragma Events**: pragmaComposed, pragmaIntegrated, pragmaConflict
- **Service Events**: serviceRegistered, serviceDiscovered, serviceUnavailable

## Usage Pattern

Packages specify their event types and data structures by referencing the foundational schemas:

```typescript
// In package spec
import { EventType, EventData } from '@spicetime/events';

interface MyPackageEvents {
  parse: EventType<'parse', ParseEventData>;
  generate: EventType<'generate', GenerateEventData>;
  custom: EventType<'myCustomEvent', MyCustomEventData>;
}
```

## Benefits

- **Modularity**: Each service is independent and reusable
- **Evolution**: Event schemas can evolve without breaking packages
- **Consistency**: Standardized event patterns across ecosystem
- **Extensibility**: New event types can be added without changing existing code
- **Type Safety**: Full TypeScript support with schema validation
