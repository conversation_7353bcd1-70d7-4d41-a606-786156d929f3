# Chain Pragma

## Description

Implements sequential composition patterns for pragmas and data structures. Works in conjunction with the 'of' linguistic construct to enable natural language composition like `chain_of_(obj_of_types)`. Manages child pragmas and provides the implementation mechanics for chaining operations.

## Dual Nature with Linguistic System

### Pragma Responsibilities
- **Manages child pragmas** and their execution order
- **Handles composition logic** and data flow between components
- **Provides implementation** of the actual chaining functionality
- **Maintains execution state** and error handling

### Linguistic Integration
- **Works with 'of' construct** to form `chain_of_(parameter)` expressions
- **Feeds data to linguistic layer** for natural language processing
- **Receives composition requests** from vocabulary mapping system
- **Enables natural composition** through pragma-vocabulary mapping

## Chain Architecture

### Core Chain Interface
```typescript
interface ChainPragma extends STPragma {
  // Chain configuration
  type: ChainType;
  strategy: ChainStrategy;
  
  // Child management
  children: ChainedPragma[];
  childOrder: string[];
  
  // Execution control
  execution: ExecutionModel;
  errorHandling: ErrorHandlingStrategy;
  
  // Data flow
  dataFlow: DataFlowDefinition;
  transformations: TransformationRule[];
  
  // Composition operations
  compose(pragmas: Pragma[], outputType: Type): ChainedComposition;
  append(pragma: Pragma): ChainPragma;
  prepend(pragma: Pragma): ChainPragma;
  insert(pragma: Pragma, position: number): ChainPragma;
  
  // Execution operations
  execute(input: any): Promise<any>;
  executeStep(step: number, input: any): Promise<any>;
  executeRange(start: number, end: number, input: any): Promise<any>;
  
  // Linguistic interface
  linguisticInterface: ChainLinguisticInterface;
}
```

### Chain Types
```typescript
enum ChainType {
  // Sequential processing
  SEQUENTIAL = 'sequential',           // One after another
  PIPELINE = 'pipeline',              // Output of one feeds next
  WATERFALL = 'waterfall',            // Each step depends on previous
  
  // Conditional processing
  CONDITIONAL = 'conditional',         // Steps based on conditions
  BRANCHING = 'branching',            // Multiple paths possible
  SWITCH = 'switch',                  // Single path selection
  
  // Iterative processing
  LOOP = 'loop',                      // Repeat until condition
  MAP = 'map',                        // Apply to each item
  REDUCE = 'reduce',                  // Accumulate results
  
  // Parallel variants
  PARALLEL_PIPELINE = 'parallel-pipeline',  // Parallel execution, sequential data
  FORK_JOIN = 'fork-join',                 // Split, process, merge
  
  // Error handling variants
  RESILIENT = 'resilient',            // Continue on errors
  CIRCUIT_BREAKER = 'circuit-breaker', // Stop on repeated failures
  RETRY = 'retry'                     // Retry failed steps
}
```

### Chain Strategies
```typescript
interface ChainStrategy {
  // Execution strategy
  execution: 'eager' | 'lazy' | 'streaming' | 'batch';
  
  // Error handling
  errorHandling: 'fail-fast' | 'continue' | 'retry' | 'fallback';
  
  // Resource management
  resourceStrategy: 'sequential' | 'parallel' | 'adaptive';
  
  // Optimization
  optimization: 'none' | 'memoization' | 'caching' | 'fusion';
}
```

## Linguistic Integration

### Chain-Of Pattern
```typescript
interface ChainOfPattern {
  // Pattern recognition
  pattern: 'chain_of_(type)';
  
  // Parameter extraction
  extractParameter(expression: string): ChainParameter;
  
  // Type resolution
  resolveOutputType(parameter: ChainParameter): Type;
  
  // Pragma coordination
  coordinateWithPragma(pragma: ChainPragma, parameter: ChainParameter): ChainConfiguration;
}

class ChainOfPatternHandler {
  handle(expression: string): ChainConfiguration {
    // Parse: chain_of_(obj_of_types)
    const match = expression.match(/chain_of_\(([^)]+)\)/);
    if (!match) throw new Error(`Invalid chain_of pattern: ${expression}`);
    
    const parameter = match[1]; // obj_of_types
    
    // Resolve output type
    const outputType = this.typeSystem.resolve(parameter);
    
    // Create chain configuration
    return {
      type: ChainType.PIPELINE,
      outputType,
      strategy: {
        execution: 'eager',
        errorHandling: 'fail-fast',
        resourceStrategy: 'sequential',
        optimization: 'none'
      },
      dataFlow: {
        inputType: 'any',
        outputType,
        transformations: this.inferTransformations(outputType)
      }
    };
  }
}
```

### Vocabulary Mapping
```typescript
interface ChainVocabularyMapping {
  // Vocabulary terms that map to chain pragma
  vocabulary: string[];
  
  // Grammar patterns
  patterns: GrammarPattern[];
  
  // Parameter mappings
  parameterMappings: ParameterMapping[];
}

const chainVocabularyMapping: ChainVocabularyMapping = {
  vocabulary: ['chain', 'sequence', 'pipeline', 'flow', 'stream'],
  
  patterns: [
    {
      pattern: 'chain_of_(type)',
      maps_to: 'compose',
      requires: ['type'],
      example: 'chain_of_(obj_of_types)'
    },
    {
      pattern: 'chain_with_(config)',
      maps_to: 'configuredCompose',
      requires: ['config'],
      example: 'chain_with_(error_handling: retry)'
    },
    {
      pattern: 'X_then_Y',
      maps_to: 'sequence',
      requires: ['first', 'second'],
      example: 'parse_then_validate'
    }
  ],
  
  parameterMappings: [
    {
      pattern: '(obj_of_types)',
      type: 't.obj_of_types',
      description: 'Chain of objects with specified types'
    },
    {
      pattern: '(list_of_items)',
      type: 't.list_of_items',
      description: 'Chain of list items'
    }
  ]
};
```

## Child Pragma Management

### Chained Pragma Interface
```typescript
interface ChainedPragma {
  // Pragma reference
  pragma: Pragma;
  
  // Chain position
  position: number;
  dependencies: string[];
  
  // Input/output specification
  inputType: Type;
  outputType: Type;
  transformation: TransformationFunction;
  
  // Execution configuration
  timeout: number;
  retries: number;
  fallback?: Pragma;
  
  // Conditional execution
  condition?: ConditionFunction;
  skipOnError?: boolean;
}

class ChainedPragmaManager {
  private pragmas: Map<string, ChainedPragma> = new Map();
  
  add(name: string, pragma: Pragma, config: ChainedPragmaConfig): void {
    const chainedPragma: ChainedPragma = {
      pragma,
      position: config.position || this.pragmas.size,
      dependencies: config.dependencies || [],
      inputType: config.inputType || 'any',
      outputType: config.outputType || 'any',
      transformation: config.transformation || ((x) => x),
      timeout: config.timeout || 30000,
      retries: config.retries || 0,
      fallback: config.fallback,
      condition: config.condition,
      skipOnError: config.skipOnError || false
    };
    
    this.pragmas.set(name, chainedPragma);
    this.reorderPragmas();
  }
  
  remove(name: string): void {
    this.pragmas.delete(name);
    this.reorderPragmas();
  }
  
  getExecutionOrder(): ChainedPragma[] {
    return Array.from(this.pragmas.values())
      .sort((a, b) => a.position - b.position);
  }
  
  private reorderPragmas(): void {
    // Topological sort based on dependencies
    const sorted = this.topologicalSort();
    sorted.forEach((pragma, index) => {
      pragma.position = index;
    });
  }
}
```

## Execution Models

### Sequential Execution
```typescript
class SequentialChainExecutor {
  async execute(chain: ChainPragma, input: any): Promise<any> {
    const pragmas = chain.getExecutionOrder();
    let currentInput = input;
    const results: any[] = [];
    
    for (const chainedPragma of pragmas) {
      try {
        // Check condition
        if (chainedPragma.condition && !chainedPragma.condition(currentInput)) {
          continue;
        }
        
        // Execute pragma
        const result = await this.executePragma(chainedPragma, currentInput);
        results.push(result);
        
        // Transform output for next pragma
        currentInput = chainedPragma.transformation(result);
        
      } catch (error) {
        if (chainedPragma.skipOnError) {
          continue;
        }
        
        if (chainedPragma.fallback) {
          const fallbackResult = await this.executePragma(chainedPragma.fallback, currentInput);
          currentInput = chainedPragma.transformation(fallbackResult);
        } else {
          throw error;
        }
      }
    }
    
    return this.combineResults(results, chain.outputType);
  }
  
  private async executePragma(
    chainedPragma: ChainedPragma, 
    input: any
  ): Promise<any> {
    const { pragma, timeout, retries } = chainedPragma;
    
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        return await Promise.race([
          pragma.execute(input),
          this.createTimeout(timeout)
        ]);
      } catch (error) {
        if (attempt === retries) throw error;
        await this.delay(Math.pow(2, attempt) * 1000); // Exponential backoff
      }
    }
  }
}
```

### Pipeline Execution
```typescript
class PipelineChainExecutor {
  async execute(chain: ChainPragma, input: any): Promise<any> {
    const pragmas = chain.getExecutionOrder();
    
    // Create pipeline stream
    const pipeline = this.createPipeline(pragmas);
    
    // Execute pipeline
    return new Promise((resolve, reject) => {
      let result: any;
      
      pipeline
        .on('data', (data) => {
          result = data;
        })
        .on('end', () => {
          resolve(result);
        })
        .on('error', (error) => {
          reject(error);
        });
      
      // Start pipeline
      pipeline.write(input);
      pipeline.end();
    });
  }
  
  private createPipeline(pragmas: ChainedPragma[]): Transform {
    return pragmas.reduce((pipeline, chainedPragma) => {
      const transform = new Transform({
        objectMode: true,
        transform: async (chunk, encoding, callback) => {
          try {
            const result = await chainedPragma.pragma.execute(chunk);
            const transformed = chainedPragma.transformation(result);
            callback(null, transformed);
          } catch (error) {
            callback(error);
          }
        }
      });
      
      return pipeline ? pipeline.pipe(transform) : transform;
    }, null);
  }
}
```

## Integration with Type System

### Chain Type Inference
```typescript
class ChainTypeInference {
  inferChainType(
    pragmas: ChainedPragma[], 
    outputType: Type
  ): ChainTypeDefinition {
    // Analyze input/output types of each pragma
    const typeFlow = this.analyzeTypeFlow(pragmas);
    
    // Validate type compatibility
    const compatibility = this.validateTypeCompatibility(typeFlow);
    if (!compatibility.isValid) {
      throw new Error(`Type incompatibility: ${compatibility.errors.join(', ')}`);
    }
    
    // Infer final chain type
    return {
      inputType: typeFlow[0].inputType,
      outputType,
      intermediateTypes: typeFlow.map(p => p.outputType),
      transformations: this.inferTransformations(typeFlow),
      constraints: this.extractConstraints(typeFlow)
    };
  }
  
  private analyzeTypeFlow(pragmas: ChainedPragma[]): TypeFlowAnalysis[] {
    return pragmas.map((pragma, index) => ({
      pragma: pragma.pragma.name,
      position: index,
      inputType: pragma.inputType,
      outputType: pragma.outputType,
      transformation: pragma.transformation,
      dependencies: pragma.dependencies
    }));
  }
}
```

## Usage Examples

### Basic Chain Composition
```typescript
// Create a data processing chain
const dataProcessingChain = chain.compose([
  parse.pragma,      // Parse input data
  validate.pragma,   // Validate parsed data
  transform.pragma,  // Transform data structure
  persist.pragma     // Persist to storage
], t.obj_of_types);

// Execute chain
const result = await dataProcessingChain.execute(inputData);
```

### Linguistic Chain Composition
```typescript
// Using linguistic interface
const processingPipeline = chain_of_(obj_of_types)
  .then(validate_with_(validation_rules))
  .then(transform_to_(target_schema))
  .then(persist_in_(database));

// Execute through linguistic interface
const result = await processingPipeline.execute(inputData);
```

### Conditional Chain
```typescript
// Chain with conditional execution
const conditionalChain = chain.compose([
  {
    pragma: validate.pragma,
    condition: (input) => input.requiresValidation
  },
  {
    pragma: transform.pragma,
    condition: (input) => input.requiresTransformation,
    fallback: passthrough.pragma
  },
  persist.pragma
], t.obj_of_types);
```

This chain pragma provides the **implementation foundation** for **sequential composition patterns** while **integrating seamlessly** with the **linguistic system** to enable **natural language composition** of pragma operations.
