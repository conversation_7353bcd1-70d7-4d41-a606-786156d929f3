# Meta Pragma

## Description

Provides metadata and IDE integration capabilities for all pragmas in the SpiceTime ecosystem. Enables rich hover information, visualization data, and development tooling integration for enhanced developer experience in WebStorm and other IDEs.

## IDE Integration Architecture

### Metadata Structure
```typescript
interface PragmaMetadata {
  // Core identification
  pragma: string;
  version: string;
  type: PragmaType;
  
  // IDE display information
  display: DisplayMetadata;
  
  // Hover information
  hover: HoverMetadata;
  
  // Visualization data
  visualization: VisualizationMetadata;
  
  // Documentation links
  documentation: DocumentationMetadata;
  
  // Development tooling
  tooling: ToolingMetadata;
  
  // Usage examples
  examples: ExampleMetadata[];
}
```

### Display Metadata
```typescript
interface DisplayMetadata {
  // Basic display
  name: string;
  description: string;
  category: string;
  tags: string[];
  
  // Icon and styling
  icon: IconDefinition;
  color: ColorScheme;
  badge: BadgeDefinition;
  
  // Project tree display
  treeDisplay: TreeDisplayOptions;
  
  // Status indicators
  status: StatusIndicator;
}

interface IconDefinition {
  type: 'builtin' | 'custom' | 'svg' | 'unicode';
  value: string;
  fallback?: string;
}

interface TreeDisplayOptions {
  // How to display in project tree
  showInTree: boolean;
  treeIcon: IconDefinition;
  treeLabel: string;
  treeBadge?: BadgeDefinition;
  
  // Grouping and sorting
  groupBy?: 'type' | 'category' | 'status';
  sortOrder?: number;
  
  // Expansion behavior
  defaultExpanded?: boolean;
  showChildren?: boolean;
}
```

### Hover Metadata
```typescript
interface HoverMetadata {
  // Quick info
  quickInfo: QuickInfoDefinition;
  
  // Detailed information
  details: DetailedInfoDefinition;
  
  // API information
  api: APIInfoDefinition;
  
  // Usage information
  usage: UsageInfoDefinition;
  
  // Related items
  related: RelatedItemDefinition[];
}

interface QuickInfoDefinition {
  // One-line summary
  summary: string;
  
  // Type information
  type: string;
  signature?: string;
  
  // Status and health
  status: 'active' | 'deprecated' | 'experimental' | 'stable';
  health: 'healthy' | 'warning' | 'error' | 'unknown';
  
  // Quick stats
  stats?: {
    children?: number;
    dependencies?: number;
    usages?: number;
    lastModified?: Date;
  };
}

interface DetailedInfoDefinition {
  // Full description
  description: string;
  
  // Purpose and functionality
  purpose: string;
  functionality: string[];
  
  // Architecture information
  architecture: {
    pattern: string;
    components: string[];
    dependencies: string[];
  };
  
  // Configuration
  configuration: ConfigurationOption[];
  
  // Constraints and limitations
  constraints: string[];
  limitations: string[];
}

interface APIInfoDefinition {
  // Endpoints and operations
  endpoints: EndpointInfo[];
  operations: OperationInfo[];
  
  // Data structures
  inputTypes: TypeInfo[];
  outputTypes: TypeInfo[];
  
  // Events
  events: EventInfo[];
  
  // Integration points
  integrations: IntegrationInfo[];
}
```

### Visualization Metadata
```typescript
interface VisualizationMetadata {
  // Diagram types supported
  diagrams: DiagramDefinition[];
  
  // Graph visualization
  graph: GraphVisualizationDefinition;
  
  // Tree visualization
  tree: TreeVisualizationDefinition;
  
  // Flow visualization
  flow: FlowVisualizationDefinition;
  
  // Custom visualizations
  custom: CustomVisualizationDefinition[];
}

interface DiagramDefinition {
  type: 'architecture' | 'sequence' | 'class' | 'component' | 'flow' | 'state';
  title: string;
  description: string;
  
  // Diagram data
  nodes: DiagramNode[];
  edges: DiagramEdge[];
  
  // Layout options
  layout: LayoutOptions;
  
  // Styling
  style: DiagramStyle;
}

interface GraphVisualizationDefinition {
  // Node representation
  nodeType: 'circle' | 'rectangle' | 'diamond' | 'custom';
  nodeSize: 'small' | 'medium' | 'large' | 'auto';
  nodeColor: ColorDefinition;
  nodeLabel: LabelDefinition;
  
  // Edge representation
  edgeType: 'line' | 'arrow' | 'dashed' | 'curved';
  edgeColor: ColorDefinition;
  edgeLabel: LabelDefinition;
  
  // Layout algorithm
  layout: 'force' | 'hierarchical' | 'circular' | 'grid';
  
  // Interaction
  interactive: boolean;
  zoomable: boolean;
  selectable: boolean;
}
```

### Documentation Metadata
```typescript
interface DocumentationMetadata {
  // Documentation links
  links: DocumentationLink[];
  
  // Inline documentation
  inline: InlineDocumentation;
  
  // Examples and tutorials
  examples: ExampleLink[];
  tutorials: TutorialLink[];
  
  // API documentation
  apiDocs: APIDocumentationLink[];
  
  // Video and media
  media: MediaLink[];
}

interface DocumentationLink {
  type: 'specification' | 'guide' | 'reference' | 'tutorial' | 'example';
  title: string;
  url: string;
  description: string;
  tags: string[];
}

interface InlineDocumentation {
  // Markdown content
  content: string;
  
  // Code examples
  codeExamples: CodeExample[];
  
  // Interactive examples
  interactiveExamples: InteractiveExample[];
}
```

### Tooling Metadata
```typescript
interface ToolingMetadata {
  // IDE features
  ide: IDEFeatureDefinition[];
  
  // Code generation
  codeGeneration: CodeGenerationDefinition[];
  
  // Validation and linting
  validation: ValidationDefinition[];
  
  // Debugging support
  debugging: DebuggingDefinition;
  
  // Testing support
  testing: TestingDefinition;
  
  // Refactoring support
  refactoring: RefactoringDefinition[];
}

interface IDEFeatureDefinition {
  feature: 'autocomplete' | 'syntax-highlighting' | 'error-checking' | 'navigation' | 'refactoring';
  enabled: boolean;
  configuration: Record<string, any>;
  
  // Feature-specific data
  autocomplete?: AutocompleteDefinition;
  syntaxHighlighting?: SyntaxHighlightingDefinition;
  errorChecking?: ErrorCheckingDefinition;
}

interface AutocompleteDefinition {
  // Completion items
  items: CompletionItem[];
  
  // Trigger characters
  triggerCharacters: string[];
  
  // Context-aware completions
  contextual: boolean;
  
  // Snippet support
  snippets: SnippetDefinition[];
}
```

## WebStorm Integration

### Project Tree Enhancement
```typescript
interface WebStormProjectTreeIntegration {
  // Custom node types
  nodeTypes: ProjectTreeNodeType[];
  
  // Node decorators
  decorators: NodeDecorator[];
  
  // Context menu items
  contextMenuItems: ContextMenuItem[];
  
  // Drag and drop support
  dragAndDrop: DragAndDropDefinition;
}

interface ProjectTreeNodeType {
  // Node identification
  type: string;
  pattern: string; // File pattern to match
  
  // Display properties
  icon: IconDefinition;
  label: LabelTemplate;
  badge?: BadgeDefinition;
  
  // Behavior
  expandable: boolean;
  selectable: boolean;
  editable: boolean;
  
  // Children
  childrenProvider?: ChildrenProviderDefinition;
}

interface NodeDecorator {
  // When to apply decorator
  condition: DecoratorCondition;
  
  // Visual decoration
  decoration: {
    icon?: IconDefinition;
    badge?: BadgeDefinition;
    color?: ColorDefinition;
    style?: StyleDefinition;
  };
  
  // Tooltip enhancement
  tooltip?: TooltipDefinition;
}
```

### Hover Information Provider
```typescript
class PragmaHoverProvider {
  async provideHover(
    document: TextDocument,
    position: Position
  ): Promise<HoverInfo | null> {
    // Identify pragma at position
    const pragma = await this.identifyPragmaAtPosition(document, position);
    if (!pragma) return null;
    
    // Get metadata for pragma
    const metadata = await this.getPragmaMetadata(pragma);
    if (!metadata) return null;
    
    // Build hover content
    const hoverContent = this.buildHoverContent(metadata);
    
    return {
      contents: hoverContent,
      range: pragma.range
    };
  }
  
  private buildHoverContent(metadata: PragmaMetadata): MarkdownString {
    const content = new MarkdownString();
    
    // Quick info section
    content.appendMarkdown(`## ${metadata.display.name}\n`);
    content.appendMarkdown(`${metadata.hover.quickInfo.summary}\n\n`);
    
    // Type and signature
    if (metadata.hover.quickInfo.signature) {
      content.appendCodeblock(metadata.hover.quickInfo.signature, 'typescript');
    }
    
    // Status and health
    content.appendMarkdown(`**Status:** ${metadata.hover.quickInfo.status}\n`);
    content.appendMarkdown(`**Health:** ${metadata.hover.quickInfo.health}\n\n`);
    
    // Quick stats
    if (metadata.hover.quickInfo.stats) {
      const stats = metadata.hover.quickInfo.stats;
      content.appendMarkdown('**Stats:**\n');
      if (stats.children) content.appendMarkdown(`- Children: ${stats.children}\n`);
      if (stats.dependencies) content.appendMarkdown(`- Dependencies: ${stats.dependencies}\n`);
      if (stats.usages) content.appendMarkdown(`- Usages: ${stats.usages}\n`);
    }
    
    // Documentation links
    if (metadata.documentation.links.length > 0) {
      content.appendMarkdown('\n**Documentation:**\n');
      metadata.documentation.links.forEach(link => {
        content.appendMarkdown(`- [${link.title}](${link.url})\n`);
      });
    }
    
    return content;
  }
}
```

### Visualization Integration
```typescript
class PragmaVisualizationProvider {
  async provideVisualization(
    pragma: string,
    type: 'architecture' | 'flow' | 'dependencies' | 'usage'
  ): Promise<VisualizationData | null> {
    const metadata = await this.getPragmaMetadata(pragma);
    if (!metadata?.visualization) return null;
    
    switch (type) {
      case 'architecture':
        return this.generateArchitectureVisualization(metadata);
      case 'flow':
        return this.generateFlowVisualization(metadata);
      case 'dependencies':
        return this.generateDependencyVisualization(metadata);
      case 'usage':
        return this.generateUsageVisualization(metadata);
      default:
        return null;
    }
  }
  
  private generateArchitectureVisualization(
    metadata: PragmaMetadata
  ): VisualizationData {
    const archDiagram = metadata.visualization.diagrams.find(
      d => d.type === 'architecture'
    );
    
    if (!archDiagram) return null;
    
    return {
      type: 'graph',
      data: {
        nodes: archDiagram.nodes.map(node => ({
          id: node.id,
          label: node.label,
          type: node.type,
          metadata: node.metadata
        })),
        edges: archDiagram.edges.map(edge => ({
          source: edge.source,
          target: edge.target,
          label: edge.label,
          type: edge.type
        }))
      },
      layout: archDiagram.layout,
      style: archDiagram.style
    };
  }
}
```

## Usage Examples

### Pragma Metadata Definition
```typescript
// Example metadata for spec pragma
const specPragmaMetadata: PragmaMetadata = {
  pragma: 'spec',
  version: '1.0.0',
  type: 'transformation',
  
  display: {
    name: 'Spec Pragma',
    description: 'Transforms specifications into implementation code',
    category: 'Core',
    tags: ['specification', 'transformation', 'code-generation'],
    icon: { type: 'builtin', value: 'file-code' },
    color: { primary: '#007ACC', secondary: '#005A9E' },
    treeDisplay: {
      showInTree: true,
      treeIcon: { type: 'builtin', value: 'gear' },
      treeLabel: 'Spec Transformer',
      defaultExpanded: true
    }
  },
  
  hover: {
    quickInfo: {
      summary: 'Transforms industry-standard specifications into TypeScript implementation code',
      type: 'TransformationPragma',
      signature: 'spec.execute(input: SpecificationInput): Promise<GeneratedCode>',
      status: 'stable',
      health: 'healthy',
      stats: {
        children: 6,
        dependencies: 4,
        usages: 12,
        lastModified: new Date('2024-01-15')
      }
    },
    details: {
      description: 'The spec pragma orchestrates the transformation of industry-standard specifications (OpenAPI, JSON Schema, Gherkin) into TypeScript implementation code, tests, and documentation.',
      purpose: 'Bridge the gap between formal specifications and working code',
      functionality: [
        'Parse specifications in multiple formats',
        'Generate TypeScript components and types',
        'Create test suites and documentation',
        'Maintain bidirectional synchronization'
      ],
      architecture: {
        pattern: 'Pipeline with round-robin coordination',
        components: ['parse', 'generate', 'sync', 'events', 'middleware'],
        dependencies: ['kernel', 'type-system', 'template-engine']
      }
    }
  },
  
  visualization: {
    diagrams: [
      {
        type: 'architecture',
        title: 'Spec Pragma Architecture',
        description: 'High-level architecture showing component relationships',
        nodes: [
          { id: 'parse', label: 'Parse', type: 'component' },
          { id: 'generate', label: 'Generate', type: 'component' },
          { id: 'sync', label: 'Sync', type: 'component' }
        ],
        edges: [
          { source: 'parse', target: 'generate', type: 'data-flow' },
          { source: 'generate', target: 'sync', type: 'data-flow' }
        ]
      }
    ]
  },
  
  documentation: {
    links: [
      {
        type: 'specification',
        title: 'Spec Pragma Specification',
        url: '/docs/pragmas/spec',
        description: 'Complete specification and API reference'
      }
    ]
  }
};
```

This meta pragma provides **comprehensive IDE integration** capabilities, enabling **rich developer experience** with **hover information**, **visualizations**, and **tooling support** for all pragmas in the SpiceTime ecosystem.
