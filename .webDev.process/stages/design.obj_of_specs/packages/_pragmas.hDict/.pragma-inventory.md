# Pragma Inventory - Missing Specs to Create

## Current Status
✅ = Completed
🔄 = In Progress  
📋 = Planned

## Foundation Pragmas

### Core System
- ✅ **st** - Foundation pragma system
- ✅ **spec** - Specification transformation pragma
- 📋 **base** - Base spec type with categorical transforms
- 📋 **meta** - Metadata pragma for IDE integration

### Type System
- 📋 **t** - Type system foundation
- 📋 **factory** - Factory pattern for type creation
- 📋 **chain** - Sequential composition pragma
- 📋 **of** - Linguistic construct for relationships

## Core Pragmas (_core.pragmas)

### Data Structures
- 📋 **obj** - Object pragma
- 📋 **dict** - Dictionary pragma  
- 📋 **hDict** - Homogeneous dictionary pragma
- 📋 **list** - List pragma
- 📋 **seq** - Sequence pragma
- 📋 **arr** - Array pragma

### Spatial & Organizational
- 📋 **vSpace** - Virtual space pragma
- 📋 **context** - Context management pragma
- 📋 **scope** - Scope management pragma

### Behavioral
- 📋 **prop** - Property pragma
- 📋 **ttree** - Tree structure pragma
- 📋 **event** - Event handling pragma

## Linguistic System

### Core Linguistics
- 📋 **l** - Linguistics foundation pragma
- 📋 **vocabulary** - Vocabulary management
- 📋 **grammar** - Grammar rules
- 📋 **mapping** - Pragma-vocabulary mapping

### Linguistic Constructs
- 📋 **of** - Relationship construct
- 📋 **with** - Association construct  
- 📋 **by** - Method/means construct
- 📋 **to** - Direction/target construct

## Service Pragmas

### Core Services
- 📋 **kernel** - Kernel coordination service
- 📋 **events** - Event system service
- 📋 **middleware** - Middleware plugin service
- 📋 **schemas** - Schema registry service

### Specialized Services
- 📋 **validation** - Validation service
- 📋 **transformation** - Data transformation service
- 📋 **persistence** - Data persistence service
- 📋 **communication** - Inter-service communication

## React Integration Pragmas

### Component System
- 📋 **component** - React component pragma
- 📋 **hook** - React hook pragma
- 📋 **provider** - React provider pragma
- 📋 **hoc** - Higher-order component pragma

### State Management
- 📋 **state** - State management pragma
- 📋 **reducer** - Reducer pragma
- 📋 **selector** - Selector pragma
- 📋 **effect** - Side effect pragma

## Monadic Operators

### Compositional
- 📋 **chain_of** - Sequential composition operator
- 📋 **parallel_of** - Parallel composition operator
- 📋 **branch_of** - Conditional branching operator
- 📋 **merge_of** - Result merging operator

### Transformational
- 📋 **map_over** - Transform operator
- 📋 **filter_by** - Selection operator
- 📋 **reduce_to** - Aggregation operator
- 📋 **group_by** - Organization operator

### Temporal
- 📋 **delay_by** - Temporal delay operator
- 📋 **throttle_by** - Rate limiting operator
- 📋 **batch_by** - Batching operator
- 📋 **stream_of** - Streaming operator

## Spec Types

### Base Types
- 📋 **base.spec** - Foundation specification type
- 📋 **pragma.spec** - Pragma specification type
- 📋 **service.spec** - Service specification type
- 📋 **component.spec** - Component specification type

### Specialized Types
- 📋 **api.spec** - API specification type
- 📋 **event.spec** - Event specification type
- 📋 **workflow.spec** - Workflow specification type
- 📋 **integration.spec** - Integration specification type

## Priority Order for Creation

### Phase 1: Foundation (Critical Path)
1. **base.spec** - Foundation for all other specs
2. **meta** - IDE integration metadata
3. **t** - Type system foundation
4. **factory** - Factory pattern implementation

### Phase 2: Core Pragmas (High Priority)
5. **obj** - Object pragma (referenced everywhere)
6. **dict** - Dictionary pragma
7. **hDict** - Homogeneous dictionary pragma
8. **chain** - Sequential composition pragma

### Phase 3: Linguistic System (Medium Priority)
9. **l** - Linguistics foundation
10. **of** - Relationship construct
11. **vocabulary** - Vocabulary management
12. **mapping** - Pragma-vocabulary mapping

### Phase 4: Services (Medium Priority)
13. **kernel** - Kernel coordination
14. **events** - Event system
15. **middleware** - Middleware system
16. **schemas** - Schema registry

### Phase 5: React Integration (Lower Priority)
17. **component** - React component pragma
18. **hook** - React hook pragma
19. **provider** - React provider pragma
20. **state** - State management pragma

### Phase 6: Advanced Features (Future)
21. Monadic operators
22. Specialized services
23. Advanced spec types
24. Domain-specific pragmas

## Dependencies Map

```
base.spec → (foundation for all specs)
├── pragma.spec → (extends base.spec)
├── service.spec → (extends base.spec)
└── component.spec → (extends base.spec)

t → (type system foundation)
├── factory → (depends on t)
├── obj → (depends on t)
└── chain → (depends on t, obj)

l → (linguistics foundation)
├── of → (depends on l)
├── vocabulary → (depends on l)
└── mapping → (depends on l, vocabulary)

obj → (referenced by most other pragmas)
├── dict → (depends on obj)
├── hDict → (depends on dict)
└── context → (depends on obj)
```

This inventory provides a clear roadmap for systematically creating all missing pragma specifications.
