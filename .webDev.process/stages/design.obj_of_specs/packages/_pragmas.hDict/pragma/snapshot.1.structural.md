# Pragma Behavioral Snapshot 1: Structural Analysis

## First Pass: Core Structural Patterns

### 1. Dual Private/Public Composition Architecture
**Core Pattern**: Every node has two entangled functions:
- **Private pragma** - Determined by filename extensions, resolved through private linguistic system
- **Public pragma** - Extends private pragma with local functionality
- **Composition pipeline** - Private pragma result feeds into public pragma
- **Final API** - Public pragma result becomes the node's exposed API

### 2. Naming Convention System
**Prefix Semantics**:
- `_` prefix = Private scope (current directory only, does not travel downscope)
- `.` prefix = Protected scope (current + direct children, isolated version spaces)
- No prefix = Public scope (globally accessible)
- `$` prefix = Dynamic scope (computed at runtime)
- `@` prefix = Injected scope (provided by external system)

**Extension Resolution**:
```
find.by_status          → FilterOperation pragma
create.with_defaults    → CreateOperation pragma  
update.by_id           → UpdateOperation pragma
sort.by_name.asc       → SortOperation pragma (chained)
```

### 3. Node Processing Strategy
**Folder Nodes**:
- Wraps the index file of child folders
- Constructs pragmatic index file that becomes folder's API
- Injects appropriate scopes into index file
- Extends parent scopes with local scope from folder

**Leaf Nodes**:
- Wraps pragmatic leaf nodes directly
- Applies filename extension resolution to determine private pragma
- Generates pragmaOperator function for the leaf
- Injects getter functions that provide scoped access

### 4. Scope Construction Hierarchy
```typescript
// Parent scope injection pattern
parentScope = {
  public: { /* globally accessible */ },
  protected: { /* .prefixed items */ },
  private: { /* _prefixed items */ },
  deps: { /* dependency namespace */ },
  context: { /* context namespaces that become tTree instances */ }
}

// Local scope extension
localScope = {
  ...parentScope,
  local: { /* current node's scope */ },
  children: { /* child node APIs */ }
}
```

### 5. Context as Namespace Creation
**Key Insight**: Context creation becomes namespace in scopes - **this is what becomes tTree instances**
- Context namespaces are created in scope.context
- Each context namespace becomes a tTree instance
- Multiple tTree instances form a forest
- Forest management through forestry.types system

### 6. Dependency Management Integration
**Package.json Integration**:
- Dependencies managed through `pragma.deps` files
- Eliminates traditional import statements
- Uses `deps` namespace for all external access
- Package realm system (local, npm, org, custom)

### 7. Child Pragma Extension Mechanism
**Extension Patterns**:
```
.obj.pragma          # Local definition (does not travel downscope)
pragma.obj           # Composes obj as defined by obj.pragma upscope or sibling  
pragma.funny.obj     # Funny pragma (noop if doesn't exist, descriptive)
.pragma.funny.obj    # Extends node private pragma, not accessible downscope
```

### 8. Type System Integration
**pragma.type Updates**:
- Each pragma defines type contribution through `pragma.type.ts`
- Automatic type namespace update to global `t` object
- Hierarchical type composition
- Unified type files with static and runtime types

### 9. Linguistic System Integration
**Private vs Public Linguistic Systems**:
- Private linguistic system resolves filename extensions to pragmas
- Public linguistic system handles pragma composition
- WebDev process maintains both private and public `l` systems
- `.pragma.l` scripts extend linguistic capabilities

### 10. Reactive Behavior System
**TreenityTree Integration**:
- Pragmas form reactive trees that respond to structure changes
- AutoExec pattern for development environment
- Automatic API regeneration on structure modifications
- Event propagation through pragma tree

## Key Structural Insights

1. **Context → tTree**: Context namespaces in scopes become tTree instances
2. **Forest Management**: Multiple tTree instances managed through forestry.types
3. **Linguistic Entanglement**: Private and public `l` systems maintained by webdev process
4. **Extension Analysis**: Pragma reads its name and locates extending pragmas
5. **Scope Isolation**: Private scope with private `l` vs public scope with public `l`

This first pass reveals the **structural foundation** of the pragma system - how components are organized, scoped, and interconnected.
