# Complete Pragma Behavioral Snapshot
*Distilled from all concepts in chronological order*

## Core Behavioral Architecture

### Dual Private/Public Composition Pattern
The pragma system implements a **fundamental dual composition** where every node has:

1. **Private Pragma** - Determined by filename extensions, resolved through private linguistic system
2. **Public Pragma** - Extends private pragma with local functionality  
3. **Composition Pipeline** - Private pragma result feeds into public pragma
4. **Final API** - Public pragma result becomes the node's API

```typescript
// Core composition pattern
function pragmaOperator(scope) {
  // 1. Resolve private pragma from filename extensions
  const privatePragma = resolvePrivatePragma(filename.extensions);
  
  // 2. Execute private pragma with injected scope
  const privateResult = privatePragma(scope);
  
  // 3. Apply public pragma extension
  const publicPragma = getPublicPragma(currentNode);
  const publicResult = publicPragma(privateResult);
  
  // 4. Update type system
  updateTypeNamespace(publicResult.type);
  
  // 5. Return composed API
  return publicResult;
}
```

## Naming Convention System

### Prefix Semantics & Scope Control
- `_` prefix = **Private scope** (current directory only, does not travel downscope)
- `.` prefix = **Protected scope** (current + direct children, cannot be extended past local node)
- No prefix = **Public scope** (globally accessible)
- `$` prefix = **Dynamic scope** (computed at runtime)
- `@` prefix = **Injected scope** (provided by external system)

### Extension Resolution Patterns
```typescript
find.by_status          → FilterOperation pragma
create.with_defaults    → CreateOperation pragma  
update.by_id           → UpdateOperation pragma
sort.by_name.asc       → SortOperation pragma (chained)
filter.where{active}   → FilterOperation pragma (with condition)
```

### Pragma Definition Hierarchy
```
.obj.pragma          # Local definition (does not travel downscope)
pragma.obj           # Composes obj as defined by obj.pragma upscope or sibling  
pragma.funny.obj     # Funny pragma (noop if doesn't exist, descriptive)
.pragma.funny.obj    # Extends node private pragma, not accessible downscope
_.pragma             # Entry point into private pragma composition
pragma               # Starting point for public pragma composition
_.pragma             # Protected pragma (isolated version spaces)
```

## Node Wrapping Behaviors

### Folder Node Processing
When pragma encounters a **folder node**:
1. **Wraps the index file** of the child folder
2. **Constructs pragmatic index file** that becomes the folder's API
3. **Injects appropriate scopes** into the index file
4. **Extends parent scopes** with local scope from the folder
5. **Generates getter functions** that provide scoped access to children

```typescript
// Generated index.ts for each folder
export default function(injectedScope) {
  // Apply pragma operator to construct API
  const api = pragmaOperator(injectedScope);
  
  // Extend with child APIs
  const childAPIs = constructChildAPIs(children, extendedScope);
  
  // Compose final API
  return {
    ...api,
    ...childAPIs
  };
}
```

### Leaf Node Processing
When pragma encounters a **leaf node** (file):
1. **Wraps pragmatic leaf nodes** directly
2. **Applies filename extension resolution** to determine private pragma
3. **Generates pragmaOperator function** for the leaf
4. **Injects getter functions** that provide scoped access
5. **Updates type namespace** with pragma's type contribution

## Scope Construction & Injection

### Hierarchical Scope System
```typescript
// Parent scope injection pattern
parentScope = {
  public: { /* globally accessible */ },
  protected: { /* .prefixed items */ },
  private: { /* _prefixed items */ },
  deps: { /* dependency namespace */ }
}

// Local scope extension
localScope = {
  ...parentScope,
  local: { /* current node's scope */ },
  children: { /* child node APIs */ }
}

// Injected into child nodes
childScope = {
  ...localScope,
  inherited: { /* from parent chain */ },
  context: { /* execution context */ }
}
```

### Scope Accessibility Rules
1. **Private scope** (`_` prefix) - Only accessible within current node
2. **Protected scope** (`.` prefix) - Accessible to current node and direct children
3. **Public scope** (no prefix) - Accessible globally
4. **Local scope** - Current node's definitions, merged with inherited scope
5. **Dependency scope** - Accessed through `deps` namespace

## Dependency Management System

### Package.json Integration
Pragma integrates with package.json through **pragma.deps** files:

```typescript
// pragma.deps (equivalent to package.json dependencies)
export default {
  // Local realm dependencies
  local: {
    'utils': '^1.0.0',
    'components': '^2.1.0'
  },
  
  // NPM realm dependencies  
  npm: {
    'lodash': '^4.17.21',
    'react': '^18.2.0'
  },
  
  // Organization realm dependencies
  org: {
    'design-system': '^2.0.0'
  }
};
```

### Dependency Namespace Elimination
**Eliminates traditional import statements** in favor of `deps` namespace:

```typescript
// Traditional (NOT used)
import { someFunction } from 'myPackage';

// SpiceTime approach
deps.myPackage.someFunction();
```

### Package Realm System
- **Local Realm**: Packages within current monorepo
- **NPM Realm**: Packages from NPM registry  
- **Org Realm**: Organization-specific packages
- **Custom Realms**: User-defined package sources

## Child Pragma Extension System

### Child Pragma Creation
Pragma creates **child pragma extensions** in each node through:

```typescript
// Child pragma extension mechanism
function extendChildPragma(basePragma, extension) {
  return function(scope) {
    const baseResult = basePragma(scope);
    const extensionResult = extension(baseResult, scope);
    
    return {
      ...baseResult,
      ...extensionResult,
      extended: true,
      basePragma: basePragma.name,
      extension: extension.name
    };
  };
}
```

### Child Pragma Patterns
```typescript
// Sequential composition
1.pragma.first.validation    # First in sequence
2.pragma.second.transform    # Second in sequence
3.pragma.third.output        # Third in sequence

// Protected pragma
_.pragma                     # Creates isolated version spaces
```

## Type System Integration

### pragma.type Updates
Each pragma can define its type contribution through `pragma.type.ts`:

```typescript
// pragma.type.ts
export interface MyPragmaType {
  // Type definition that gets merged into t namespace
}

// Automatic type namespace update
function updateTypeNamespace(pragmaType) {
  // Extends global t object with pragma's type
  t[pragmaName] = pragmaType;
  
  // Updates parent type definitions
  if (parentType) {
    parentType.children[pragmaName] = pragmaType;
  }
}
```

### Hierarchical Type Composition
```typescript
// Types compose hierarchically
t.user = {
  query: {
    find: FindOperationType,
    filter: FilterOperationType
  },
  mutation: {
    create: CreateOperationType,
    update: UpdateOperationType
  }
}
```

### Unified Type Files
Types are defined in `.type.ts` files containing both static TypeScript types and runtime types:

```typescript
// In _.pragma.type.ts
// Static type definitions (t namespace)
export interface User {
  id: string;
  name: string;
  email: string;
}

// Runtime type validation
export const UserType = {
  validate: (obj: any): obj is User => { /* validation logic */ },
  create: (data: Partial<User>): User => { /* creation logic */ }
};
```

## Linguistic System Integration

### Private Linguistic Resolution
```typescript
// Resolves filename extensions to pragmas
function resolvePrivatePragma(extensions) {
  const pragmaTerms = privateLinguisticSystem.parse(extensions);
  const pragma = pragmaRegistry.resolve(pragmaTerms);
  
  return pragma || noopPragma;
}
```

### Public Linguistic Access
```typescript
// Access through linguistic system
const userPragma = l`pragma of users`;
const queryPragma = l`pragma of users.query`;
const filterPragma = l`pragma of users.query.find`;

// Composition through l terms
const composedPragma = l`pragma of ${myNode}`;
```

### Linguistic Registration
```typescript
// pragma.linguistic - Pragma accessible through linguistic system
export default function linguisticPragma(privatePragmaResult) {
  return {
    ...privatePragmaResult,
    
    // Register with linguistic system
    registerWithL: () => {
      l.register('pragma', 'myNode', this);
    },
    
    // Linguistic composition
    composeWith: (otherNodeName) => {
      const otherPragma = l`pragma of ${otherNodeName}`;
      return compose(this, otherPragma);
    }
  };
}
```

## Reactive Behavior System

### TreenityTree Integration
Pragmas form reactive trees that automatically respond to structure changes:

```typescript
class PragmaTree extends TreenityTree {
  // Automatic reaction to structure modifications
  onStructureChange(change) {
    // Recompute affected pragma APIs
    this.recomputeAffectedNodes(change.path);
    
    // Update type namespace
    this.updateTypeNamespace();
    
    // Emit change events
    this.emit('pragmaChange', change);
  }
}
```

### AutoExec Pattern
WebDev runs as autoexec function that reacts to any modifications:

```typescript
function webDevAutoExec() {
  pragmaTree.watch((change) => {
    // Automatically regenerate affected APIs
    regenerateAPIs(change.affectedNodes);
    
    // Update development environment
    updateDevEnvironment(change);
  });
}
```

## Index File Construction

### Pragmatic Index Generation
Pragma constructs index files that wrap folder APIs:

```typescript
// Generated pragmatic index file
export default function pragmaticIndex(scope) {
  // 1. Apply pragma operator to current folder
  const folderAPI = pragmaOperator(scope);
  
  // 2. Collect child APIs with extended scope
  const childAPIs = {};
  for (const child of scope.children) {
    const childScope = extendScope(scope, child.localScope);
    childAPIs[child.name] = child.pragmaOperator(childScope);
  }
  
  // 3. Compose final API
  return {
    ...folderAPI,
    ...childAPIs,
    // Metadata
    __pragma: {
      type: folderAPI.type,
      children: Object.keys(childAPIs),
      scope: scope.public
    }
  };
}
```

### Index File Wrapping Strategy
- **Folder nodes**: Wraps the index file of child folders
- **Leaf nodes**: Wraps pragmatic leaf nodes directly
- **Scope injection**: Injects appropriate scopes into wrapped files
- **API composition**: Composes child APIs into parent API

## Error Handling & Fallback

### Graceful Degradation
```typescript
// Pragma execution with error handling
function executePragmaWithGracefulDegradation(pragma, scope) {
  try {
    return pragma(scope);
  } catch (error) {
    console.error(`Pragma execution failed: ${error.message}`);
    
    // Return scope with error metadata
    return {
      ...scope,
      error: error.message,
      degraded: true
    };
  }
}
```

### Fallback Patterns
```typescript
// If pragma not found, fallback to noop
function resolvePragmaWithFallback(pragmaName) {
  const pragma = pragmaRegistry.get(pragmaName);
  
  if (!pragma) {
    console.warn(`Pragma '${pragmaName}' not found, using noop`);
    return noopPragma;
  }
  
  return pragma;
}

// Noop pragma
function noopPragma(scope) {
  return scope; // Pass through unchanged
}
```

## Composition Strategies

### Sequential Composition
```typescript
function sequentialComposition(pragmas) {
  return function(scope) {
    return pragmas.reduce((result, pragma) => {
      return pragma(result);
    }, scope);
  };
}
```

### Parallel Composition
```typescript
function parallelComposition(pragmas) {
  return function(scope) {
    const results = pragmas.map(pragma => pragma(scope));
    return mergeResults(results);
  };
}
```

### Hierarchical Composition
```typescript
function hierarchicalComposition(parentPragma, childPragmas) {
  return function(scope) {
    const parentResult = parentPragma(scope);
    const childResults = childPragmas.map(child => 
      child(extendScope(scope, parentResult))
    );
    
    return {
      ...parentResult,
      children: childResults
    };
  };
}
```

## Package Integration Patterns

### Package Profile System
```typescript
// Package profile in index.ts
export const __package = {
  name: 'my-package',
  version: '1.0.0',
  pragmaExtensions: {
    // Pragma extensions this package provides
  },
  namepathExtensions: {
    // Namepath extensions
  },
  dependencies: {
    // Package dependencies
  }
};
```

### Content Addressing
```typescript
// Content-addressed package mapping
const pragmaticPackage = {
  semanticName: 'ui-components',
  contentHash: 'QmZ9myDsXfXzUu4XCX6ZAPQGxYcvdRLYAGqkf5qwuA9K4A',
  pragmaExtensions: {
    // Pragma extensions
  },
  namepathExtensions: {
    // Namepath extensions  
  },
  sourceCode: new Map([
    ['src/index.ts', '...'],
    ['src/Button.tsx', '...']
  ])
};
```

## Kernel Integration

### Task Scheduling
All pragma operations are scheduled through SpiceTime kernel:

```typescript
// Pragma execution through kernel
async function executePragmaViaKernel(pragma, scope) {
  const taskId = await kernel.scheduleTask({
    type: 'pragma-execution',
    priority: 'normal',
    payload: { pragma: pragma.name, scope }
  });
  
  return await kernel.waitForTask(taskId);
}
```

### Resource Management
```typescript
// Resource allocation for pragma execution
async function allocateResourcesForPragma(pragma) {
  const requirements = pragma.getResourceRequirements();
  const allocation = await kernel.requestResources(requirements);
  
  return allocation;
}
```

This behavioral snapshot captures the **complete pragma system** as designed through all concepts, showing how it orchestrates **composition**, **scope management**, **dependency injection**, **type system integration**, and **reactive behavior** to create the foundational infrastructure for the entire SpiceTime ecosystem.
