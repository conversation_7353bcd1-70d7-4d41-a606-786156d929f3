# Pragma Behavioral Specification

## Core Behavioral Pattern

The pragma system implements a **dual private/public composition pattern** where:

1. **Private pragma** is determined by filename extensions and resolved through private linguistic system
2. **Public pragma** extends the private pragma with local functionality
3. **Composition** feeds private pragma result into public pragma
4. **API** becomes the public API of the node

## Naming Convention Behaviors

### Prefix Semantics
- `_` prefix = **Private scope** (available only in current directory)
- `.` prefix = **Protected scope** (available in current and child directories) 
- No prefix = **Public scope** (available globally)
- `$` prefix = **Dynamic scope** (computed at runtime)
- `@` prefix = **Injected scope** (provided by external system)

### Extension Resolution
```
find.by_status          → FilterOperation pragma
create.with_defaults    → CreateOperation pragma  
update.by_id           → UpdateOperation pragma
sort.by_name.asc       → SortOperation pragma (chained)
filter.where{active}   → FilterOperation pragma (with condition)
```

## Node Wrapping Behaviors

### Folder Node Wrapping
When pragma encounters a **folder node**:
1. **Wraps the index file** of the child folder
2. **Injects appropriate scopes** into the index file
3. **Constructs pragmatic index file** that becomes the folder's API
4. **Extends parent scopes** with local scope from the folder

### Leaf Node Wrapping  
When pragma encounters a **leaf node** (file):
1. **Wraps pragmatic leaf nodes** directly
2. **Applies filename extension resolution** to determine private pragma
3. **Injects getter functions** that provide scoped access
4. **Generates pragmaOperator function** for the leaf

## Scope Construction and Injection

### Scope Hierarchy
```typescript
// Parent scope injection pattern
parentScope = {
  public: { /* globally accessible */ },
  protected: { /* .prefixed items */ },
  private: { /* _prefixed items */ }
}

// Local scope extension
localScope = {
  ...parentScope,
  local: { /* current node's scope */ }
}

// Injected into child nodes
childScope = {
  ...localScope,
  inherited: { /* from parent chain */ }
}
```

### Getter Function Injection
```typescript
// For folder nodes
function getFolderAPI(folderPath) {
  const indexFile = path.join(folderPath, 'index');
  const wrappedIndex = wrapIndexFile(indexFile);
  return injectScope(wrappedIndex, constructedScope);
}

// For leaf nodes  
function getLeafAPI(filePath) {
  const pragmaOperator = generatePragmaOperator(filePath);
  return injectScope(pragmaOperator, constructedScope);
}
```

## PragmaOperator Function Generation

### Core Pattern
```typescript
// Generated for each node
function pragmaOperator(scope) {
  // 1. Determine private pragma from filename extensions
  const privatePragma = resolvePrivatePragma(filename.extensions);
  
  // 2. Execute private pragma with injected scope
  const privateResult = privatePragma(scope);
  
  // 3. Apply public pragma extension
  const publicPragma = getPublicPragma(currentNode);
  const publicResult = publicPragma(privateResult);
  
  // 4. Update type system
  updateTypeNamespace(publicResult.type);
  
  // 5. Return composed API
  return publicResult;
}
```

### Index File Construction
```typescript
// Generated index.ts for each folder
export default function(injectedScope) {
  // Apply pragma operator to construct API
  const api = pragmaOperator(injectedScope);
  
  // Extend with child APIs
  const childAPIs = constructChildAPIs(children, extendedScope);
  
  // Compose final API
  return {
    ...api,
    ...childAPIs
  };
}
```

## Child Pragma Extension Patterns

### Definition Hierarchy
```
.obj.pragma          # Local definition of obj pragma (does not travel downscope)
pragma.obj           # Composes obj as defined by obj.pragma upscope or sibling  
pragma.funny.obj     # Funny pragma (noop if doesn't exist, but descriptive)
.pragma.funny.obj    # Extends node private pragma, not accessible downscope
```

### Extension Mechanism
```typescript
// Child pragma extension
function extendChildPragma(basePragma, extension) {
  return function(scope) {
    const baseResult = basePragma(scope);
    const extensionResult = extension(baseResult, scope);
    
    return {
      ...baseResult,
      ...extensionResult,
      extended: true,
      basePragma: basePragma.name,
      extension: extension.name
    };
  };
}
```

## Type API Extension

### pragma.type Integration
```typescript
// Each pragma can define its type contribution
// pragma.type.ts
export interface MyPragmaType {
  // Type definition that gets merged into t namespace
}

// Automatic type namespace update
function updateTypeNamespace(pragmaType) {
  // Extends global t object with pragma's type
  t[pragmaName] = pragmaType;
  
  // Updates parent type definitions
  if (parentType) {
    parentType.children[pragmaName] = pragmaType;
  }
}
```

### Type Composition Pattern
```typescript
// Types compose hierarchically
t.user = {
  query: {
    find: FindOperationType,
    filter: FilterOperationType
  },
  mutation: {
    create: CreateOperationType,
    update: UpdateOperationType
  }
}
```

## Scope Extension Behaviors

### Parent Scope Extension
```typescript
function extendParentScope(parentScope, localScope) {
  return {
    // Inherit parent scopes
    ...parentScope,
    
    // Add local scope
    local: localScope,
    
    // Merge protected items
    protected: {
      ...parentScope.protected,
      ...localScope.protected
    },
    
    // Private items don't propagate down
    private: localScope.private
  };
}
```

### Scope Accessibility Rules
1. **Private scope** (`_` prefix) - Only accessible within current node
2. **Protected scope** (`.` prefix) - Accessible to current node and direct children
3. **Public scope** (no prefix) - Accessible globally
4. **Local scope** - Current node's definitions, merged with inherited scope

## Linguistic Integration

### Private Linguistic System
```typescript
// Resolves filename extensions to pragmas
function resolvePrivatePragma(extensions) {
  const pragmaTerms = privateLinguisticSystem.parse(extensions);
  const pragma = pragmaRegistry.resolve(pragmaTerms);
  
  return pragma || noopPragma;
}
```

### Public Linguistic System  
```typescript
// Handles public pragma composition
function composePublicPragma(privatePragmaResult) {
  const publicExtensions = getPublicPragmaDefinitions();
  
  return publicExtensions.reduce((result, extension) => {
    return extension(result);
  }, privatePragmaResult);
}
```

## Reactive Behavior

### TreenityTree Integration
```typescript
// Pragmas form reactive trees
class PragmaTree extends TreenityTree {
  // Automatic reaction to structure modifications
  onStructureChange(change) {
    // Recompute affected pragma APIs
    this.recomputeAffectedNodes(change.path);
    
    // Update type namespace
    this.updateTypeNamespace();
    
    // Emit change events
    this.emit('pragmaChange', change);
  }
}
```

### AutoExec Pattern
```typescript
// WebDev runs as autoexec function
function webDevAutoExec() {
  // React to any modifications of the structure
  pragmaTree.watch((change) => {
    // Automatically regenerate affected APIs
    regenerateAPIs(change.affectedNodes);
    
    // Update development environment
    updateDevEnvironment(change);
  });
}
```

## Composition Strategies

### Sequential Composition
```typescript
// Pragmas compose in sequence
function sequentialComposition(pragmas) {
  return function(scope) {
    return pragmas.reduce((result, pragma) => {
      return pragma(result);
    }, scope);
  };
}
```

### Parallel Composition
```typescript
// Pragmas compose in parallel
function parallelComposition(pragmas) {
  return function(scope) {
    const results = pragmas.map(pragma => pragma(scope));
    return mergeResults(results);
  };
}
```

## Error Handling Behaviors

### Fallback Patterns
```typescript
// If pragma not found, fallback to noop
function resolvePragmaWithFallback(pragmaName) {
  const pragma = pragmaRegistry.get(pragmaName);
  
  if (!pragma) {
    console.warn(`Pragma '${pragmaName}' not found, using noop`);
    return noopPragma;
  }
  
  return pragma;
}

// Noop pragma
function noopPragma(scope) {
  return scope; // Pass through unchanged
}
```

### Graceful Degradation
```typescript
// Pragma execution with error handling
function executePragmaWithGracefulDegradation(pragma, scope) {
  try {
    return pragma(scope);
  } catch (error) {
    console.error(`Pragma execution failed: ${error.message}`);
    
    // Return scope with error metadata
    return {
      ...scope,
      error: error.message,
      degraded: true
    };
  }
}
```

This behavioral specification captures the **actual patterns** the pragma system implements, focusing on **how it behaves** rather than what CRUD operations it provides.
