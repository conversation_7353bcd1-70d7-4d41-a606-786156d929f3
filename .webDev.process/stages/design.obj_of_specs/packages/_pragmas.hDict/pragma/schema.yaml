# Pragma Pragma Schema Definition
# Defines the validatable schemas for the foundational Pragma system
# The meta-pragma that defines how all other pragmas work

pragma:
  name: "pragma"
  version: "1.0.0"
  description: |
    The foundational Pragma system that defines how all other pragmas are created, 
    managed, and executed. This is the meta-pragma that provides the core 
    infrastructure for the entire SpiceTime ecosystem.

# Core Operations
operations:
  create:
    description: "Create a new pragma instance"
    input: CreatePragmaInput
    output: CreatePragmaOutput
    errors: [PragmaCreationError, ValidationError]
    
  execute:
    description: "Execute a pragma with given input"
    input: ExecutePragmaInput
    output: ExecutePragmaOutput
    errors: [ExecutionError, RuntimeError]
    
  compose:
    description: "Compose multiple pragmas into a new pragma"
    input: ComposePragmaInput
    output: ComposePragmaOutput
    errors: [CompositionError, CompatibilityError]
    
  register:
    description: "Register a pragma in the system registry"
    input: RegisterPragmaInput
    output: RegisterPragmaOutput
    errors: [RegistrationError, ConflictError]
    
  discover:
    description: "Discover available pragmas by criteria"
    input: DiscoverPragmaInput
    output: DiscoverPragmaOutput
    errors: [DiscoveryError]
    
  validate:
    description: "Validate pragma definition and structure"
    input: ValidatePragmaInput
    output: ValidatePragmaOutput
    errors: [ValidationError]
    
  extend:
    description: "Extend an existing pragma with additional capabilities"
    input: ExtendPragmaInput
    output: ExtendPragmaOutput
    errors: [ExtensionError, InheritanceError]

# Type Definitions
types:
  # Core Pragma Definition
  PragmaDefinition:
    type: object
    required: [name, version, type, schema, behavior]
    properties:
      name:
        type: string
        pattern: "^[a-z][a-z0-9_]*$"
        description: "Pragma name (lowercase, underscore-separated)"
      version:
        type: string
        pattern: "^\\d+\\.\\d+\\.\\d+$"
        description: "Semantic version"
      type:
        type: string
        enum: [foundation, transformation, composition, service, component, linguistic]
        description: "Pragma type classification"
      description:
        type: string
        description: "Human-readable description"
      schema:
        $ref: "#/types/PragmaSchema"
      behavior:
        $ref: "#/types/PragmaBehavior"
      metadata:
        $ref: "#/types/PragmaMetadata"
      children:
        type: object
        additionalProperties:
          $ref: "#/types/ChildPragmaDefinition"
        description: "Child pragma definitions"
      dependencies:
        type: array
        items:
          $ref: "#/types/PragmaDependency"
        description: "Pragma dependencies"
      linguistic:
        $ref: "#/types/LinguisticInterface"
        description: "Linguistic interface definition"

  PragmaSchema:
    type: object
    required: [input, output]
    properties:
      input:
        $ref: "#/types/InputSchema"
        description: "Input data schema"
      output:
        $ref: "#/types/OutputSchema"
        description: "Output data schema"
      state:
        $ref: "#/types/StateSchema"
        description: "Internal state schema"
      events:
        type: array
        items:
          $ref: "#/types/EventSchema"
        description: "Event schemas"
      configuration:
        $ref: "#/types/ConfigurationSchema"
        description: "Configuration schema"

  PragmaBehavior:
    type: object
    required: [operations]
    properties:
      operations:
        type: array
        items:
          $ref: "#/types/OperationDefinition"
        description: "Pragma operations"
      lifecycle:
        $ref: "#/types/LifecycleDefinition"
        description: "Lifecycle hooks"
      events:
        $ref: "#/types/EventHandling"
        description: "Event handling behavior"
      composition:
        $ref: "#/types/CompositionBehavior"
        description: "Composition behavior"
      validation:
        $ref: "#/types/ValidationBehavior"
        description: "Validation behavior"

  PragmaMetadata:
    type: object
    properties:
      author:
        type: string
        description: "Pragma author"
      license:
        type: string
        description: "License identifier"
      tags:
        type: array
        items:
          type: string
        description: "Pragma tags for discovery"
      category:
        type: string
        enum: [core, data, ui, service, utility, integration]
        description: "Pragma category"
      stability:
        type: string
        enum: [experimental, alpha, beta, stable, deprecated]
        description: "Stability level"
      performance:
        $ref: "#/types/PerformanceMetadata"
        description: "Performance characteristics"
      documentation:
        $ref: "#/types/DocumentationMetadata"
        description: "Documentation metadata"

  # Create Operation Types
  CreatePragmaInput:
    type: object
    required: [definition]
    properties:
      definition:
        $ref: "#/types/PragmaDefinition"
      options:
        $ref: "#/types/CreationOptions"
      context:
        $ref: "#/types/CreationContext"

  CreationOptions:
    type: object
    properties:
      validateDefinition:
        type: boolean
        default: true
        description: "Validate pragma definition before creation"
      generateMetadata:
        type: boolean
        default: true
        description: "Generate metadata for IDE integration"
      createTests:
        type: boolean
        default: false
        description: "Generate test templates"
      registerGlobally:
        type: boolean
        default: false
        description: "Register in global pragma registry"

  CreationContext:
    type: object
    properties:
      namespace:
        type: string
        description: "Namespace for the pragma"
      parentPragma:
        type: string
        description: "Parent pragma if this is a child"
      environment:
        type: string
        enum: [development, testing, production]
        description: "Creation environment"
      metadata:
        type: object
        additionalProperties: true
        description: "Additional context metadata"

  CreatePragmaOutput:
    type: object
    required: [pragma, metadata]
    properties:
      pragma:
        $ref: "#/types/PragmaInstance"
      metadata:
        $ref: "#/types/CreationMetadata"
      warnings:
        type: array
        items:
          $ref: "#/types/Warning"

  # Execute Operation Types
  ExecutePragmaInput:
    type: object
    required: [pragma, input]
    properties:
      pragma:
        oneOf:
          - type: string
            description: "Pragma name to execute"
          - $ref: "#/types/PragmaInstance"
            description: "Pragma instance to execute"
      input:
        description: "Input data for pragma execution"
      options:
        $ref: "#/types/ExecutionOptions"
      context:
        $ref: "#/types/ExecutionContext"

  ExecutionOptions:
    type: object
    properties:
      timeout:
        type: integer
        minimum: 0
        description: "Execution timeout in milliseconds"
      retries:
        type: integer
        minimum: 0
        default: 0
        description: "Number of retry attempts"
      async:
        type: boolean
        default: false
        description: "Execute asynchronously"
      validateInput:
        type: boolean
        default: true
        description: "Validate input against schema"
      validateOutput:
        type: boolean
        default: true
        description: "Validate output against schema"
      trackPerformance:
        type: boolean
        default: false
        description: "Track performance metrics"

  ExecutionContext:
    type: object
    properties:
      correlationId:
        type: string
        description: "Correlation ID for tracking"
      parentExecution:
        type: string
        description: "Parent execution ID if nested"
      environment:
        type: object
        additionalProperties: true
        description: "Environment variables"
      services:
        type: object
        additionalProperties: true
        description: "Available services"
      pragmaRegistry:
        type: string
        description: "Pragma registry to use"

  ExecutePragmaOutput:
    type: object
    required: [result, metadata]
    properties:
      result:
        description: "Execution result"
      metadata:
        $ref: "#/types/ExecutionMetadata"
      events:
        type: array
        items:
          $ref: "#/types/ExecutionEvent"
      warnings:
        type: array
        items:
          $ref: "#/types/Warning"

  # Compose Operation Types
  ComposePragmaInput:
    type: object
    required: [pragmas, composition]
    properties:
      pragmas:
        type: array
        items:
          oneOf:
            - type: string
              description: "Pragma name"
            - $ref: "#/types/PragmaInstance"
              description: "Pragma instance"
        description: "Pragmas to compose"
      composition:
        $ref: "#/types/CompositionDefinition"
      options:
        $ref: "#/types/CompositionOptions"
      metadata:
        $ref: "#/types/CompositionMetadata"

  CompositionDefinition:
    type: object
    required: [type, strategy]
    properties:
      type:
        type: string
        enum: [sequential, parallel, conditional, pipeline, tree]
        description: "Composition type"
      strategy:
        type: string
        enum: [merge, chain, branch, reduce, map]
        description: "Composition strategy"
      dataFlow:
        $ref: "#/types/DataFlowDefinition"
        description: "Data flow between pragmas"
      errorHandling:
        $ref: "#/types/ErrorHandlingStrategy"
        description: "Error handling strategy"
      validation:
        $ref: "#/types/CompositionValidation"
        description: "Composition validation rules"

  ComposePragmaOutput:
    type: object
    required: [composedPragma, metadata]
    properties:
      composedPragma:
        $ref: "#/types/PragmaInstance"
      metadata:
        $ref: "#/types/CompositionResultMetadata"
      warnings:
        type: array
        items:
          $ref: "#/types/Warning"

  # Register Operation Types
  RegisterPragmaInput:
    type: object
    required: [pragma]
    properties:
      pragma:
        oneOf:
          - $ref: "#/types/PragmaDefinition"
          - $ref: "#/types/PragmaInstance"
      registry:
        type: string
        description: "Target registry name"
      namespace:
        type: string
        description: "Namespace for registration"
      options:
        $ref: "#/types/RegistrationOptions"

  RegistrationOptions:
    type: object
    properties:
      overwrite:
        type: boolean
        default: false
        description: "Overwrite existing pragma"
      validateUniqueness:
        type: boolean
        default: true
        description: "Validate name uniqueness"
      generateDocumentation:
        type: boolean
        default: true
        description: "Generate documentation"
      publishEvents:
        type: boolean
        default: true
        description: "Publish registration events"

  RegisterPragmaOutput:
    type: object
    required: [registered, metadata]
    properties:
      registered:
        type: boolean
        description: "Whether registration was successful"
      pragmaId:
        type: string
        description: "Unique pragma identifier"
      registryPath:
        type: string
        description: "Full registry path"
      metadata:
        $ref: "#/types/RegistrationMetadata"

  # Discover Operation Types
  DiscoverPragmaInput:
    type: object
    properties:
      criteria:
        $ref: "#/types/DiscoveryCriteria"
      options:
        $ref: "#/types/DiscoveryOptions"

  DiscoveryCriteria:
    type: object
    properties:
      name:
        type: string
        description: "Pragma name pattern"
      type:
        type: string
        enum: [foundation, transformation, composition, service, component, linguistic]
        description: "Pragma type"
      category:
        type: string
        enum: [core, data, ui, service, utility, integration]
        description: "Pragma category"
      tags:
        type: array
        items:
          type: string
        description: "Required tags"
      capabilities:
        type: array
        items:
          type: string
        description: "Required capabilities"
      namespace:
        type: string
        description: "Namespace to search"

  DiscoveryOptions:
    type: object
    properties:
      includeMetadata:
        type: boolean
        default: true
        description: "Include pragma metadata"
      includeSchema:
        type: boolean
        default: false
        description: "Include pragma schema"
      limit:
        type: integer
        minimum: 1
        maximum: 1000
        default: 100
        description: "Maximum results to return"
      sortBy:
        type: string
        enum: [name, type, category, stability, performance]
        default: name
        description: "Sort results by field"

  DiscoverPragmaOutput:
    type: object
    required: [pragmas, metadata]
    properties:
      pragmas:
        type: array
        items:
          $ref: "#/types/PragmaInfo"
      metadata:
        $ref: "#/types/DiscoveryMetadata"

  # Validate Operation Types
  ValidatePragmaInput:
    type: object
    required: [pragma]
    properties:
      pragma:
        oneOf:
          - $ref: "#/types/PragmaDefinition"
          - $ref: "#/types/PragmaInstance"
      rules:
        type: array
        items:
          $ref: "#/types/ValidationRule"
      options:
        $ref: "#/types/ValidationOptions"

  ValidatePragmaOutput:
    type: object
    required: [valid, results]
    properties:
      valid:
        type: boolean
        description: "Overall validation result"
      results:
        type: array
        items:
          $ref: "#/types/ValidationResult"
      metadata:
        $ref: "#/types/ValidationMetadata"

  # Extend Operation Types
  ExtendPragmaInput:
    type: object
    required: [basePragma, extension]
    properties:
      basePragma:
        oneOf:
          - type: string
            description: "Base pragma name"
          - $ref: "#/types/PragmaInstance"
            description: "Base pragma instance"
      extension:
        $ref: "#/types/PragmaExtension"
      options:
        $ref: "#/types/ExtensionOptions"

  PragmaExtension:
    type: object
    properties:
      name:
        type: string
        description: "Extended pragma name"
      additionalSchema:
        $ref: "#/types/SchemaExtension"
        description: "Additional schema elements"
      additionalBehavior:
        $ref: "#/types/BehaviorExtension"
        description: "Additional behavior elements"
      overrides:
        $ref: "#/types/BehaviorOverrides"
        description: "Behavior overrides"
      metadata:
        $ref: "#/types/ExtensionMetadata"
        description: "Extension metadata"

  ExtendPragmaOutput:
    type: object
    required: [extendedPragma, metadata]
    properties:
      extendedPragma:
        $ref: "#/types/PragmaInstance"
      metadata:
        $ref: "#/types/ExtensionResultMetadata"
      warnings:
        type: array
        items:
          $ref: "#/types/Warning"

  # Core Supporting Types
  PragmaInstance:
    type: object
    required: [id, name, version, definition]
    properties:
      id:
        type: string
        description: "Unique pragma instance ID"
      name:
        type: string
        description: "Pragma name"
      version:
        type: string
        description: "Pragma version"
      definition:
        $ref: "#/types/PragmaDefinition"
      state:
        type: object
        additionalProperties: true
        description: "Current pragma state"
      context:
        $ref: "#/types/PragmaContext"
        description: "Pragma execution context"
      registry:
        type: string
        description: "Registry where pragma is registered"

  PragmaContext:
    type: object
    properties:
      namespace:
        type: string
        description: "Pragma namespace"
      environment:
        type: string
        description: "Execution environment"
      services:
        type: object
        additionalProperties: true
        description: "Available services"
      configuration:
        type: object
        additionalProperties: true
        description: "Pragma configuration"

  ChildPragmaDefinition:
    type: object
    required: [name, type]
    properties:
      name:
        type: string
        description: "Child pragma name"
      type:
        type: string
        description: "Child pragma type"
      required:
        type: boolean
        default: false
        description: "Whether child is required"
      defaultImplementation:
        type: string
        description: "Default implementation pragma"
      constraints:
        type: array
        items:
          $ref: "#/types/ChildConstraint"
        description: "Constraints on child pragma"

  PragmaDependency:
    type: object
    required: [name, version]
    properties:
      name:
        type: string
        description: "Dependency pragma name"
      version:
        type: string
        description: "Required version (semver)"
      optional:
        type: boolean
        default: false
        description: "Whether dependency is optional"
      namespace:
        type: string
        description: "Dependency namespace"

  LinguisticInterface:
    type: object
    properties:
      vocabulary:
        type: array
        items:
          type: string
        description: "Vocabulary terms for this pragma"
      patterns:
        type: array
        items:
          $ref: "#/types/LinguisticPattern"
        description: "Linguistic patterns"
      grammar:
        type: array
        items:
          $ref: "#/types/GrammarRule"
        description: "Grammar rules"

  # Schema Types
  InputSchema:
    type: object
    required: [type]
    properties:
      type:
        type: string
        enum: [object, array, string, number, boolean, null, any]
      properties:
        type: object
        additionalProperties:
          $ref: "#/types/PropertySchema"
      required:
        type: array
        items:
          type: string
      additionalProperties:
        type: boolean
        default: false

  OutputSchema:
    type: object
    required: [type]
    properties:
      type:
        type: string
        enum: [object, array, string, number, boolean, null, any]
      properties:
        type: object
        additionalProperties:
          $ref: "#/types/PropertySchema"
      examples:
        type: array
        items: {}
        description: "Example outputs"

  StateSchema:
    type: object
    properties:
      properties:
        type: object
        additionalProperties:
          $ref: "#/types/PropertySchema"
      persistent:
        type: array
        items:
          type: string
        description: "Properties that persist across executions"
      transient:
        type: array
        items:
          type: string
        description: "Properties that are reset each execution"

  PropertySchema:
    type: object
    required: [type]
    properties:
      type:
        type: string
        enum: [object, array, string, number, boolean, null, any]
      description:
        type: string
      default:
        description: "Default value"
      enum:
        type: array
        description: "Allowed values"
      format:
        type: string
        description: "String format (date, email, etc.)"
      minimum:
        type: number
        description: "Minimum value for numbers"
      maximum:
        type: number
        description: "Maximum value for numbers"
      minLength:
        type: integer
        description: "Minimum length for strings/arrays"
      maxLength:
        type: integer
        description: "Maximum length for strings/arrays"

  EventSchema:
    type: object
    required: [name, type]
    properties:
      name:
        type: string
        description: "Event name"
      type:
        type: string
        enum: [lifecycle, data, error, user, system]
        description: "Event type"
      data:
        $ref: "#/types/PropertySchema"
        description: "Event data schema"
      timing:
        type: string
        enum: [before, after, during, async]
        description: "Event timing"

  ConfigurationSchema:
    type: object
    properties:
      properties:
        type: object
        additionalProperties:
          $ref: "#/types/PropertySchema"
      required:
        type: array
        items:
          type: string
      groups:
        type: array
        items:
          $ref: "#/types/ConfigurationGroup"
        description: "Configuration groups for UI"

  # Behavior Types
  OperationDefinition:
    type: object
    required: [name, type]
    properties:
      name:
        type: string
        description: "Operation name"
      type:
        type: string
        enum: [sync, async, stream, batch]
        description: "Operation type"
      input:
        $ref: "#/types/InputSchema"
        description: "Operation input schema"
      output:
        $ref: "#/types/OutputSchema"
        description: "Operation output schema"
      preconditions:
        type: array
        items:
          $ref: "#/types/Condition"
        description: "Preconditions for execution"
      postconditions:
        type: array
        items:
          $ref: "#/types/Condition"
        description: "Postconditions after execution"
      sideEffects:
        type: array
        items:
          $ref: "#/types/SideEffect"
        description: "Known side effects"

  LifecycleDefinition:
    type: object
    properties:
      onCreate:
        $ref: "#/types/LifecycleHook"
        description: "Called when pragma is created"
      onInitialize:
        $ref: "#/types/LifecycleHook"
        description: "Called when pragma is initialized"
      onExecute:
        $ref: "#/types/LifecycleHook"
        description: "Called before each execution"
      onComplete:
        $ref: "#/types/LifecycleHook"
        description: "Called after each execution"
      onError:
        $ref: "#/types/LifecycleHook"
        description: "Called when error occurs"
      onDestroy:
        $ref: "#/types/LifecycleHook"
        description: "Called when pragma is destroyed"

  LifecycleHook:
    type: object
    properties:
      async:
        type: boolean
        default: false
        description: "Whether hook is asynchronous"
      timeout:
        type: integer
        description: "Hook timeout in milliseconds"
      retries:
        type: integer
        default: 0
        description: "Number of retries on failure"

  EventHandling:
    type: object
    properties:
      emits:
        type: array
        items:
          type: string
        description: "Events this pragma emits"
      listens:
        type: array
        items:
          type: string
        description: "Events this pragma listens to"
      handlers:
        type: object
        additionalProperties:
          $ref: "#/types/EventHandler"
        description: "Event handlers"

  EventHandler:
    type: object
    required: [type]
    properties:
      type:
        type: string
        enum: [sync, async, debounced, throttled]
      timeout:
        type: integer
        description: "Handler timeout"
      retries:
        type: integer
        default: 0
        description: "Retry attempts"
      errorHandling:
        type: string
        enum: [ignore, log, throw, retry]
        default: log

  CompositionBehavior:
    type: object
    properties:
      composable:
        type: boolean
        default: true
        description: "Whether pragma can be composed"
      compositionTypes:
        type: array
        items:
          type: string
          enum: [sequential, parallel, conditional, pipeline]
        description: "Supported composition types"
      dataFlowRules:
        type: array
        items:
          $ref: "#/types/DataFlowRule"
        description: "Data flow rules for composition"

  ValidationBehavior:
    type: object
    properties:
      validateInput:
        type: boolean
        default: true
        description: "Validate input by default"
      validateOutput:
        type: boolean
        default: true
        description: "Validate output by default"
      customValidators:
        type: array
        items:
          $ref: "#/types/CustomValidator"
        description: "Custom validation functions"

  # Metadata Types
  PerformanceMetadata:
    type: object
    properties:
      averageExecutionTime:
        type: number
        description: "Average execution time in milliseconds"
      memoryUsage:
        type: number
        description: "Average memory usage in bytes"
      cpuIntensive:
        type: boolean
        description: "Whether pragma is CPU intensive"
      ioIntensive:
        type: boolean
        description: "Whether pragma is I/O intensive"
      scalability:
        type: string
        enum: [linear, logarithmic, exponential, constant]
        description: "Scalability characteristics"

  DocumentationMetadata:
    type: object
    properties:
      readme:
        type: string
        description: "README content"
      examples:
        type: array
        items:
          $ref: "#/types/ExampleDefinition"
        description: "Usage examples"
      tutorials:
        type: array
        items:
          $ref: "#/types/TutorialDefinition"
        description: "Tutorials"
      apiDocs:
        type: string
        description: "API documentation URL"

  # Common Types
  Warning:
    type: object
    required: [code, message]
    properties:
      code:
        type: string
        description: "Warning code"
      message:
        type: string
        description: "Warning message"
      severity:
        type: string
        enum: [info, warning, error]
        default: warning
      path:
        type: string
        description: "Path where warning occurred"

  Condition:
    type: object
    required: [expression]
    properties:
      expression:
        type: string
        description: "Condition expression"
      message:
        type: string
        description: "Error message if condition fails"

  SideEffect:
    type: object
    required: [type, description]
    properties:
      type:
        type: string
        enum: [state-change, io, network, filesystem, database]
        description: "Side effect type"
      description:
        type: string
        description: "Side effect description"
      reversible:
        type: boolean
        default: false
        description: "Whether side effect is reversible"

  PragmaInfo:
    type: object
    required: [name, version, type]
    properties:
      name:
        type: string
      version:
        type: string
      type:
        type: string
      description:
        type: string
      metadata:
        $ref: "#/types/PragmaMetadata"
      schema:
        $ref: "#/types/PragmaSchema"

  ValidationRule:
    type: object
    required: [name, type]
    properties:
      name:
        type: string
        description: "Rule name"
      type:
        type: string
        enum: [schema, business, performance, security]
        description: "Rule type"
      expression:
        type: string
        description: "Rule expression"
      severity:
        type: string
        enum: [error, warning, info]
        default: error

  ValidationResult:
    type: object
    required: [rule, passed]
    properties:
      rule:
        type: string
        description: "Rule name"
      passed:
        type: boolean
        description: "Whether rule passed"
      message:
        type: string
        description: "Result message"
      details:
        type: object
        additionalProperties: true
        description: "Additional details"

  # Additional supporting types would continue here...
  # (Truncated for brevity - would include all remaining types)

# Error Types
errors:
  PragmaCreationError:
    type: object
    required: [code, message]
    properties:
      code:
        type: string
        enum: [INVALID_DEFINITION, DEPENDENCY_MISSING, NAME_CONFLICT]
      message:
        type: string
      definition:
        $ref: "#/types/PragmaDefinition"

  ExecutionError:
    type: object
    required: [code, message]
    properties:
      code:
        type: string
        enum: [RUNTIME_ERROR, TIMEOUT, VALIDATION_FAILED]
      message:
        type: string
      pragma:
        type: string
      input:
        description: "Input that caused the error"

  CompositionError:
    type: object
    required: [code, message]
    properties:
      code:
        type: string
        enum: [INCOMPATIBLE_PRAGMAS, CIRCULAR_DEPENDENCY, COMPOSITION_FAILED]
      message:
        type: string
      pragmas:
        type: array
        items:
          type: string

  RegistrationError:
    type: object
    required: [code, message]
    properties:
      code:
        type: string
        enum: [REGISTRY_UNAVAILABLE, NAME_TAKEN, VALIDATION_FAILED]
      message:
        type: string
      pragma:
        type: string

  ValidationError:
    type: object
    required: [code, message, path]
    properties:
      code:
        type: string
        enum: [SCHEMA_VIOLATION, CONSTRAINT_VIOLATION, TYPE_MISMATCH]
      message:
        type: string
      path:
        type: string
      expected:
        description: "Expected value"
      actual:
        description: "Actual value"
