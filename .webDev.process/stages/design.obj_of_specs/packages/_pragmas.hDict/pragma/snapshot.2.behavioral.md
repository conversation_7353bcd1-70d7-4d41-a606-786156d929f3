# Pragma Behavioral Snapshot 2: Behavioral Dynamics

## Second Pass: Behavioral Dynamics and Processing

### 1. PragmaOperator Construction Process
**Core Construction Pattern**:
```typescript
function pragmaOperator(scope) {
  // 1. Read pragma's own name from filesystem location
  const pragmaName = extractPragmaName(scope.nodePath);
  
  // 2. Locate pragmas that extend this pragma
  const extendingPragmas = locateExtendingPragmas(pragmaName, scope);
  
  // 3. Use l (linguistic system) to process extensions
  const processedExtensions = scope.l.processExtensions(extendingPragmas);
  
  // 4. Construct final pragma operator
  return composeExtensions(processedExtensions);
}
```

### 2. Pragma Name Analysis and Extension Location
**Self-Analysis Process**:
- Pragma reads its own name from filesystem location
- Analyzes parent pragma extensions that might affect it
- Locates all pragmas that extend it through linguistic analysis
- Uses this information to extend itself appropriately

**Extension Location Strategy**:
```typescript
function locateExtendingPragmas(pragmaName, scope) {
  // Look for .pragma.{pragmaName} files in current and parent scopes
  const localExtensions = findLocalExtensions(pragmaName, scope.local);
  const parentExtensions = findParentExtensions(pragmaName, scope.parent);
  const siblingExtensions = findSiblingExtensions(pragmaName, scope.siblings);
  
  return {
    local: localExtensions,
    parent: parentExtensions,
    siblings: siblingExtensions
  };
}
```

### 3. Linguistic Processing of Extensions
**Not a Simple Function Pipeline**:
- Extensions are NOT just a list of functions in a pipe
- They are processed by the linguistic system (`l`)
- The `l` system in the scope determines processing rules
- Different scopes have different `l` systems (private vs public)

**Linguistic Analysis Process**:
```typescript
function processExtensions(extensions, linguisticSystem) {
  // Parse extension names using linguistic rules
  const parsedExtensions = extensions.map(ext => 
    linguisticSystem.parse(ext.name)
  );
  
  // Analyze relationships between extensions
  const relationships = linguisticSystem.analyzeRelationships(parsedExtensions);
  
  // Determine composition order based on linguistic rules
  const compositionOrder = linguisticSystem.determineCompositionOrder(relationships);
  
  // Apply linguistic transformations
  return linguisticSystem.compose(parsedExtensions, compositionOrder);
}
```

### 4. Private vs Public Linguistic Systems
**Scope-Dependent Linguistic Processing**:
- If `_.pragma` script - uses **private scope** and **private l**
- If `pragma` script - uses **public scope** and **public l**
- WebDev process maintains both private and public `l` systems
- Each scope gets appropriate `l` system injected

**WebDev Process L Management**:
```typescript
class WebDevProcess {
  private privateLinguisticSystem: LinguisticSystem;
  private publicLinguisticSystem: LinguisticSystem;
  
  injectLinguisticSystem(scope) {
    if (scope.isPrivate) {
      scope.l = this.privateLinguisticSystem;
    } else {
      scope.l = this.publicLinguisticSystem;
    }
    
    // Add extensions from .pragma.l scripts
    const localLExtensions = this.findLocalLExtensions(scope);
    scope.l.extend(localLExtensions);
  }
}
```

### 5. .pragma.l Script Extensions
**Linguistic System Extension**:
- `.pragma.l` scripts extend the linguistic capabilities
- WebDev process adds its own extension of `l` from `.pragma.l` script
- These extensions modify how pragma names are parsed and processed
- Local linguistic extensions affect local pragma processing

**L Extension Pattern**:
```typescript
// .pragma.l script example
export default function extendLinguistics(baseLinguisticSystem) {
  return {
    ...baseLinguisticSystem,
    
    // Add custom parsing rules
    parseCustomPatterns: (text) => {
      // Custom parsing logic for this scope
    },
    
    // Add custom composition rules
    composeCustom: (extensions) => {
      // Custom composition logic
    },
    
    // Override default behaviors
    parse: (text) => {
      // Enhanced parsing with local rules
      const baseResult = baseLinguisticSystem.parse(text);
      return this.parseCustomPatterns(text) || baseResult;
    }
  };
}
```

### 6. Context as tTree Instance Creation
**Context Namespace → tTree Conversion**:
- Context creation becomes namespace in scopes
- **This is what becomes tTree instances**
- Each context namespace in scope.context becomes a separate tTree
- Multiple tTree instances form a forest managed by forestry.types

**Context to tTree Process**:
```typescript
function createContextTrees(scope) {
  const contextTrees = new Map();
  
  // Each context namespace becomes a tTree
  for (const [contextName, contextData] of Object.entries(scope.context)) {
    const tTree = new TreenityTree({
      name: contextName,
      data: contextData,
      reactive: true
    });
    
    contextTrees.set(contextName, tTree);
  }
  
  return contextTrees;
}
```

### 7. Forest Management System
**Multiple tTree Coordination**:
- Forest manages multiple tTree instances
- Forestry.types provides typing for forest operations
- Cross-tree relationships and synchronization
- Forest-level operations and queries

**Forest Coordination**:
```typescript
class PragmaForest {
  private trees: Map<string, TreenityTree> = new Map();
  
  addContextTree(name: string, contextData: any) {
    const tree = new TreenityTree({
      name,
      data: contextData,
      reactive: true
    });
    
    this.trees.set(name, tree);
    this.synchronizeTrees();
  }
  
  synchronizeTrees() {
    // Synchronize related data across trees
    for (const [name, tree] of this.trees) {
      tree.onUpdate((change) => {
        this.propagateChange(name, change);
      });
    }
  }
}
```

### 8. Dependency Integration Through Scope
**Deps Namespace Injection**:
- Pragma takes what deps provide as API and sticks it in scope
- Not concerned with what's inside namespaces in scopes (like `l`)
- Simply provides access through `deps` namespace
- Eliminates traditional import statements

**Scope Injection Process**:
```typescript
function injectDependencies(scope, pragmaDeps) {
  // Create deps namespace in scope
  scope.deps = {};
  
  // Inject each dependency realm
  for (const [realm, packages] of Object.entries(pragmaDeps)) {
    scope.deps[realm] = {};
    
    for (const [packageName, packageAPI] of Object.entries(packages)) {
      // Simply stick the API in without analyzing internals
      scope.deps[realm][packageName] = packageAPI;
    }
  }
  
  return scope;
}
```

### 9. Entanglement Between Systems
**System Interdependencies**:
- **Pragma** reads its name and locates extensions
- **L system** processes extensions linguistically
- **Context** creates tTree instances in scope
- **Forest** manages multiple tTree instances
- **Deps** provides external APIs through scope
- **WebDev** orchestrates all systems together

**Entanglement Pattern**:
```typescript
function orchestrateSystems(nodePath) {
  // 1. Create base scope
  const scope = createBaseScope(nodePath);
  
  // 2. Inject dependencies
  injectDependencies(scope, loadPragmaDeps(nodePath));
  
  // 3. Inject linguistic system
  injectLinguisticSystem(scope, determineScope(nodePath));
  
  // 4. Create context trees
  const contextTrees = createContextTrees(scope);
  scope.contextTrees = contextTrees;
  
  // 5. Create forest
  const forest = new PragmaForest(contextTrees);
  scope.forest = forest;
  
  // 6. Generate pragma operator
  const pragmaOperator = generatePragmaOperator(scope);
  
  return pragmaOperator;
}
```

### 10. Reactive Behavior Propagation
**Cross-System Reactivity**:
- Changes in one system propagate to others
- tTree changes affect pragma behavior
- Pragma changes update linguistic processing
- Forest changes synchronize across contexts

**Reactive Propagation**:
```typescript
class ReactiveSystemOrchestrator {
  setupReactivity(scope) {
    // React to tTree changes
    scope.contextTrees.forEach(tree => {
      tree.onUpdate((change) => {
        this.updatePragmaOperator(scope, change);
        this.updateLinguisticSystem(scope, change);
      });
    });
    
    // React to linguistic system changes
    scope.l.onUpdate((change) => {
      this.reprocessExtensions(scope, change);
    });
    
    // React to dependency changes
    scope.deps.onUpdate((change) => {
      this.reinjectDependencies(scope, change);
    });
  }
}
```

## Key Behavioral Insights

1. **Self-Analysis**: Pragma reads its own name and analyzes extensions
2. **Linguistic Processing**: Extensions processed by `l`, not simple function pipeline
3. **Scope-Dependent L**: Private vs public `l` systems based on scope
4. **Context → tTree**: Context namespaces become tTree instances
5. **Forest Coordination**: Multiple tTree instances managed as forest
6. **System Entanglement**: All systems interdependent and reactive
7. **WebDev Orchestration**: WebDev process coordinates all systems
8. **Extension Analysis**: Pragma locates and processes its own extensions
9. **Reactive Propagation**: Changes propagate across all systems
10. **Scope Injection**: Dependencies and systems injected into scope

This second pass reveals the **dynamic behavioral patterns** - how the systems interact, process information, and react to changes.
