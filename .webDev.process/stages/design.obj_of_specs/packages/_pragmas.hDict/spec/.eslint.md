# ESLint Configuration for Spec Pragma

## Root ESLint Configuration Modifications

### Add to Root .eslintrc.json

**Extend existing rules with spec pragma specific patterns:**

```json
{
  "overrides": [
    {
      "files": ["packages/_pragmas.hDict/spec/src/**/*.ts"],
      "rules": {
        "@typescript-eslint/no-explicit-any": "warn",
        "@typescript-eslint/no-unused-vars": ["error", { "argsIgnorePattern": "^_" }],
        "prefer-const": "error",
        "no-var": "error"
      }
    },
    {
      "files": ["packages/_pragmas.hDict/spec/**/*.feature"],
      "parser": null,
      "rules": {}
    }
  ]
}
```

### Spec Pragma Specific Rules

**For specification parsing code:**
- Allow `any` types when dealing with external schema formats
- Strict typing for internal APIs
- Consistent naming conventions for pragma functions

**For generated code:**
- Follow React component best practices
- Enforce proper TypeScript patterns
- Maintain consistency with existing codebase

## Local ESLint Configuration

### .eslintrc.local.json
```json
{
  "extends": ["../../../../../.eslintrc.json"],
  "parserOptions": {
    "project": "./tsconfig.json"
  },
  "ignorePatterns": [
    "dist/",
    "**/*.feature",
    "**/*.md"
  ]
}
```

## Integration with Existing Tooling

### Reuse Root Configuration
- Extends existing ESLint setup
- Maintains consistency with project standards
- No duplicate rule definitions

### Workspace-Specific Overrides
- Spec pragma specific file patterns
- Gherkin feature file exclusions
- Generated code handling
