# Spec Pragma

## Description

Extends ST Pragma to orchestrate the transformation of industry-standard specifications into TypeScript implementation code, tests, and documentation. Inherits the foundational pragma system and adds specification-specific child pragmas and transformation logic.

## Extension of ST Pragma

```typescript
class SpecPragma extends STPragma {
  constructor() {
    super('spec', '1.0.0');

    // Inherit ST pragma children:
    // - execute (execution engine)
    // - events (event system)
    // - compose (composition engine)
    // - kernel (kernel integration)
    // - middleware (middleware system)
    // - schema (schema management)

    // Add spec-specific children:
    this.addChild('parse', new ParsePragma());
    this.addChild('generate', new GeneratePragma());
    this.addChild('sync', new SyncPragma());
  }
}
```

## Child Pragma Structure

### Inherited from ST Pragma
- **execute** - Core execution engine (inherited)
- **events** - Event system foundation (inherited, extended)
- **compose** - Composition engine (inherited, extended)
- **kernel** - Kernel integration (inherited)
- **middleware** - Middleware system (inherited, extended)
- **schema** - Schema management (inherited)

### Spec-Specific Child Pragmas

#### 1. parse - Specification Parsing
Extends ST pragma execution to transform raw specification text into semantic structures
- Handles OpenAPI 3.0, JSON Schema, and Gherkin formats
- Validates specification syntax and structure
- Extracts semantic meaning for downstream processing
- Uses ST pragma event system for beforeParse/afterParse events

#### 2. generate - Code Generation
Extends ST pragma execution to transform semantic structures into implementation artifacts
- Generates TypeScript components and types
- Creates Vitest test suites
- Produces Storybook stories and documentation
- Uses ST pragma middleware system for generation plugins

#### 3. sync - Bidirectional Synchronization
Extends ST pragma execution to maintain consistency between specifications and implementation
- Detects changes in specifications or code
- Applies updates while preserving custom modifications
- Resolves conflicts between specification and implementation
- Uses ST pragma kernel integration for resource-intensive sync operations

## Pipeline Orchestration

The spec pragma index orchestrates child pragmas in the following sequence:

```
1. middleware.register() → Register plugins for pipeline
2. events.emit('beforeParse') → Notify middleware of parse start
3. parse.execute() → Transform specification to semantics
4. events.emit('afterParse') → Notify middleware of parse completion
5. events.emit('beforeGenerate') → Notify middleware of generation start
6. generate.execute() → Transform semantics to code
7. events.emit('afterGenerate') → Notify middleware of generation completion
8. sync.execute() → Maintain spec-code consistency
9. compose.execute() → Integrate with pragma ecosystem
```

### Error Handling Flow

```
If parse.execute() fails:
  → events.emit('parseError')
  → middleware.handleError()
  → Abort or retry based on middleware response

If generate.execute() fails:
  → events.emit('generateError')
  → middleware.handleError()
  → Fallback to previous generation or abort

If sync.execute() conflicts:
  → events.emit('syncConflict')
  → middleware.resolveConflict()
  → Manual resolution or automatic strategy
```

### Resource Coordination

```
1. Kernel allocates resources for spec pragma task
2. Each child pragma requests resources through kernel
3. Middleware can request additional resources
4. Resources released upon completion or failure
```

## Supported Specification Formats

### OpenAPI 3.0 Component Schema
```yaml
components:
  schemas:
    UserProfileProps:
      type: object
      required:
        - user
        - onAvatarClick
      properties:
        user:
          $ref: '#/components/schemas/User'
        onAvatarClick:
          type: string
          format: callback
        loading:
          type: boolean
          default: false
        error:
          type: string
          nullable: true
```

### JSON Schema
```json
{
  "$schema": "https://json-schema.org/draft/2020-12/schema",
  "type": "object",
  "properties": {
    "user": {
      "type": "object",
      "properties": {
        "name": { "type": "string" },
        "email": { "type": "string", "format": "email" },
        "avatarUrl": { "type": "string", "format": "uri" }
      },
      "required": ["name", "email", "avatarUrl"]
    },
    "onAvatarClick": { "type": "function" }
  },
  "required": ["user", "onAvatarClick"]
}
```

### Gherkin BDD Specifications
```gherkin
Feature: User Profile Component

  Scenario: Display user information
    Given a user with name "John Doe" and email "<EMAIL>"
    When the component renders
    Then it should display the user's name and email
    And it should show the user's avatar

  Scenario: Handle avatar click
    Given the component is rendered
    When the user clicks the avatar
    Then it should call the onAvatarClick callback
```

## Generated Outputs

### TypeScript Implementation (following React best practices)
```typescript
// Generated from OpenAPI/JSON Schema
import React, { memo } from 'react';

interface User {
  name: string;
  email: string;
  avatarUrl: string;
}

interface UserProfileProps {
  user: User;
  onAvatarClick: () => void;
  loading?: boolean;
  error?: string | null;
}

const UserProfile = memo<UserProfileProps>(({ user, onAvatarClick, loading, error }) => {
  // Implementation generated from specifications
});

export default UserProfile;
```

### Vitest Test Suite (following Testing Library patterns)
```typescript
// Generated from Gherkin scenarios
import { describe, it, expect, vi } from 'vitest';
import { render, fireEvent, screen } from '@testing-library/react';
import UserProfile from './UserProfile';

describe('UserProfile', () => {
  const mockUser = {
    name: 'John Doe',
    email: '<EMAIL>',
    avatarUrl: 'https://example.com/avatar.jpg'
  };

  const mockOnAvatarClick = vi.fn();

  it('should display user information', () => {
    render(<UserProfile user={mockUser} onAvatarClick={mockOnAvatarClick} />);

    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('should handle avatar click', () => {
    render(<UserProfile user={mockUser} onAvatarClick={mockOnAvatarClick} />);

    const avatar = screen.getByRole('img', { name: /john doe/i });
    fireEvent.click(avatar);

    expect(mockOnAvatarClick).toHaveBeenCalledOnce();
  });
});
```

### Storybook Stories (following Storybook CSF)
```typescript
// Generated component stories
import type { Meta, StoryObj } from '@storybook/react';
import UserProfile from './UserProfile';

const meta: Meta<typeof UserProfile> = {
  title: 'Components/UserProfile',
  component: UserProfile,
  parameters: {
    docs: {
      description: {
        component: 'User profile component displaying user information'
      }
    }
  }
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    user: {
      name: 'John Doe',
      email: '<EMAIL>',
      avatarUrl: 'https://example.com/avatar.jpg'
    },
    onAvatarClick: () => console.log('Avatar clicked')
  }
};
```

## Transformation Process

### Parse Phase
Linguistic analysis extracts semantic meaning from specification text using functional programming transformations through the `l` package.

### Generate Phase
Semantic structure drives code generation through template application and AST construction, producing complete implementation artifacts.

### Sync Phase
Bidirectional synchronization maintains consistency between specifications and implementation, allowing changes in either direction.

## Integration

### Pragma Ecosystem
Composes with foundation pragmas (obj, prop, ttree, context) and React pragmas (component, hook, provider) to create complete application structures.

### Kernel Integration
All transformations are scheduled through the SpiceTime kernel with proper priority management and resource allocation.

### Style Tolerance
Multiple expression styles supported through linguistic term matching, allowing developers to work in their preferred format while generating identical outputs.
