# Spec Pragma

## Description

Transforms industry-standard specifications (OpenAPI, JSON Schema, <PERSON><PERSON><PERSON>) into TypeScript implementation code, tests, and documentation. Serves as the bridge between formal specifications and working code in the SpiceTime pragma ecosystem.

## Supported Specification Formats

### OpenAPI 3.0 Component Schema
```yaml
components:
  schemas:
    UserProfileProps:
      type: object
      required:
        - user
        - onAvatarClick
      properties:
        user:
          $ref: '#/components/schemas/User'
        onAvatarClick:
          type: string
          format: callback
        loading:
          type: boolean
          default: false
        error:
          type: string
          nullable: true
```

### JSON Schema
```json
{
  "$schema": "https://json-schema.org/draft/2020-12/schema",
  "type": "object",
  "properties": {
    "user": {
      "type": "object",
      "properties": {
        "name": { "type": "string" },
        "email": { "type": "string", "format": "email" },
        "avatarUrl": { "type": "string", "format": "uri" }
      },
      "required": ["name", "email", "avatarUrl"]
    },
    "onAvatarClick": { "type": "function" }
  },
  "required": ["user", "onAvatarClick"]
}
```

### Gherkin BDD Specifications
```gherkin
Feature: User Profile Component

  Scenario: Display user information
    Given a user with name "John Doe" and email "<EMAIL>"
    When the component renders
    Then it should display the user's name and email
    And it should show the user's avatar

  Scenario: Handle avatar click
    Given the component is rendered
    When the user clicks the avatar
    Then it should call the onAvatarClick callback
```

## Generated Outputs

### TypeScript Implementation (following React best practices)
```typescript
// Generated from OpenAPI/JSON Schema
import React, { memo } from 'react';

interface User {
  name: string;
  email: string;
  avatarUrl: string;
}

interface UserProfileProps {
  user: User;
  onAvatarClick: () => void;
  loading?: boolean;
  error?: string | null;
}

const UserProfile = memo<UserProfileProps>(({ user, onAvatarClick, loading, error }) => {
  // Implementation generated from specifications
});

export default UserProfile;
```

### Vitest Test Suite (following Testing Library patterns)
```typescript
// Generated from Gherkin scenarios
import { describe, it, expect, vi } from 'vitest';
import { render, fireEvent, screen } from '@testing-library/react';
import UserProfile from './UserProfile';

describe('UserProfile', () => {
  const mockUser = {
    name: 'John Doe',
    email: '<EMAIL>',
    avatarUrl: 'https://example.com/avatar.jpg'
  };

  const mockOnAvatarClick = vi.fn();

  it('should display user information', () => {
    render(<UserProfile user={mockUser} onAvatarClick={mockOnAvatarClick} />);

    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('should handle avatar click', () => {
    render(<UserProfile user={mockUser} onAvatarClick={mockOnAvatarClick} />);

    const avatar = screen.getByRole('img', { name: /john doe/i });
    fireEvent.click(avatar);

    expect(mockOnAvatarClick).toHaveBeenCalledOnce();
  });
});
```

### Storybook Stories (following Storybook CSF)
```typescript
// Generated component stories
import type { Meta, StoryObj } from '@storybook/react';
import UserProfile from './UserProfile';

const meta: Meta<typeof UserProfile> = {
  title: 'Components/UserProfile',
  component: UserProfile,
  parameters: {
    docs: {
      description: {
        component: 'User profile component displaying user information'
      }
    }
  }
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    user: {
      name: 'John Doe',
      email: '<EMAIL>',
      avatarUrl: 'https://example.com/avatar.jpg'
    },
    onAvatarClick: () => console.log('Avatar clicked')
  }
};
```

## Transformation Process

### Parse Phase
Linguistic analysis extracts semantic meaning from specification text using functional programming transformations through the `l` package.

### Generate Phase
Semantic structure drives code generation through template application and AST construction, producing complete implementation artifacts.

### Sync Phase
Bidirectional synchronization maintains consistency between specifications and implementation, allowing changes in either direction.

## Integration

### Pragma Ecosystem
Composes with foundation pragmas (obj, prop, ttree, context) and React pragmas (component, hook, provider) to create complete application structures.

### Kernel Integration
All transformations are scheduled through the SpiceTime kernel with proper priority management and resource allocation.

### Style Tolerance
Multiple expression styles supported through linguistic term matching, allowing developers to work in their preferred format while generating identical outputs.
