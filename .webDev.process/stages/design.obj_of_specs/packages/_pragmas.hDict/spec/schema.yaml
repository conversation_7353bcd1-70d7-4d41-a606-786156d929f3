# Spec Pragma Schema Definition
# Defines the validatable schemas for the Spec Pragma operations
# Can be converted to GraphQL, OpenAPI, or other formats as needed

pragma:
  name: "spec"
  version: "1.0.0"
  description: |
    The Spec Pragma provides transformation capabilities for converting industry-standard 
    specifications (OpenAPI, JSON Schema, <PERSON>herkin) into TypeScript implementation code, tests, 
    and documentation with bidirectional synchronization.

# Core Operations
operations:
  parse:
    description: "Parse specification input into semantic structure"
    input: ParseInput
    output: ParseOutput
    errors: [ParseError, ValidationError]
    
  generate:
    description: "Generate implementation code from semantic structure"
    input: GenerateInput
    output: GenerateOutput
    errors: [GenerationError, TemplateError]
    
  sync:
    description: "Synchronize specification and implementation bidirectionally"
    input: SyncInput
    output: SyncOutput
    errors: [SyncError, ConflictError]
    
  validate:
    description: "Validate specification against schema and business rules"
    input: ValidateInput
    output: ValidateOutput
    errors: [ValidationError]
    
  transform:
    description: "Transform between specification formats"
    input: TransformInput
    output: TransformOutput
    errors: [TransformError, FormatError]

# Type Definitions
types:
  # Parse Operation Types
  ParseInput:
    type: object
    required: [input, format]
    properties:
      input:
        oneOf:
          - type: string
            description: "Specification content as string"
          - type: object
            description: "Specification content as parsed object"
      format:
        type: string
        enum: [openapi, json-schema, gherkin, typescript, auto-detect]
        description: "Input specification format"
      options:
        $ref: "#/types/ParseOptions"
      context:
        $ref: "#/types/ParseContext"

  ParseOptions:
    type: object
    properties:
      strictMode:
        type: boolean
        default: false
        description: "Enable strict parsing with enhanced validation"
      preserveComments:
        type: boolean
        default: true
        description: "Preserve comments and documentation"
      resolveReferences:
        type: boolean
        default: true
        description: "Resolve $ref and other references"
      validateSchema:
        type: boolean
        default: true
        description: "Validate against specification schema"

  ParseContext:
    type: object
    properties:
      baseUrl:
        type: string
        description: "Base URL for resolving relative references"
      workingDirectory:
        type: string
        description: "Working directory for file resolution"
      environment:
        type: string
        enum: [development, staging, production]
        description: "Target environment context"
      metadata:
        type: object
        additionalProperties: true
        description: "Additional context metadata"

  ParseOutput:
    type: object
    required: [semanticStructure, metadata]
    properties:
      semanticStructure:
        $ref: "#/types/SemanticStructure"
      metadata:
        $ref: "#/types/ParseMetadata"
      warnings:
        type: array
        items:
          $ref: "#/types/Warning"

  # Core Semantic Structure
  SemanticStructure:
    type: object
    required: [type, version, components]
    properties:
      type:
        type: string
        enum: [api, schema, behavior, component, service]
        description: "Type of specification"
      version:
        type: string
        description: "Semantic version of the structure"
      components:
        type: array
        items:
          $ref: "#/types/Component"
      relationships:
        type: array
        items:
          $ref: "#/types/Relationship"
      constraints:
        type: array
        items:
          $ref: "#/types/Constraint"
      metadata:
        type: object
        additionalProperties: true

  Component:
    type: object
    required: [id, name, type]
    properties:
      id:
        type: string
        description: "Unique component identifier"
      name:
        type: string
        description: "Component name"
      type:
        type: string
        enum: [endpoint, schema, operation, event, workflow, entity, pragma]
      properties:
        type: object
        additionalProperties: true
      children:
        type: array
        items:
          $ref: "#/types/Component"
      annotations:
        type: object
        additionalProperties: true

  # Generate Operation Types
  GenerateInput:
    type: object
    required: [semanticStructure, targets]
    properties:
      semanticStructure:
        $ref: "#/types/SemanticStructure"
      targets:
        type: array
        items:
          $ref: "#/types/GenerationTarget"
      options:
        $ref: "#/types/GenerationOptions"
      templates:
        type: array
        items:
          $ref: "#/types/Template"

  GenerationTarget:
    type: object
    required: [type, language]
    properties:
      type:
        type: string
        enum: [implementation, tests, documentation, types, schemas, pragmas]
      language:
        type: string
        enum: [typescript, javascript, python, java, csharp, go]
      framework:
        type: string
        enum: [react, vue, angular, express, fastapi, spring, dotnet, spicetime]
      outputPath:
        type: string
        description: "Output file or directory path"
      options:
        type: object
        additionalProperties: true

  GenerationOptions:
    type: object
    properties:
      codeStyle:
        $ref: "#/types/CodeStyle"
      optimization:
        $ref: "#/types/OptimizationOptions"
      validation:
        $ref: "#/types/ValidationOptions"
      documentation:
        $ref: "#/types/DocumentationOptions"
      pragmaGeneration:
        $ref: "#/types/PragmaGenerationOptions"

  PragmaGenerationOptions:
    type: object
    properties:
      generateChildPragmas:
        type: boolean
        default: true
        description: "Generate child pragma definitions"
      generateLinguisticInterface:
        type: boolean
        default: true
        description: "Generate linguistic vocabulary mappings"
      generateMetadata:
        type: boolean
        default: true
        description: "Generate pragma metadata for IDE integration"
      generateTests:
        type: boolean
        default: true
        description: "Generate pragma test suites"

  GenerateOutput:
    type: object
    required: [files, metadata]
    properties:
      files:
        type: array
        items:
          $ref: "#/types/GeneratedFile"
      metadata:
        $ref: "#/types/GenerationMetadata"
      warnings:
        type: array
        items:
          $ref: "#/types/Warning"

  GeneratedFile:
    type: object
    required: [path, content, type]
    properties:
      path:
        type: string
        description: "File path relative to output directory"
      content:
        type: string
        description: "Generated file content"
      type:
        type: string
        enum: [implementation, test, documentation, type-definition, schema, pragma]
      language:
        type: string
        description: "Programming language of the content"
      encoding:
        type: string
        default: utf-8
      metadata:
        type: object
        additionalProperties: true

  # Sync Operation Types
  SyncInput:
    type: object
    required: [specification, implementation]
    properties:
      specification:
        $ref: "#/types/SpecificationSource"
      implementation:
        $ref: "#/types/ImplementationSource"
      strategy:
        $ref: "#/types/SyncStrategy"
      options:
        $ref: "#/types/SyncOptions"

  SpecificationSource:
    type: object
    required: [content, format]
    properties:
      content:
        type: string
        description: "Specification content"
      format:
        type: string
        enum: [openapi, json-schema, gherkin]
      lastModified:
        type: string
        format: date-time
      checksum:
        type: string
        description: "Content checksum for change detection"

  ImplementationSource:
    type: object
    required: [files]
    properties:
      files:
        type: array
        items:
          $ref: "#/types/ImplementationFile"
      metadata:
        type: object
        additionalProperties: true

  ImplementationFile:
    type: object
    required: [path, content]
    properties:
      path:
        type: string
      content:
        type: string
      lastModified:
        type: string
        format: date-time
      checksum:
        type: string

  SyncStrategy:
    type: object
    properties:
      direction:
        type: string
        enum: [spec-to-code, code-to-spec, bidirectional]
        default: bidirectional
      conflictResolution:
        type: string
        enum: [manual, spec-wins, code-wins, merge, interactive]
        default: interactive
      preserveCustomizations:
        type: boolean
        default: true
      backupBeforeSync:
        type: boolean
        default: true

  SyncOutput:
    type: object
    required: [status, changes]
    properties:
      status:
        type: string
        enum: [success, conflicts, failed]
      changes:
        type: array
        items:
          $ref: "#/types/SyncChange"
      conflicts:
        type: array
        items:
          $ref: "#/types/SyncConflict"
      metadata:
        $ref: "#/types/SyncMetadata"

  SyncChange:
    type: object
    required: [type, target, description]
    properties:
      type:
        type: string
        enum: [added, modified, deleted, moved]
      target:
        type: string
        enum: [specification, implementation]
      path:
        type: string
        description: "Path of changed item"
      description:
        type: string
        description: "Human-readable change description"
      diff:
        type: string
        description: "Detailed diff of the change"

  SyncConflict:
    type: object
    required: [type, path, description]
    properties:
      type:
        type: string
        enum: [structural, semantic, naming, type-mismatch]
      path:
        type: string
        description: "Path where conflict occurred"
      description:
        type: string
        description: "Conflict description"
      specValue:
        description: "Value from specification"
      codeValue:
        description: "Value from implementation"
      suggestions:
        type: array
        items:
          $ref: "#/types/ConflictResolution"

  # Validation Types
  ValidateInput:
    type: object
    required: [input]
    properties:
      input:
        oneOf:
          - type: string
          - type: object
      format:
        type: string
        enum: [openapi, json-schema, gherkin, auto-detect]
      rules:
        type: array
        items:
          type: string
      options:
        $ref: "#/types/ValidationOptions"

  ValidateOutput:
    type: object
    required: [valid]
    properties:
      valid:
        type: boolean
      errors:
        type: array
        items:
          $ref: "#/types/ValidationError"
      warnings:
        type: array
        items:
          $ref: "#/types/Warning"
      metadata:
        type: object
        additionalProperties: true

  # Transform Types
  TransformInput:
    type: object
    required: [input, sourceFormat, targetFormat]
    properties:
      input:
        oneOf:
          - type: string
          - type: object
      sourceFormat:
        type: string
        enum: [openapi, json-schema, gherkin]
      targetFormat:
        type: string
        enum: [openapi, json-schema, gherkin]
      options:
        type: object
        additionalProperties: true

  TransformOutput:
    type: object
    required: [output, metadata]
    properties:
      output:
        oneOf:
          - type: string
          - type: object
      metadata:
        type: object
        additionalProperties: true
      warnings:
        type: array
        items:
          $ref: "#/types/Warning"

  # Common Types
  Warning:
    type: object
    required: [code, message]
    properties:
      code:
        type: string
        description: "Warning code for programmatic handling"
      message:
        type: string
        description: "Human-readable warning message"
      severity:
        type: string
        enum: [info, warning, error]
        default: warning
      path:
        type: string
        description: "Path where warning occurred"
      suggestion:
        type: string
        description: "Suggested fix for the warning"

  ValidationError:
    type: object
    required: [path, message]
    properties:
      path:
        type: string
        description: "JSON path where error occurred"
      message:
        type: string
        description: "Error message"
      code:
        type: string
        description: "Error code"
      expected:
        description: "Expected value or format"
      actual:
        description: "Actual value that caused error"

  ConflictResolution:
    type: object
    required: [strategy, description]
    properties:
      strategy:
        type: string
        enum: [use-spec, use-code, merge, custom]
      description:
        type: string
      preview:
        type: string
        description: "Preview of resolution result"
      confidence:
        type: number
        minimum: 0
        maximum: 1
        description: "Confidence in this resolution"

  Relationship:
    type: object
    required: [source, target, type]
    properties:
      source:
        type: string
        description: "Source component ID"
      target:
        type: string
        description: "Target component ID"
      type:
        type: string
        enum: [depends-on, extends, implements, uses, contains]
      metadata:
        type: object
        additionalProperties: true

  Constraint:
    type: object
    required: [type, expression]
    properties:
      type:
        type: string
        enum: [validation, business-rule, performance, security]
      expression:
        type: string
        description: "Constraint expression"
      message:
        type: string
        description: "Error message when constraint is violated"
      severity:
        type: string
        enum: [error, warning, info]

  Template:
    type: object
    required: [name, content]
    properties:
      name:
        type: string
        description: "Template name"
      content:
        type: string
        description: "Template content"
      variables:
        type: object
        additionalProperties: true
        description: "Template variables"
      metadata:
        type: object
        additionalProperties: true

  # Configuration Types
  CodeStyle:
    type: object
    properties:
      indentation:
        type: string
        enum: [spaces, tabs]
        default: spaces
      indentSize:
        type: integer
        minimum: 1
        maximum: 8
        default: 2
      lineEnding:
        type: string
        enum: [lf, crlf, cr]
        default: lf
      maxLineLength:
        type: integer
        minimum: 80
        maximum: 200
        default: 120
      semicolons:
        type: boolean
        default: true
      quotes:
        type: string
        enum: [single, double]
        default: single

  OptimizationOptions:
    type: object
    properties:
      minify:
        type: boolean
        default: false
      treeShaking:
        type: boolean
        default: true
      bundling:
        type: boolean
        default: false
      caching:
        type: boolean
        default: true

  ValidationOptions:
    type: object
    properties:
      strict:
        type: boolean
        default: false
      customRules:
        type: array
        items:
          type: string
      skipValidation:
        type: array
        items:
          type: string

  DocumentationOptions:
    type: object
    properties:
      includeExamples:
        type: boolean
        default: true
      includeSchemas:
        type: boolean
        default: true
      format:
        type: string
        enum: [markdown, html, pdf]
        default: markdown
      template:
        type: string
        description: "Documentation template to use"

  SyncOptions:
    type: object
    properties:
      dryRun:
        type: boolean
        default: false
        description: "Preview changes without applying them"
      interactive:
        type: boolean
        default: true
        description: "Prompt for conflict resolution"
      autoResolve:
        type: array
        items:
          type: string
        description: "Auto-resolve these conflict types"
      excludePaths:
        type: array
        items:
          type: string
        description: "Paths to exclude from sync"

  # Metadata Types
  ParseMetadata:
    type: object
    properties:
      parseTime:
        type: number
        description: "Parse time in milliseconds"
      originalFormat:
        type: string
        description: "Original specification format"
      resolvedReferences:
        type: integer
        description: "Number of references resolved"
      componentsFound:
        type: integer
        description: "Number of components identified"

  GenerationMetadata:
    type: object
    properties:
      generationTime:
        type: number
        description: "Generation time in milliseconds"
      filesGenerated:
        type: integer
        description: "Number of files generated"
      linesOfCode:
        type: integer
        description: "Total lines of code generated"
      templatesUsed:
        type: array
        items:
          type: string
        description: "Templates used for generation"

  SyncMetadata:
    type: object
    properties:
      syncTime:
        type: number
        description: "Synchronization time in milliseconds"
      changesApplied:
        type: integer
        description: "Number of changes applied"
      conflictsResolved:
        type: integer
        description: "Number of conflicts resolved"
      backupCreated:
        type: boolean
        description: "Whether backup was created"

# Error Types
errors:
  ParseError:
    type: object
    required: [code, message]
    properties:
      code:
        type: string
        enum: [INVALID_FORMAT, PARSE_FAILED, REFERENCE_ERROR]
      message:
        type: string
      details:
        type: object
        additionalProperties: true

  ValidationError:
    type: object
    required: [code, message, path]
    properties:
      code:
        type: string
        enum: [SCHEMA_VIOLATION, CONSTRAINT_VIOLATION, TYPE_MISMATCH]
      message:
        type: string
      path:
        type: string
      expected:
        description: "Expected value"
      actual:
        description: "Actual value"

  GenerationError:
    type: object
    required: [code, message]
    properties:
      code:
        type: string
        enum: [TEMPLATE_ERROR, OUTPUT_ERROR, DEPENDENCY_ERROR]
      message:
        type: string
      target:
        type: string
        description: "Generation target that failed"

  SyncError:
    type: object
    required: [code, message]
    properties:
      code:
        type: string
        enum: [CONFLICT_UNRESOLVED, BACKUP_FAILED, SYNC_FAILED]
      message:
        type: string
      conflicts:
        type: array
        items:
          $ref: "#/types/SyncConflict"

  ConflictError:
    type: object
    required: [code, message, conflicts]
    properties:
      code:
        type: string
        enum: [MERGE_CONFLICT, STRUCTURAL_CONFLICT, SEMANTIC_CONFLICT]
      message:
        type: string
      conflicts:
        type: array
        items:
          $ref: "#/types/SyncConflict"

  TransformError:
    type: object
    required: [code, message]
    properties:
      code:
        type: string
        enum: [UNSUPPORTED_FORMAT, CONVERSION_FAILED, DATA_LOSS]
      message:
        type: string
      sourceFormat:
        type: string
      targetFormat:
        type: string

  TemplateError:
    type: object
    required: [code, message]
    properties:
      code:
        type: string
        enum: [TEMPLATE_NOT_FOUND, TEMPLATE_SYNTAX_ERROR, VARIABLE_MISSING]
      message:
        type: string
      template:
        type: string
        description: "Template name that failed"

  FormatError:
    type: object
    required: [code, message]
    properties:
      code:
        type: string
        enum: [UNKNOWN_FORMAT, UNSUPPORTED_VERSION, FORMAT_MISMATCH]
      message:
        type: string
      format:
        type: string
        description: "Format that caused the error"
