# Spec Pragma Schema Definition
# Defines the validatable schemas for the Spec Pragma API
# Can be converted to GraphQL, OpenAPI, or other formats as needed

spec:
  name: "spec-pragma"
  version: "1.0.0"
  description: |
    The Spec Pragma provides transformation capabilities for converting industry-standard
    specifications (OpenAPI, JSON Schema, Gherkin) into TypeScript implementation code, tests,
    and documentation with bidirectional synchronization.

# Core Operations Schema
operations:
  /parse:
    post:
      summary: Parse specification input
      description: |
        Parses various specification formats and converts them into a unified semantic structure
        that can be processed by other pragma operations.
      operationId: parseSpecification
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ParseRequest'
      responses:
        '200':
          description: Successfully parsed specification
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ParseResponse'
        '400':
          description: Invalid specification format
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Specification validation failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'

  /generate:
    post:
      summary: Generate implementation code
      description: |
        Generates TypeScript implementation code, tests, and documentation from
        parsed semantic structures.
      operationId: generateImplementation
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GenerateRequest'
      responses:
        '200':
          description: Successfully generated implementation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenerateResponse'
        '400':
          description: Invalid generation request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /sync:
    post:
      summary: Synchronize specification and implementation
      description: |
        Maintains bidirectional synchronization between specifications and generated code,
        detecting conflicts and providing resolution strategies.
      operationId: synchronizeSpecAndCode
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SyncRequest'
      responses:
        '200':
          description: Successfully synchronized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SyncResponse'
        '409':
          description: Synchronization conflicts detected
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConflictResponse'

  /validate:
    post:
      summary: Validate specification
      description: |
        Validates specifications against their schemas and business rules,
        providing detailed error reports and suggestions.
      operationId: validateSpecification
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ValidateRequest'
      responses:
        '200':
          description: Validation completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidateResponse'

  /transform:
    post:
      summary: Transform between specification formats
      description: |
        Converts specifications between different formats (OpenAPI ↔ JSON Schema ↔ Gherkin)
        while preserving semantic meaning.
      operationId: transformSpecification
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TransformRequest'
      responses:
        '200':
          description: Successfully transformed specification
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TransformResponse'

components:
  schemas:
    # Parse Operation Schemas
    ParseRequest:
      type: object
      required:
        - input
        - format
      properties:
        input:
          oneOf:
            - type: string
              description: Specification content as string
            - type: object
              description: Specification content as parsed object
        format:
          type: string
          enum: [openapi, json-schema, gherkin, typescript, auto-detect]
          description: Input specification format
        options:
          $ref: '#/components/schemas/ParseOptions'
        context:
          $ref: '#/components/schemas/ParseContext'

    ParseOptions:
      type: object
      properties:
        strictMode:
          type: boolean
          default: false
          description: Enable strict parsing with enhanced validation
        preserveComments:
          type: boolean
          default: true
          description: Preserve comments and documentation
        resolveReferences:
          type: boolean
          default: true
          description: Resolve $ref and other references
        validateSchema:
          type: boolean
          default: true
          description: Validate against specification schema

    ParseContext:
      type: object
      properties:
        baseUrl:
          type: string
          description: Base URL for resolving relative references
        workingDirectory:
          type: string
          description: Working directory for file resolution
        environment:
          type: string
          enum: [development, staging, production]
          description: Target environment context
        metadata:
          type: object
          additionalProperties: true
          description: Additional context metadata

    ParseResponse:
      type: object
      required:
        - semanticStructure
        - metadata
      properties:
        semanticStructure:
          $ref: '#/components/schemas/SemanticStructure'
        metadata:
          $ref: '#/components/schemas/ParseMetadata'
        warnings:
          type: array
          items:
            $ref: '#/components/schemas/Warning'

    SemanticStructure:
      type: object
      required:
        - type
        - version
        - components
      properties:
        type:
          type: string
          enum: [api, schema, behavior, component, service]
          description: Type of specification
        version:
          type: string
          description: Semantic version of the structure
        components:
          type: array
          items:
            $ref: '#/components/schemas/Component'
        relationships:
          type: array
          items:
            $ref: '#/components/schemas/Relationship'
        constraints:
          type: array
          items:
            $ref: '#/components/schemas/Constraint'
        metadata:
          type: object
          additionalProperties: true

    Component:
      type: object
      required:
        - id
        - name
        - type
      properties:
        id:
          type: string
          description: Unique component identifier
        name:
          type: string
          description: Component name
        type:
          type: string
          enum: [endpoint, schema, operation, event, workflow, entity]
        properties:
          type: object
          additionalProperties: true
        children:
          type: array
          items:
            $ref: '#/components/schemas/Component'
        annotations:
          type: object
          additionalProperties: true

    # Generate Operation Schemas
    GenerateRequest:
      type: object
      required:
        - semanticStructure
        - targets
      properties:
        semanticStructure:
          $ref: '#/components/schemas/SemanticStructure'
        targets:
          type: array
          items:
            $ref: '#/components/schemas/GenerationTarget'
        options:
          $ref: '#/components/schemas/GenerationOptions'
        templates:
          type: array
          items:
            $ref: '#/components/schemas/Template'

    GenerationTarget:
      type: object
      required:
        - type
        - language
      properties:
        type:
          type: string
          enum: [implementation, tests, documentation, types, schemas]
        language:
          type: string
          enum: [typescript, javascript, python, java, csharp, go]
        framework:
          type: string
          enum: [react, vue, angular, express, fastapi, spring, dotnet]
        outputPath:
          type: string
          description: Output file or directory path
        options:
          type: object
          additionalProperties: true

    GenerationOptions:
      type: object
      properties:
        codeStyle:
          $ref: '#/components/schemas/CodeStyle'
        optimization:
          $ref: '#/components/schemas/OptimizationOptions'
        validation:
          $ref: '#/components/schemas/ValidationOptions'
        documentation:
          $ref: '#/components/schemas/DocumentationOptions'

    CodeStyle:
      type: object
      properties:
        indentation:
          type: string
          enum: [spaces, tabs]
          default: spaces
        indentSize:
          type: integer
          minimum: 1
          maximum: 8
          default: 2
        lineEnding:
          type: string
          enum: [lf, crlf, cr]
          default: lf
        maxLineLength:
          type: integer
          minimum: 80
          maximum: 200
          default: 120
        semicolons:
          type: boolean
          default: true
        quotes:
          type: string
          enum: [single, double]
          default: single

    GenerateResponse:
      type: object
      required:
        - files
        - metadata
      properties:
        files:
          type: array
          items:
            $ref: '#/components/schemas/GeneratedFile'
        metadata:
          $ref: '#/components/schemas/GenerationMetadata'
        warnings:
          type: array
          items:
            $ref: '#/components/schemas/Warning'

    GeneratedFile:
      type: object
      required:
        - path
        - content
        - type
      properties:
        path:
          type: string
          description: File path relative to output directory
        content:
          type: string
          description: Generated file content
        type:
          type: string
          enum: [implementation, test, documentation, type-definition, schema]
        language:
          type: string
          description: Programming language of the content
        encoding:
          type: string
          default: utf-8
        metadata:
          type: object
          additionalProperties: true

    # Sync Operation Schemas
    SyncRequest:
      type: object
      required:
        - specification
        - implementation
      properties:
        specification:
          $ref: '#/components/schemas/SpecificationSource'
        implementation:
          $ref: '#/components/schemas/ImplementationSource'
        strategy:
          $ref: '#/components/schemas/SyncStrategy'
        options:
          $ref: '#/components/schemas/SyncOptions'

    SpecificationSource:
      type: object
      required:
        - content
        - format
      properties:
        content:
          type: string
          description: Specification content
        format:
          type: string
          enum: [openapi, json-schema, gherkin]
        lastModified:
          type: string
          format: date-time
        checksum:
          type: string
          description: Content checksum for change detection

    ImplementationSource:
      type: object
      required:
        - files
      properties:
        files:
          type: array
          items:
            $ref: '#/components/schemas/ImplementationFile'
        metadata:
          type: object
          additionalProperties: true

    ImplementationFile:
      type: object
      required:
        - path
        - content
      properties:
        path:
          type: string
        content:
          type: string
        lastModified:
          type: string
          format: date-time
        checksum:
          type: string

    SyncStrategy:
      type: object
      properties:
        direction:
          type: string
          enum: [spec-to-code, code-to-spec, bidirectional]
          default: bidirectional
        conflictResolution:
          type: string
          enum: [manual, spec-wins, code-wins, merge, interactive]
          default: interactive
        preserveCustomizations:
          type: boolean
          default: true
        backupBeforeSync:
          type: boolean
          default: true

    SyncResponse:
      type: object
      required:
        - status
        - changes
      properties:
        status:
          type: string
          enum: [success, conflicts, failed]
        changes:
          type: array
          items:
            $ref: '#/components/schemas/SyncChange'
        conflicts:
          type: array
          items:
            $ref: '#/components/schemas/SyncConflict'
        metadata:
          $ref: '#/components/schemas/SyncMetadata'

    SyncChange:
      type: object
      required:
        - type
        - target
        - description
      properties:
        type:
          type: string
          enum: [added, modified, deleted, moved]
        target:
          type: string
          enum: [specification, implementation]
        path:
          type: string
          description: Path of changed item
        description:
          type: string
          description: Human-readable change description
        diff:
          type: string
          description: Detailed diff of the change

    SyncConflict:
      type: object
      required:
        - type
        - path
        - description
      properties:
        type:
          type: string
          enum: [structural, semantic, naming, type-mismatch]
        path:
          type: string
          description: Path where conflict occurred
        description:
          type: string
          description: Conflict description
        specValue:
          description: Value from specification
        codeValue:
          description: Value from implementation
        suggestions:
          type: array
          items:
            $ref: '#/components/schemas/ConflictResolution'

    ConflictResolution:
      type: object
      required:
        - strategy
        - description
      properties:
        strategy:
          type: string
          enum: [use-spec, use-code, merge, custom]
        description:
          type: string
        preview:
          type: string
          description: Preview of resolution result
        confidence:
          type: number
          minimum: 0
          maximum: 1
          description: Confidence in this resolution

    # Common Schemas
    Warning:
      type: object
      required:
        - code
        - message
      properties:
        code:
          type: string
          description: Warning code for programmatic handling
        message:
          type: string
          description: Human-readable warning message
        severity:
          type: string
          enum: [info, warning, error]
          default: warning
        path:
          type: string
          description: Path where warning occurred
        suggestion:
          type: string
          description: Suggested fix for the warning

    ErrorResponse:
      type: object
      required:
        - error
        - message
      properties:
        error:
          type: string
          description: Error code
        message:
          type: string
          description: Error message
        details:
          type: object
          additionalProperties: true
        timestamp:
          type: string
          format: date-time

    ValidationErrorResponse:
      type: object
      required:
        - errors
      properties:
        errors:
          type: array
          items:
            $ref: '#/components/schemas/ValidationError'
        warnings:
          type: array
          items:
            $ref: '#/components/schemas/Warning'

    ValidationError:
      type: object
      required:
        - path
        - message
      properties:
        path:
          type: string
          description: JSON path where error occurred
        message:
          type: string
          description: Error message
        code:
          type: string
          description: Error code
        expected:
          description: Expected value or format
        actual:
          description: Actual value that caused error

    # Metadata Schemas
    ParseMetadata:
      type: object
      properties:
        parseTime:
          type: number
          description: Parse time in milliseconds
        originalFormat:
          type: string
          description: Original specification format
        resolvedReferences:
          type: integer
          description: Number of references resolved
        componentsFound:
          type: integer
          description: Number of components identified

    GenerationMetadata:
      type: object
      properties:
        generationTime:
          type: number
          description: Generation time in milliseconds
        filesGenerated:
          type: integer
          description: Number of files generated
        linesOfCode:
          type: integer
          description: Total lines of code generated
        templatesUsed:
          type: array
          items:
            type: string
          description: Templates used for generation

    SyncMetadata:
      type: object
      properties:
        syncTime:
          type: number
          description: Synchronization time in milliseconds
        changesApplied:
          type: integer
          description: Number of changes applied
        conflictsResolved:
          type: integer
          description: Number of conflicts resolved
        backupCreated:
          type: boolean
          description: Whether backup was created

    # Additional Schemas
    Relationship:
      type: object
      required:
        - source
        - target
        - type
      properties:
        source:
          type: string
          description: Source component ID
        target:
          type: string
          description: Target component ID
        type:
          type: string
          enum: [depends-on, extends, implements, uses, contains]
        metadata:
          type: object
          additionalProperties: true

    Constraint:
      type: object
      required:
        - type
        - expression
      properties:
        type:
          type: string
          enum: [validation, business-rule, performance, security]
        expression:
          type: string
          description: Constraint expression
        message:
          type: string
          description: Error message when constraint is violated
        severity:
          type: string
          enum: [error, warning, info]

    Template:
      type: object
      required:
        - name
        - content
      properties:
        name:
          type: string
          description: Template name
        content:
          type: string
          description: Template content
        variables:
          type: object
          additionalProperties: true
          description: Template variables
        metadata:
          type: object
          additionalProperties: true

    OptimizationOptions:
      type: object
      properties:
        minify:
          type: boolean
          default: false
        treeShaking:
          type: boolean
          default: true
        bundling:
          type: boolean
          default: false
        caching:
          type: boolean
          default: true

    ValidationOptions:
      type: object
      properties:
        strict:
          type: boolean
          default: false
        customRules:
          type: array
          items:
            type: string
        skipValidation:
          type: array
          items:
            type: string

    DocumentationOptions:
      type: object
      properties:
        includeExamples:
          type: boolean
          default: true
        includeSchemas:
          type: boolean
          default: true
        format:
          type: string
          enum: [markdown, html, pdf]
          default: markdown
        template:
          type: string
          description: Documentation template to use

    ValidateRequest:
      type: object
      required:
        - input
      properties:
        input:
          oneOf:
            - type: string
            - type: object
        format:
          type: string
          enum: [openapi, json-schema, gherkin, auto-detect]
        rules:
          type: array
          items:
            type: string
        options:
          $ref: '#/components/schemas/ValidationOptions'

    ValidateResponse:
      type: object
      required:
        - valid
      properties:
        valid:
          type: boolean
        errors:
          type: array
          items:
            $ref: '#/components/schemas/ValidationError'
        warnings:
          type: array
          items:
            $ref: '#/components/schemas/Warning'
        metadata:
          type: object
          additionalProperties: true

    TransformRequest:
      type: object
      required:
        - input
        - sourceFormat
        - targetFormat
      properties:
        input:
          oneOf:
            - type: string
            - type: object
        sourceFormat:
          type: string
          enum: [openapi, json-schema, gherkin]
        targetFormat:
          type: string
          enum: [openapi, json-schema, gherkin]
        options:
          type: object
          additionalProperties: true

    TransformResponse:
      type: object
      required:
        - output
        - metadata
      properties:
        output:
          oneOf:
            - type: string
            - type: object
        metadata:
          type: object
          additionalProperties: true
        warnings:
          type: array
          items:
            $ref: '#/components/schemas/Warning'

    ConflictResponse:
      type: object
      required:
        - conflicts
      properties:
        conflicts:
          type: array
          items:
            $ref: '#/components/schemas/SyncConflict'
        partialSync:
          type: boolean
          description: Whether partial sync was applied
        rollbackAvailable:
          type: boolean
          description: Whether rollback is available

    SyncOptions:
      type: object
      properties:
        dryRun:
          type: boolean
          default: false
          description: Preview changes without applying them
        interactive:
          type: boolean
          default: true
          description: Prompt for conflict resolution
        autoResolve:
          type: array
          items:
            type: string
          description: Auto-resolve these conflict types
        excludePaths:
          type: array
          items:
            type: string
          description: Paths to exclude from sync
