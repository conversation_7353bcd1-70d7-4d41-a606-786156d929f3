# Spec Pragma Examples

## Basic Component Specification

### Natural Language Format
```
# User Profile Component

The component displays user information including name, email, and avatar.
It accepts a user object as props with name, email, and avatarUrl properties.
When the user clicks the avatar, it should trigger an onAvatarClick callback.
The component should be memoized for performance.
It should handle loading and error states gracefully.
```

### Structured Format
```yaml
# User Profile Component

props:
  user: object with name, email, avatarUrl
  onAvatarClick: function
  loading: boolean = false
  error: string | null = null

behavior:
  - displays user name and email
  - shows avatar image from avatarUrl
  - calls onAvatarClick when avatar clicked
  - shows loading spinner when loading is true
  - shows error message when error exists

performance:
  - memoized component
  - only re-renders when props change
```

### Minimal Format
```
user:object, onAvatarClick:function, loading:boolean, error:string|null
-> displays user info, handles avatar click, loading/error states
-> memoized
```

## Generated Implementation
```typescript
import React, { memo } from 'react';

interface UserProfileProps {
  user: {
    name: string;
    email: string;
    avatarUrl: string;
  };
  onAvatarClick: () => void;
  loading?: boolean;
  error?: string | null;
}

const UserProfile = memo<UserProfileProps>(({ 
  user, 
  onAvatarClick, 
  loading = false, 
  error = null 
}) => {
  if (loading) {
    return <div className="loading-spinner">Loading...</div>;
  }
  
  if (error) {
    return <div className="error-message">{error}</div>;
  }
  
  return (
    <div className="user-profile">
      <img 
        src={user.avatarUrl} 
        alt={user.name}
        onClick={onAvatarClick}
        className="avatar"
      />
      <div className="user-info">
        <h3>{user.name}</h3>
        <p>{user.email}</p>
      </div>
    </div>
  );
});

export default UserProfile;
```

## Generated Tests
```typescript
import { render, fireEvent, screen } from '@testing-library/react';
import UserProfile from './UserProfile';

describe('UserProfile', () => {
  const mockUser = {
    name: 'John Doe',
    email: '<EMAIL>',
    avatarUrl: 'https://example.com/avatar.jpg'
  };
  
  const mockOnAvatarClick = jest.fn();
  
  beforeEach(() => {
    mockOnAvatarClick.mockClear();
  });
  
  it('displays user name and email', () => {
    render(<UserProfile user={mockUser} onAvatarClick={mockOnAvatarClick} />);
    
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });
  
  it('shows avatar image from avatarUrl', () => {
    render(<UserProfile user={mockUser} onAvatarClick={mockOnAvatarClick} />);
    
    const avatar = screen.getByAltText('John Doe');
    expect(avatar).toHaveAttribute('src', 'https://example.com/avatar.jpg');
  });
  
  it('calls onAvatarClick when avatar clicked', () => {
    render(<UserProfile user={mockUser} onAvatarClick={mockOnAvatarClick} />);
    
    const avatar = screen.getByAltText('John Doe');
    fireEvent.click(avatar);
    
    expect(mockOnAvatarClick).toHaveBeenCalledTimes(1);
  });
  
  it('shows loading spinner when loading is true', () => {
    render(<UserProfile user={mockUser} onAvatarClick={mockOnAvatarClick} loading={true} />);
    
    expect(screen.getByText('Loading...')).toBeInTheDocument();
    expect(screen.queryByText('John Doe')).not.toBeInTheDocument();
  });
  
  it('shows error message when error exists', () => {
    render(<UserProfile user={mockUser} onAvatarClick={mockOnAvatarClick} error="Something went wrong" />);
    
    expect(screen.getByText('Something went wrong')).toBeInTheDocument();
    expect(screen.queryByText('John Doe')).not.toBeInTheDocument();
  });
  
  it('is memoized and only re-renders when props change', () => {
    const { rerender } = render(<UserProfile user={mockUser} onAvatarClick={mockOnAvatarClick} />);
    
    // Re-render with same props should not cause re-render
    rerender(<UserProfile user={mockUser} onAvatarClick={mockOnAvatarClick} />);
    
    // Re-render with different props should cause re-render
    const newUser = { ...mockUser, name: 'Jane Doe' };
    rerender(<UserProfile user={newUser} onAvatarClick={mockOnAvatarClick} />);
    
    expect(screen.getByText('Jane Doe')).toBeInTheDocument();
  });
});
```

## Complex Component Example

### Gherkin BDD Format
```gherkin
Feature: Shopping Cart Component

  Scenario: Adding items to cart
    Given the shopping cart is empty
    When the user adds an item with quantity 2
    Then the cart should show 1 item type with quantity 2
    And the total should be calculated correctly

  Scenario: Removing items from cart
    Given the cart has items
    When the user removes an item
    Then the item should be removed from the cart
    And the total should be recalculated

  Scenario: Updating item quantities
    Given the cart has items
    When the user changes an item quantity
    Then the cart should update the quantity
    And the total should be recalculated
```

### Generated Implementation with State Management
```typescript
import React, { memo, useCallback, useMemo } from 'react';
import { useCart } from '../hooks/useCart';

interface ShoppingCartProps {
  onCheckout: (items: CartItem[]) => void;
  currency?: string;
}

interface CartItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
}

const ShoppingCart = memo<ShoppingCartProps>(({ 
  onCheckout, 
  currency = 'USD' 
}) => {
  const { items, addItem, removeItem, updateQuantity, total } = useCart();
  
  const handleQuantityChange = useCallback((id: string, quantity: number) => {
    if (quantity <= 0) {
      removeItem(id);
    } else {
      updateQuantity(id, quantity);
    }
  }, [removeItem, updateQuantity]);
  
  const handleCheckout = useCallback(() => {
    onCheckout(items);
  }, [items, onCheckout]);
  
  const formattedTotal = useMemo(() => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency
    }).format(total);
  }, [total, currency]);
  
  if (items.length === 0) {
    return (
      <div className="shopping-cart empty">
        <p>Your cart is empty</p>
      </div>
    );
  }
  
  return (
    <div className="shopping-cart">
      <div className="cart-items">
        {items.map(item => (
          <div key={item.id} className="cart-item">
            <span className="item-name">{item.name}</span>
            <input
              type="number"
              value={item.quantity}
              onChange={(e) => handleQuantityChange(item.id, parseInt(e.target.value))}
              min="0"
            />
            <span className="item-price">{item.price}</span>
            <button onClick={() => removeItem(item.id)}>Remove</button>
          </div>
        ))}
      </div>
      <div className="cart-total">
        <strong>Total: {formattedTotal}</strong>
      </div>
      <button onClick={handleCheckout} className="checkout-button">
        Checkout
      </button>
    </div>
  );
});

export default ShoppingCart;
```

These examples demonstrate the spec pragma's ability to transform various specification formats into complete, production-ready implementations with comprehensive test suites.
