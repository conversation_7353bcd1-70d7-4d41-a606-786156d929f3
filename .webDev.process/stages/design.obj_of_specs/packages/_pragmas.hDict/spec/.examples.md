# Spec Pragma Examples

## OpenAPI 3.0 Component Specification

### Component Schema Definition
```yaml
# userprofile.openapi.yaml
openapi: 3.0.3
info:
  title: User Profile Component
  version: 1.0.0
  description: React component for displaying user profile information

components:
  schemas:
    User:
      type: object
      required:
        - name
        - email
        - avatarUrl
      properties:
        name:
          type: string
          description: User's full name
        email:
          type: string
          format: email
          description: User's email address
        avatarUrl:
          type: string
          format: uri
          description: URL to user's avatar image

    UserProfileProps:
      type: object
      required:
        - user
        - onAvatarClick
      properties:
        user:
          $ref: '#/components/schemas/User'
        onAvatarClick:
          type: string
          format: callback
          description: Callback function when avatar is clicked
        loading:
          type: boolean
          default: false
          description: Loading state indicator
        error:
          type: string
          nullable: true
          description: Error message to display
```

### JSON Schema Definition
```json
{
  "$schema": "https://json-schema.org/draft/2020-12/schema",
  "$id": "https://example.com/userprofile.schema.json",
  "title": "UserProfile Component Props",
  "type": "object",
  "properties": {
    "user": {
      "type": "object",
      "properties": {
        "name": {
          "type": "string",
          "description": "User's full name"
        },
        "email": {
          "type": "string",
          "format": "email",
          "description": "User's email address"
        },
        "avatarUrl": {
          "type": "string",
          "format": "uri",
          "description": "URL to user's avatar image"
        }
      },
      "required": ["name", "email", "avatarUrl"]
    },
    "onAvatarClick": {
      "type": "function",
      "description": "Callback function when avatar is clicked"
    },
    "loading": {
      "type": "boolean",
      "default": false,
      "description": "Loading state indicator"
    },
    "error": {
      "type": ["string", "null"],
      "description": "Error message to display"
    }
  },
  "required": ["user", "onAvatarClick"]
}
```

### Gherkin BDD Specification
```gherkin
# userprofile.feature
Feature: User Profile Component
  As a user
  I want to see my profile information
  So that I can verify my account details

  Background:
    Given a user with the following details:
      | name     | email           | avatarUrl                    |
      | John Doe | <EMAIL> | https://example.com/john.jpg |

  Scenario: Display user information
    When the UserProfile component renders
    Then I should see "John Doe" as the user name
    And I should see "<EMAIL>" as the email
    And I should see an avatar image with src "https://example.com/john.jpg"

  Scenario: Handle avatar click interaction
    Given the UserProfile component is rendered
    When I click on the avatar image
    Then the onAvatarClick callback should be triggered

  Scenario: Display loading state
    Given the loading prop is true
    When the UserProfile component renders
    Then I should see a loading indicator
    And I should not see user information

  Scenario: Display error state
    Given the error prop is "Failed to load user"
    When the UserProfile component renders
    Then I should see "Failed to load user" as an error message
    And I should not see user information
```

## Generated Implementation
```typescript
import React, { memo } from 'react';

interface UserProfileProps {
  user: {
    name: string;
    email: string;
    avatarUrl: string;
  };
  onAvatarClick: () => void;
  loading?: boolean;
  error?: string | null;
}

const UserProfile = memo<UserProfileProps>(({
  user,
  onAvatarClick,
  loading = false,
  error = null
}) => {
  if (loading) {
    return <div className="loading-spinner">Loading...</div>;
  }

  if (error) {
    return <div className="error-message">{error}</div>;
  }

  return (
    <div className="user-profile">
      <img
        src={user.avatarUrl}
        alt={user.name}
        onClick={onAvatarClick}
        className="avatar"
      />
      <div className="user-info">
        <h3>{user.name}</h3>
        <p>{user.email}</p>
      </div>
    </div>
  );
});

export default UserProfile;
```

## Generated Tests
```typescript
import { render, fireEvent, screen } from '@testing-library/react';
import UserProfile from './UserProfile';

describe('UserProfile', () => {
  const mockUser = {
    name: 'John Doe',
    email: '<EMAIL>',
    avatarUrl: 'https://example.com/avatar.jpg'
  };

  const mockOnAvatarClick = jest.fn();

  beforeEach(() => {
    mockOnAvatarClick.mockClear();
  });

  it('displays user name and email', () => {
    render(<UserProfile user={mockUser} onAvatarClick={mockOnAvatarClick} />);

    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('shows avatar image from avatarUrl', () => {
    render(<UserProfile user={mockUser} onAvatarClick={mockOnAvatarClick} />);

    const avatar = screen.getByAltText('John Doe');
    expect(avatar).toHaveAttribute('src', 'https://example.com/avatar.jpg');
  });

  it('calls onAvatarClick when avatar clicked', () => {
    render(<UserProfile user={mockUser} onAvatarClick={mockOnAvatarClick} />);

    const avatar = screen.getByAltText('John Doe');
    fireEvent.click(avatar);

    expect(mockOnAvatarClick).toHaveBeenCalledTimes(1);
  });

  it('shows loading spinner when loading is true', () => {
    render(<UserProfile user={mockUser} onAvatarClick={mockOnAvatarClick} loading={true} />);

    expect(screen.getByText('Loading...')).toBeInTheDocument();
    expect(screen.queryByText('John Doe')).not.toBeInTheDocument();
  });

  it('shows error message when error exists', () => {
    render(<UserProfile user={mockUser} onAvatarClick={mockOnAvatarClick} error="Something went wrong" />);

    expect(screen.getByText('Something went wrong')).toBeInTheDocument();
    expect(screen.queryByText('John Doe')).not.toBeInTheDocument();
  });

  it('is memoized and only re-renders when props change', () => {
    const { rerender } = render(<UserProfile user={mockUser} onAvatarClick={mockOnAvatarClick} />);

    // Re-render with same props should not cause re-render
    rerender(<UserProfile user={mockUser} onAvatarClick={mockOnAvatarClick} />);

    // Re-render with different props should cause re-render
    const newUser = { ...mockUser, name: 'Jane Doe' };
    rerender(<UserProfile user={newUser} onAvatarClick={mockOnAvatarClick} />);

    expect(screen.getByText('Jane Doe')).toBeInTheDocument();
  });
});
```

## Complex Component Example

### Gherkin BDD Format
```gherkin
Feature: Shopping Cart Component

  Scenario: Adding items to cart
    Given the shopping cart is empty
    When the user adds an item with quantity 2
    Then the cart should show 1 item type with quantity 2
    And the total should be calculated correctly

  Scenario: Removing items from cart
    Given the cart has items
    When the user removes an item
    Then the item should be removed from the cart
    And the total should be recalculated

  Scenario: Updating item quantities
    Given the cart has items
    When the user changes an item quantity
    Then the cart should update the quantity
    And the total should be recalculated
```

### Generated Implementation with State Management
```typescript
import React, { memo, useCallback, useMemo } from 'react';
import { useCart } from '../hooks/useCart';

interface ShoppingCartProps {
  onCheckout: (items: CartItem[]) => void;
  currency?: string;
}

interface CartItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
}

const ShoppingCart = memo<ShoppingCartProps>(({
  onCheckout,
  currency = 'USD'
}) => {
  const { items, addItem, removeItem, updateQuantity, total } = useCart();

  const handleQuantityChange = useCallback((id: string, quantity: number) => {
    if (quantity <= 0) {
      removeItem(id);
    } else {
      updateQuantity(id, quantity);
    }
  }, [removeItem, updateQuantity]);

  const handleCheckout = useCallback(() => {
    onCheckout(items);
  }, [items, onCheckout]);

  const formattedTotal = useMemo(() => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency
    }).format(total);
  }, [total, currency]);

  if (items.length === 0) {
    return (
      <div className="shopping-cart empty">
        <p>Your cart is empty</p>
      </div>
    );
  }

  return (
    <div className="shopping-cart">
      <div className="cart-items">
        {items.map(item => (
          <div key={item.id} className="cart-item">
            <span className="item-name">{item.name}</span>
            <input
              type="number"
              value={item.quantity}
              onChange={(e) => handleQuantityChange(item.id, parseInt(e.target.value))}
              min="0"
            />
            <span className="item-price">{item.price}</span>
            <button onClick={() => removeItem(item.id)}>Remove</button>
          </div>
        ))}
      </div>
      <div className="cart-total">
        <strong>Total: {formattedTotal}</strong>
      </div>
      <button onClick={handleCheckout} className="checkout-button">
        Checkout
      </button>
    </div>
  );
});

export default ShoppingCart;
```

These examples demonstrate the spec pragma's ability to transform various specification formats into complete, production-ready implementations with comprehensive test suites.
