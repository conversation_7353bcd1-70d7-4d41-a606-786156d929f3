openapi: 3.0.3
info:
  title: Spec Pragma API
  version: 1.0.0
  description: Specification transformation pragma - extends ST Pragma for converting specs to implementation code

paths:
  /parse:
    post:
      summary: Parse specification
      operationId: parseSpecification
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ParseRequest'
      responses:
        '200':
          description: Specification parsed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ParseResponse'
        '400':
          description: Parse error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /generate:
    post:
      summary: Generate code from semantics
      operationId: generateCode
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GenerateRequest'
      responses:
        '200':
          description: Code generated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenerateResponse'

  /sync:
    post:
      summary: Synchronize specification and implementation
      operationId: syncSpecification
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SyncRequest'
      responses:
        '200':
          description: Synchronization completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SyncResponse'

  /formats:
    get:
      summary: List supported specification formats
      operationId: listSupportedFormats
      responses:
        '200':
          description: List of supported formats
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SpecificationFormat'

  /templates:
    get:
      summary: List available templates
      operationId: listTemplates
      parameters:
        - name: target
          in: query
          schema:
            type: string
            enum: [typescript, vitest, storybook]
      responses:
        '200':
          description: List of templates
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/TemplateInfo'

components:
  schemas:
    # Parse API
    ParseRequest:
      type: object
      required:
        - specText
        - format
      properties:
        specText:
          type: string
          description: Raw specification text
        format:
          type: string
          enum: [openapi, json-schema, gherkin]
          description: Specification format
        options:
          $ref: '#/components/schemas/ParseOptions'
        context:
          $ref: '#/components/schemas/ParseContext'

    ParseOptions:
      type: object
      properties:
        strictValidation:
          type: boolean
          default: true
        allowExtensions:
          type: boolean
          default: true
        validateExamples:
          type: boolean
          default: true
        customValidators:
          type: array
          items:
            type: string

    ParseContext:
      type: object
      properties:
        sourceFile:
          type: string
        encoding:
          type: string
          default: utf-8
        projectConfig:
          type: object
          additionalProperties: true
        environment:
          type: string
          enum: [development, staging, production]

    ParseResponse:
      type: object
      required:
        - semantics
        - metadata
      properties:
        semantics:
          $ref: '#/components/schemas/SemanticStructure'
        metadata:
          $ref: '#/components/schemas/ParseMetadata'
        warnings:
          type: array
          items:
            type: string

    SemanticStructure:
      type: object
      required:
        - type
        - version
      properties:
        type:
          type: string
          enum: [component, service, api, test-suite]
        version:
          type: string
        description:
          type: string
        components:
          type: array
          items:
            $ref: '#/components/schemas/ComponentSpec'
        types:
          type: array
          items:
            $ref: '#/components/schemas/TypeDefinition'
        behaviors:
          type: array
          items:
            $ref: '#/components/schemas/BehaviorSpec'
        dependencies:
          type: array
          items:
            $ref: '#/components/schemas/DependencySpec'

    ComponentSpec:
      type: object
      required:
        - name
        - type
      properties:
        name:
          type: string
        type:
          type: string
          enum: [react-component, hook, provider, utility]
        props:
          type: array
          items:
            $ref: '#/components/schemas/PropSpec'
        events:
          type: array
          items:
            $ref: '#/components/schemas/EventSpec'
        lifecycle:
          type: array
          items:
            type: string

    PropSpec:
      type: object
      required:
        - name
        - type
      properties:
        name:
          type: string
        type:
          type: string
        required:
          type: boolean
          default: false
        default:
          description: Default value
        description:
          type: string
        validation:
          $ref: '#/components/schemas/ValidationRule'

    EventSpec:
      type: object
      required:
        - name
        - trigger
      properties:
        name:
          type: string
        trigger:
          type: string
        payload:
          type: object
          additionalProperties: true
        bubbles:
          type: boolean
          default: false

    BehaviorSpec:
      type: object
      required:
        - description
        - type
      properties:
        description:
          type: string
        type:
          type: string
          enum: [given, when, then, and, but]
        condition:
          type: string
        action:
          type: string
        expected:
          type: string
        testable:
          type: boolean
          default: true

    TypeDefinition:
      type: object
      required:
        - name
        - definition
      properties:
        name:
          type: string
        definition:
          type: object
          additionalProperties: true
        exported:
          type: boolean
          default: true
        generic:
          type: boolean
          default: false

    DependencySpec:
      type: object
      required:
        - name
        - type
      properties:
        name:
          type: string
        type:
          type: string
          enum: [pragma, package, service, external]
        version:
          type: string
        optional:
          type: boolean
          default: false

    ParseMetadata:
      type: object
      properties:
        parseTime:
          type: number
        format:
          type: string
        statistics:
          type: object
          properties:
            components:
              type: number
            types:
              type: number
            behaviors:
              type: number
            linesOfSpec:
              type: number

    ValidationRule:
      type: object
      properties:
        pattern:
          type: string
        min:
          type: number
        max:
          type: number
        custom:
          type: string

    # Generate API
    GenerateRequest:
      type: object
      required:
        - semantics
        - target
      properties:
        semantics:
          $ref: '#/components/schemas/SemanticStructure'
        target:
          type: string
          enum: [typescript, vitest, storybook, documentation]
        templates:
          type: object
          additionalProperties:
            type: string
        options:
          $ref: '#/components/schemas/GenerateOptions'

    GenerateOptions:
      type: object
      properties:
        outputDirectory:
          type: string
        fileNaming:
          $ref: '#/components/schemas/FileNamingOptions'
        formatting:
          $ref: '#/components/schemas/FormattingOptions'
        optimization:
          $ref: '#/components/schemas/OptimizationOptions'

    FileNamingOptions:
      type: object
      properties:
        component:
          type: string
          default: '{name}.tsx'
        test:
          type: string
          default: '{name}.test.tsx'
        story:
          type: string
          default: '{name}.stories.tsx'
        types:
          type: string
          default: '{name}.types.ts'

    FormattingOptions:
      type: object
      properties:
        prettier:
          type: boolean
          default: true
        eslint:
          type: boolean
          default: true
        organizeImports:
          type: boolean
          default: true

    OptimizationOptions:
      type: object
      properties:
        treeshaking:
          type: boolean
          default: true
        memoization:
          type: boolean
          default: true
        bundleAnalysis:
          type: boolean
          default: false

    GenerateResponse:
      type: object
      required:
        - files
        - metadata
      properties:
        files:
          type: array
          items:
            $ref: '#/components/schemas/GeneratedFile'
        metadata:
          $ref: '#/components/schemas/GenerateMetadata'

    GeneratedFile:
      type: object
      required:
        - path
        - content
        - type
      properties:
        path:
          type: string
        content:
          type: string
        type:
          type: string
          enum: [typescript, test, story, documentation, configuration]
        size:
          type: number
        checksum:
          type: string

    GenerateMetadata:
      type: object
      properties:
        generateTime:
          type: number
        target:
          type: string
        templatesUsed:
          type: array
          items:
            type: string
        statistics:
          type: object
          properties:
            filesGenerated:
              type: number
            linesOfCode:
              type: number
            testCoverage:
              type: number

    # Sync API
    SyncRequest:
      type: object
      required:
        - specification
        - implementation
      properties:
        specification:
          type: string
        implementation:
          type: string
        direction:
          type: string
          enum: [spec-to-code, code-to-spec, bidirectional]
          default: bidirectional
        options:
          $ref: '#/components/schemas/SyncOptions'

    SyncOptions:
      type: object
      properties:
        preserveCustomCode:
          type: boolean
          default: true
        conflictResolution:
          type: string
          enum: [manual, spec-wins, code-wins, merge]
          default: manual
        backupBeforeSync:
          type: boolean
          default: true
        dryRun:
          type: boolean
          default: false

    SyncResponse:
      type: object
      required:
        - result
        - metadata
      properties:
        result:
          $ref: '#/components/schemas/SyncResult'
        conflicts:
          type: array
          items:
            $ref: '#/components/schemas/ConflictReport'
        metadata:
          $ref: '#/components/schemas/SyncMetadata'

    SyncResult:
      type: object
      properties:
        updatedSpecification:
          type: string
        updatedImplementation:
          type: string
        changesSummary:
          $ref: '#/components/schemas/ChangesSummary'
        success:
          type: boolean

    ConflictReport:
      type: object
      required:
        - type
        - location
        - description
      properties:
        type:
          type: string
          enum: [property, method, structure, type]
        location:
          type: string
        description:
          type: string
        specValue:
          description: Value in specification
        codeValue:
          description: Value in implementation
        resolution:
          type: string
          enum: [manual, spec-wins, code-wins, merged]
        suggestions:
          type: array
          items:
            type: string

    ChangesSummary:
      type: object
      properties:
        added:
          type: array
          items:
            type: string
        modified:
          type: array
          items:
            type: string
        removed:
          type: array
          items:
            type: string
        conflicts:
          type: number

    SyncMetadata:
      type: object
      properties:
        syncTime:
          type: number
        direction:
          type: string
        conflictsResolved:
          type: number
        backupCreated:
          type: boolean

    # Supporting Types
    SpecificationFormat:
      type: object
      required:
        - name
        - version
        - extensions
      properties:
        name:
          type: string
        version:
          type: string
        description:
          type: string
        extensions:
          type: array
          items:
            type: string
        mimeTypes:
          type: array
          items:
            type: string
        features:
          type: array
          items:
            type: string

    TemplateInfo:
      type: object
      required:
        - name
        - target
        - version
      properties:
        name:
          type: string
        target:
          type: string
        version:
          type: string
        description:
          type: string
        variables:
          type: array
          items:
            type: string
        customizable:
          type: boolean

    ErrorResponse:
      type: object
      required:
        - error
        - message
      properties:
        error:
          type: string
        message:
          type: string
        code:
          type: string
        details:
          type: object
          additionalProperties: true
        suggestions:
          type: array
          items:
            type: string
