# Required Dependencies for Spec Pragma

## Dependencies to Add to Root package.json

### Production Dependencies
```json
{
  "@apidevtools/swagger-parser": "10.1.0",
  "@cucumber/gherkin": "27.0.0", 
  "ajv": "8.12.0",
  "ajv-formats": "2.1.1",
  "openapi-types": "12.1.3",
  "json-schema": "0.4.0"
}
```

### Development Dependencies  
```json
{
  "@types/json-schema": "7.0.15"
}
```

### Existing Dependencies (Already Available)
These dependencies are already in the root package.json and will be reused:

- `typescript`: "5.7.2" ✅
- `vitest`: "2.1.8" ✅  
- `@vitest/coverage-v8`: "2.1.8" ✅
- `eslint`: "9.17.0" ✅
- `@typescript-eslint/eslint-plugin`: "8.19.0" ✅
- `@typescript-eslint/parser`: "8.19.0" ✅
- `@types/node`: "22.10.5" ✅
- `react`: "18.2.0" ✅
- `@testing-library/react`: "16.1.0" ✅

## PNPM Overrides to Add

Add these to the `pnpm.overrides` section in root package.json:

```json
{
  "@apidevtools/swagger-parser": "10.1.0",
  "@cucumber/gherkin": "27.0.0",
  "ajv": "8.12.0",
  "ajv-formats": "2.1.1", 
  "openapi-types": "12.1.3",
  "json-schema": "0.4.0",
  "@types/json-schema": "7.0.15"
}
```

## Rationale

### Locked Versions
All versions are locked (no `^` or `~`) to ensure reproducible builds and prevent unexpected breaking changes.

### Hoisted Dependencies
Dependencies are managed at the root level to:
- Reduce disk space usage
- Ensure version consistency across packages
- Simplify dependency management
- Enable better caching in CI/CD

### Standard Libraries
- **@apidevtools/swagger-parser**: Industry standard for OpenAPI parsing
- **@cucumber/gherkin**: Official Cucumber Gherkin parser
- **ajv**: JSON Schema validation (fastest and most widely used)
- **openapi-types**: TypeScript types for OpenAPI specifications
- **json-schema**: JSON Schema type definitions

### Version Selection
Versions chosen based on:
- Latest stable releases
- Compatibility with existing TypeScript 5.7.2
- Security considerations
- Community adoption and maintenance status
