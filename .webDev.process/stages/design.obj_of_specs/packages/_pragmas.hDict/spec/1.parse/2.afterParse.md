# afterParse Event Pragma

## Description

Event emitted after successful specification parsing. Allows middleware to validate parsed semantics, enhance the semantic structure, or trigger downstream processing.

## Event Type

`afterParse` - Pipeline event in parse stage

## Event Data Schema

```yaml
afterParseEvent:
  allOf:
    - $ref: '@spicetime/events#/components/schemas/PipelineEvent'
    - type: object
      properties:
        data:
          type: object
          required:
            - input
            - output
            - duration
          properties:
            input:
              type: object
              required:
                - specText
                - format
              properties:
                specText:
                  type: string
                  description: Original specification text
                format:
                  type: string
                  enum: [openapi, json-schema, gherkin]
                options:
                  type: object
                  additionalProperties: true
            output:
              type: object
              required:
                - semantics
              properties:
                semantics:
                  type: object
                  additionalProperties: true
                  description: Parsed semantic structure
                metadata:
                  type: object
                  properties:
                    parseTime:
                      type: number
                      description: Parse duration in milliseconds
                    warnings:
                      type: array
                      items:
                        type: string
                      description: Parse warnings
                    statistics:
                      type: object
                      properties:
                        components:
                          type: number
                        properties:
                          type: number
                        scenarios:
                          type: number
            duration:
              type: number
              description: Total parse duration in milliseconds
```

## Middleware Capabilities

### Semantic Validation
- Validate parsed semantic structure for completeness
- Check for required components or properties
- Verify semantic consistency across specification

### Semantic Enhancement
- Add computed properties to semantic structure
- Resolve references and dependencies
- Enrich with additional metadata

### Quality Analysis
- Analyze specification quality metrics
- Generate complexity scores
- Identify potential issues or improvements

## Usage Example

```typescript
// Middleware hook for afterParse event
const semanticValidatorPlugin = {
  name: 'semantic-validator',
  hooks: {
    afterParse: async (event, context) => {
      const { semantics } = event.data.output;
      
      // Validate semantic structure
      const validation = validateSemantics(semantics);
      
      if (!validation.valid) {
        context.logger.warn('Semantic validation failed', validation.errors);
        
        // Request additional processing time
        return {
          requestResources: [{
            type: 'cpu',
            amount: 2000,
            priority: 'normal',
            justification: 'Semantic validation and correction'
          }]
        };
      }
      
      // Enhance semantics with computed properties
      const enhancedSemantics = {
        ...semantics,
        computed: {
          complexity: calculateComplexity(semantics),
          dependencies: extractDependencies(semantics),
          coverage: analyzeCoverage(semantics)
        }
      };
      
      return {
        continue: {
          ...event,
          data: {
            ...event.data,
            output: {
              ...event.data.output,
              semantics: enhancedSemantics,
              metadata: {
                ...event.data.output.metadata,
                enhanced: true,
                enhancementTime: Date.now()
              }
            }
          }
        }
      };
    }
  }
};
```

## Integration Points

### Kernel Integration
- Report successful task completion
- Release allocated parsing resources
- Schedule downstream generation tasks

### Error Recovery
- Handle partial parse failures
- Attempt semantic reconstruction
- Provide fallback semantic structures

### Performance Monitoring
- Track parse performance metrics
- Compare against estimated duration
- Update performance models

## Related Events

- **Follows**: `beforeParse`
- **Precedes**: `beforeGenerate` (if generation follows)
- **Alternative**: `parseError` (if parsing fails)
- **Triggers**: May trigger generation pipeline events
