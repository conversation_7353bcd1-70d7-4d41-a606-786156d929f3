# Spec Pragma

> Transform industry-standard specifications into TypeScript implementation code, tests, and documentation.

## Overview

The Spec Pragma System bridges the gap between formal specifications and working code by transforming industry-standard formats (OpenAPI, JSON Schema, Gherkin) into complete TypeScript implementations with tests and documentation.

## Features

- ✅ **OpenAPI 3.0** component schema parsing
- ✅ **JSON Schema Draft 2020-12** support
- ✅ **Gherkin BDD** behavioral specifications
- ✅ **TypeScript** implementation generation
- ✅ **Vitest** test suite generation
- ✅ **Storybook** story generation
- ✅ **Bidirectional sync** between specs and code

## Quick Start

### 1. Define Component Schema (OpenAPI)

```yaml
# userprofile.openapi.yaml
openapi: 3.0.3
info:
  title: User Profile Component
  version: 1.0.0

components:
  schemas:
    UserProfileProps:
      type: object
      required: [user, onAvatarClick]
      properties:
        user:
          $ref: '#/components/schemas/User'
        onAvatarClick:
          type: string
          format: callback
```

### 2. Define Behavior (<PERSON><PERSON><PERSON>)

```gherkin
# userprofile.feature
Feature: User Profile Component
  Scenario: Display user information
    Given a user with name "<PERSON>"
    When the component renders
    Then it should display the user's name
```

### 3. Generate Implementation

```typescript
// Generated TypeScript component
const UserProfile = memo<UserProfileProps>(({ user, onAvatarClick }) => {
  return (
    <div className="user-profile">
      <img src={user.avatarUrl} onClick={onAvatarClick} />
      <h3>{user.name}</h3>
    </div>
  );
});
```

## API Reference

| Pragma | Purpose | Input | Output |
|--------|---------|-------|--------|
| `parse` | Parse specifications | OpenAPI/JSON Schema/Gherkin | Semantic structure |
| `generate` | Generate code | Semantic structure | TypeScript/Tests/Stories |
| `sync` | Bidirectional sync | Code changes | Updated specifications |
| `compose` | Ecosystem integration | Other pragmas | Composed functionality |

## File Structure

```
spec/
├── README.md                 # This file
├── spec-pragma.feature       # Gherkin behavioral specification
├── index.md                  # Main specification
├── 1.parse.md               # Parse pragma specification
├── 2.generate.md            # Generate pragma specification
├── 3.sync.md                # Sync pragma specification
├── 4.compose.md             # Compose pragma specification
├── .types.md                # TypeScript type definitions
├── .examples.md             # Usage examples
└── .integration.md          # Integration patterns
```

## Standards Compliance

- **OpenAPI 3.0.3** - Latest OpenAPI specification
- **JSON Schema Draft 2020-12** - Current JSON Schema standard
- **Gherkin** - Cucumber BDD syntax
- **TypeScript 5.0+** - Modern TypeScript features
- **Vitest** - Fast, modern test runner
- **Testing Library** - React testing best practices
- **Storybook CSF** - Component Story Format

## Integration

### With SpiceTime Pragma Ecosystem
- Composes with foundation pragmas (obj, prop, ttree, context)
- Integrates with React pragmas (component, hook, provider)
- Respects kernel scheduling and resource management

### With Development Tools
- **IDE Integration** - Real-time validation and generation
- **Version Control** - Git hooks for spec-code synchronization
- **CI/CD** - Automated validation and deployment
- **Build Tools** - Webpack/Vite plugin support

## Contributing

This specification follows industry standards and best practices. When contributing:

1. Use standard specification formats (OpenAPI, JSON Schema, Gherkin)
2. Follow TypeScript best practices
3. Include comprehensive test coverage
4. Document all public APIs
5. Maintain backward compatibility

## License

Part of the SpiceTime Architecture project.
