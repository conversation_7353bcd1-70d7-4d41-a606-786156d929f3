# Spec Pragma Types

## Core Types

### SpecPragma
```typescript
interface SpecPragma {
  parse(specText: string, format?: SpecFormat): SemanticStructure;
  generate(semantics: SemanticStructure, target: GenerationTarget): GeneratedArtifact;
  sync(implementation: string, specification: string): SyncResult;
  compose(pragmas: Pragma[]): ComposedSpec;
}
```

### SemanticStructure
```typescript
interface SemanticStructure {
  description: string;
  props: PropSpecification[];
  behavior: BehaviorSpecification[];
  performance: PerformanceSpecification[];
  examples: ExampleSpecification[];
  dependencies: DependencySpecification[];
  metadata: SpecMetadata;
}
```

### PropSpecification
```typescript
interface PropSpecification {
  name: string;
  type: string;
  required: boolean;
  default?: any;
  description?: string;
  validation?: ValidationRule[];
  scopeReference?: string;
}
```

### BehaviorSpecification
```typescript
interface BehaviorSpecification {
  description: string;
  trigger: string;
  action: string;
  expected: string;
  testCase: boolean;
  priority: 'low' | 'normal' | 'high';
}
```

### PerformanceSpecification
```typescript
interface PerformanceSpecification {
  requirement: string;
  metric: string;
  threshold: number | string;
  optimization: string[];
  measurement: string;
}
```

## Format Types

### SpecFormat
```typescript
enum SpecFormat {
  NATURAL_LANGUAGE = 'natural',
  STRUCTURED_YAML = 'structured',
  MINIMAL_SHORTHAND = 'minimal',
  GHERKIN_BDD = 'gherkin',
  JSON_SCHEMA = 'json'
}
```

### GenerationTarget
```typescript
enum GenerationTarget {
  IMPLEMENTATION = 'implementation',
  TESTS = 'tests',
  TYPES = 'types',
  DOCUMENTATION = 'docs',
  ALL = 'all'
}
```

## Result Types

### GeneratedArtifact
```typescript
interface GeneratedArtifact {
  code: string;
  target: GenerationTarget;
  dependencies: string[];
  metadata: GenerationMetadata;
  validation: ValidationResult;
}
```

### SyncResult
```typescript
interface SyncResult {
  updatedSpecification?: string;
  updatedImplementation?: string;
  conflicts: ConflictReport[];
  changesSummary: ChangesSummary;
  success: boolean;
}
```

### ComposedSpec
```typescript
interface ComposedSpec {
  spec: SpecPragma;
  composedPragmas: Pragma[];
  integrationPoints: IntegrationPoint[];
  kernelIntegration: KernelIntegration;
  metadata: CompositionMetadata;
}
```

## Integration Types

### KernelIntegration
```typescript
interface KernelIntegration {
  taskScheduling: boolean;
  priorityManagement: boolean;
  resourceAllocation: boolean;
  errorRecovery: boolean;
}
```

### IntegrationPoint
```typescript
interface IntegrationPoint {
  pragma: string;
  interface: string;
  dependencies: string[];
  compatibility: CompatibilityLevel;
}
```

### CompatibilityLevel
```typescript
enum CompatibilityLevel {
  FULL = 'full',
  PARTIAL = 'partial',
  REQUIRES_ADAPTATION = 'adaptation',
  INCOMPATIBLE = 'incompatible'
}
```
