# Spec Pragma Service Integration

## Overview

The spec pragma integrates with the modular SpiceTime service system rather than implementing its own event and middleware systems. This ensures consistency, reduces duplication, and enables evolution without entanglement.

## Service Dependencies

### Required Services
- **Events Service** (`@spicetime/events`) - Event system and schemas
- **Middleware Service** (`@spicetime/middleware`) - Plugin system
- **Kernel Service** (`@spicetime/kernel`) - Task scheduling and resources
- **Schema Registry** (`@spicetime/schemas`) - Schema management and discovery

### Service Integration Pattern

```typescript
// Spec pragma declares its service dependencies
import { EventService } from '@spicetime/events';
import { MiddlewareService } from '@spicetime/middleware';
import { KernelService } from '@spicetime/kernel';
import { SchemaRegistry } from '@spicetime/schemas';

interface SpecPragmaServices {
  events: EventService;
  middleware: MiddlewareService;
  kernel: KernelService;
  schemas: SchemaRegistry;
}
```

## Event Integration

### Event Types Used by Spec Pragma

```typescript
// Spec pragma specifies which event types it uses
const specPragmaEventTypes = [
  // Pipeline events
  'beforeParse',
  'afterParse', 
  'parseError',
  'beforeGenerate',
  'afterGenerate',
  'generateError',
  'beforeSync',
  'afterSync',
  'syncConflict',
  
  // Custom events
  'specValidated',
  'codeGenerated',
  'templatesLoaded'
];
```

### Event Data Schemas

```typescript
// Spec pragma defines its event data schemas
const specPragmaEventSchemas = {
  specValidated: {
    type: 'object',
    properties: {
      specType: { type: 'string', enum: ['openapi', 'json-schema', 'gherkin'] },
      valid: { type: 'boolean' },
      errors: { type: 'array', items: { type: 'string' } },
      semantics: { type: 'object' }
    }
  },
  
  codeGenerated: {
    type: 'object', 
    properties: {
      target: { type: 'string', enum: ['typescript', 'vitest', 'storybook'] },
      files: { 
        type: 'array',
        items: {
          type: 'object',
          properties: {
            path: { type: 'string' },
            content: { type: 'string' },
            type: { type: 'string' }
          }
        }
      },
      duration: { type: 'number' }
    }
  }
};
```

## Middleware Integration

### Plugin Registration

```typescript
// Spec pragma registers with middleware service
await middlewareService.registerPlugin({
  name: 'spec-pragma-core',
  version: '1.0.0',
  hooks: {
    beforeParse: 'handleBeforeParse',
    afterGenerate: 'handleAfterGenerate'
  },
  eventTypes: specPragmaEventTypes
});
```

### Event Emission

```typescript
// Spec pragma emits events through the event service
async function parseSpecification(specText: string, format: string) {
  // Emit before event
  await eventService.emit('beforeParse', {
    input: { specText, format },
    stage: 'parse',
    timestamp: Date.now()
  });
  
  try {
    const semantics = await parseSpec(specText, format);
    
    // Emit after event
    await eventService.emit('afterParse', {
      input: { specText, format },
      output: { semantics },
      stage: 'parse',
      timestamp: Date.now()
    });
    
    return semantics;
  } catch (error) {
    // Emit error event
    await eventService.emit('parseError', {
      input: { specText, format },
      error: error.message,
      stage: 'parse',
      timestamp: Date.now()
    });
    throw error;
  }
}
```

## Kernel Integration

### Task Scheduling

```typescript
// Spec pragma schedules tasks through kernel service
async function generateCode(semantics: any, target: string) {
  const taskId = await kernelService.scheduleTask({
    type: 'code-generate',
    priority: 'normal',
    payload: { semantics, target },
    resourceRequirements: [
      { type: 'cpu', amount: 1000 },
      { type: 'memory', amount: 50 * 1024 * 1024 }
    ]
  });
  
  // Wait for kernel approval
  await kernelService.waitForApproval(taskId);
  
  // Execute generation
  const result = await executeGeneration(semantics, target);
  
  // Report completion
  await kernelService.completeTask(taskId, result);
  
  return result;
}
```

### Resource Management

```typescript
// Spec pragma requests resources through kernel
async function processLargeSpecification(spec: string) {
  const allocation = await kernelService.requestResources({
    requirements: [
      { type: 'cpu', amount: 5000 },
      { type: 'memory', amount: 200 * 1024 * 1024 }
    ],
    requesterId: 'spec-pragma',
    justification: 'Large specification processing'
  });
  
  try {
    return await processSpec(spec);
  } finally {
    await kernelService.releaseResources(allocation.id);
  }
}
```

## Schema Registry Integration

### Schema Registration

```typescript
// Spec pragma registers its schemas
await schemaRegistry.registerSchema({
  name: 'spec-pragma-api',
  version: '1.0.0',
  type: 'openapi',
  definition: specPragmaAPISchema,
  service: 'spec-pragma'
});

await schemaRegistry.registerSchema({
  name: 'spec-pragma-events',
  version: '1.0.0', 
  type: 'event',
  definition: specPragmaEventSchemas,
  service: 'spec-pragma'
});
```

### Schema Discovery

```typescript
// Spec pragma discovers schemas from other services
const middlewareSchema = await schemaRegistry.getSchema('middleware-api');
const kernelSchema = await schemaRegistry.getSchema('kernel-api');

// Generate TypeScript clients from schemas
const middlewareClient = generateClient(middlewareSchema);
const kernelClient = generateClient(kernelSchema);
```

## Configuration

### Service Configuration

```typescript
// Spec pragma configuration references services
interface SpecPragmaConfig {
  services: {
    events: {
      endpoint: string;
      timeout: number;
    };
    middleware: {
      endpoint: string;
      pluginTimeout: number;
    };
    kernel: {
      endpoint: string;
      defaultPriority: 'low' | 'normal' | 'high';
    };
    schemas: {
      endpoint: string;
      cacheTimeout: number;
    };
  };
  
  // Spec pragma specific config
  parsers: { /* ... */ };
  generators: { /* ... */ };
  templates: { /* ... */ };
}
```

## Benefits of Service Integration

### Modularity
- Each service can evolve independently
- Clear separation of concerns
- Reusable across all packages

### Consistency
- Standardized event patterns
- Common middleware system
- Unified resource management

### Evolution
- Services can be upgraded without breaking packages
- New event types can be added centrally
- Schema evolution is managed centrally

### Reduced Complexity
- No duplicate event/middleware implementations
- Single source of truth for schemas
- Centralized service discovery

## Package Development Pattern

This establishes the pattern for all SpiceTime packages:

1. **Declare service dependencies** in package specification
2. **Register event types and schemas** with central services
3. **Emit events** through event service for middleware interception
4. **Schedule tasks** through kernel service for resource management
5. **Discover schemas** through schema registry for integration

This modular approach prevents the "stale spaghetti ball" problem by keeping services loosely coupled while providing strong integration points through well-defined schemas and interfaces.
