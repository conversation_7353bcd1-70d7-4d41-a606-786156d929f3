# CI/CD Configuration for Spec Pragma

## GitHub Actions Integration

### Add to Root .github/workflows/ci.yml

**Spec pragma specific job:**

```yaml
jobs:
  spec-pragma:
    name: Spec Pragma
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'pnpm'
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      
      - name: Type check spec pragma
        run: pnpm spec-pragma:type-check
      
      - name: Lint spec pragma
        run: pnpm spec-pragma:lint
      
      - name: Test spec pragma
        run: pnpm spec-pragma:test:coverage
      
      - name: Build spec pragma
        run: pnpm spec-pragma:build
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3
        with:
          file: ./packages/_pragmas.hDict/spec/coverage/lcov.info
          flags: spec-pragma
```

### Integration with Existing Pipeline

**Extend existing CI matrix:**

```yaml
strategy:
  matrix:
    package: [core, spec-pragma, other-packages]
    
steps:
  - name: Test ${{ matrix.package }}
    run: pnpm ${{ matrix.package }}:test
```

## Local CI Configuration

### .github/workflows/spec-pragma.yml
```yaml
name: Spec Pragma CI

on:
  push:
    paths:
      - 'packages/_pragmas.hDict/spec/**'
      - '.github/workflows/spec-pragma.yml'
  pull_request:
    paths:
      - 'packages/_pragmas.hDict/spec/**'

jobs:
  test:
    name: Test Spec Pragma
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'pnpm'
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      
      - name: Run tests
        run: pnpm spec-pragma:test:coverage
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3
        with:
          file: ./packages/_pragmas.hDict/spec/coverage/lcov.info
```

## Quality Gates

### Pre-commit Hooks

**Add to root .husky/pre-commit:**

```bash
#!/bin/sh
. "$(dirname "$0")/_/husky.sh"

# Run spec pragma checks
pnpm spec-pragma:type-check
pnpm spec-pragma:lint
pnpm spec-pragma:test
```

### Pre-push Hooks

**Add to root .husky/pre-push:**

```bash
#!/bin/sh
. "$(dirname "$0")/_/husky.sh"

# Full spec pragma validation
pnpm spec-pragma:build
pnpm spec-pragma:test:coverage
```

## Deployment Configuration

### Production Build

**Add to deployment pipeline:**

```yaml
- name: Build spec pragma for production
  run: |
    pnpm spec-pragma:build
    pnpm spec-pragma:test:coverage
  env:
    NODE_ENV: production
```

### Release Process

**Semantic release configuration:**

```json
{
  "branches": ["main", "phase-1-implementation"],
  "plugins": [
    "@semantic-release/commit-analyzer",
    "@semantic-release/release-notes-generator",
    "@semantic-release/changelog",
    "@semantic-release/npm",
    "@semantic-release/git"
  ]
}
```

## Integration with Existing CI/CD

### Reuse Existing Infrastructure
- Leverages existing GitHub Actions setup
- Shares Node.js and PNPM configuration
- Consistent with project CI patterns

### Parallel Execution
- Runs alongside other package tests
- Independent failure isolation
- Efficient resource utilization

### Caching Strategy
- Reuses existing dependency caches
- Turbo build caching
- Test result caching
