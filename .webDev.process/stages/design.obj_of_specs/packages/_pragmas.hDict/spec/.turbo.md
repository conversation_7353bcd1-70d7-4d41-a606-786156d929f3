# Turbo Configuration for Spec Pragma

## Root turbo.json Modifications

### Add Spec Pragma Tasks

**Add to root turbo.json pipeline:**

```json
{
  "pipeline": {
    "spec-pragma#build": {
      "dependsOn": ["^build"],
      "outputs": ["dist/**"],
      "inputs": ["src/**/*.ts", "tsconfig.json"]
    },
    "spec-pragma#test": {
      "dependsOn": ["build"],
      "outputs": ["coverage/**"],
      "inputs": ["src/**/*.ts", "src/**/*.test.ts", "vitest.config.ts"]
    },
    "spec-pragma#lint": {
      "outputs": [],
      "inputs": ["src/**/*.ts", ".eslintrc.local.json"]
    },
    "spec-pragma#type-check": {
      "dependsOn": ["^build"],
      "outputs": [],
      "inputs": ["src/**/*.ts", "tsconfig.json"]
    }
  }
}
```

### Global Task Integration

**Extend existing global tasks:**

```json
{
  "pipeline": {
    "build": {
      "dependsOn": ["spec-pragma#build"]
    },
    "test": {
      "dependsOn": ["spec-pragma#test"]
    },
    "lint": {
      "dependsOn": ["spec-pragma#lint"]
    }
  }
}
```

## Local Turbo Configuration

### turbo.json (local)
```json
{
  "extends": ["../../../../../turbo.json"],
  "pipeline": {
    "build": {
      "outputs": ["dist/**"],
      "inputs": ["src/**/*.ts", "tsconfig.json"]
    },
    "test": {
      "outputs": ["coverage/**"],
      "inputs": ["src/**/*.ts", "src/**/*.test.ts", "vitest.config.ts"]
    },
    "lint": {
      "outputs": [],
      "inputs": ["src/**/*.ts", ".eslintrc.local.json"]
    },
    "type-check": {
      "outputs": [],
      "inputs": ["src/**/*.ts", "tsconfig.json"]
    }
  }
}
```

## Caching Strategy

### Build Caching
- Cache TypeScript compilation outputs
- Invalidate on source file changes
- Share cache across team members

### Test Caching
- Cache test results and coverage
- Invalidate on test file or source changes
- Parallel test execution

### Lint Caching
- Cache ESLint results
- Fast feedback on code quality
- Skip unchanged files
