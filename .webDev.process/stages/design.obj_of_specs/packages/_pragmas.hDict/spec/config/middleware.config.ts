/**
 * Middleware Plugin System Configuration
 * 
 * Defines events and hooks that middleware can intercept during spec-to-code transformation
 * Integrates with SpiceTime kernel task queue and priority system
 */

export interface MiddlewareConfig {
  // Plugin registration
  plugins: MiddlewarePlugin[];
  
  // Event configuration
  events: {
    enabled: boolean;
    async: boolean;
    errorHandling: 'continue' | 'abort' | 'retry';
    timeout: number;
  };
  
  // Kernel integration
  kernel: {
    taskPriority: 'low' | 'normal' | 'high';
    resourceQuota: ResourceQuota;
    middlewareScheduling: boolean;
  };
  
  // Pipeline configuration
  pipeline: {
    parallelExecution: boolean;
    batchSize: number;
    retryAttempts: number;
  };
}

export interface MiddlewarePlugin {
  name: string;
  version: string;
  priority: number;
  
  // Event hooks
  hooks: {
    // Parse phase events
    beforeParse?: MiddlewareHook<ParseEvent>;
    afterParse?: MiddlewareHook<ParseEvent>;
    onParseError?: MiddlewareHook<ParseErrorEvent>;
    
    // Generate phase events
    beforeGenerate?: MiddlewareHook<GenerateEvent>;
    afterGenerate?: MiddlewareHook<GenerateEvent>;
    onGenerateError?: MiddlewareHook<GenerateErrorEvent>;
    
    // Sync phase events
    beforeSync?: MiddlewareHook<SyncEvent>;
    afterSync?: MiddlewareHook<SyncEvent>;
    onSyncConflict?: MiddlewareHook<SyncConflictEvent>;
    
    // Compose phase events
    beforeCompose?: MiddlewareHook<ComposeEvent>;
    afterCompose?: MiddlewareHook<ComposeEvent>;
    
    // Kernel task events
    onTaskScheduled?: MiddlewareHook<TaskScheduledEvent>;
    onTaskApproved?: MiddlewareHook<TaskApprovedEvent>;
    onTaskCompleted?: MiddlewareHook<TaskCompletedEvent>;
    onTaskFailed?: MiddlewareHook<TaskFailedEvent>;
    
    // Resource events
    onResourceRequest?: MiddlewareHook<ResourceRequestEvent>;
    onResourceAllocated?: MiddlewareHook<ResourceAllocatedEvent>;
    onResourceExhausted?: MiddlewareHook<ResourceExhaustedEvent>;
  };
  
  // Configuration
  config?: Record<string, any>;
  dependencies?: string[];
}

export interface MiddlewareHook<T extends MiddlewareEvent> {
  (event: T, context: MiddlewareContext): Promise<MiddlewareResult<T>>;
}

export interface MiddlewareContext {
  // Current task information
  task: {
    id: string;
    type: TaskType;
    priority: 'low' | 'normal' | 'high';
    stage: 'parse' | 'generate' | 'sync' | 'compose';
  };
  
  // Kernel integration
  kernel: {
    scheduleTask: (type: TaskType, priority: string) => Promise<string>;
    waitForApproval: (taskId: string) => Promise<void>;
    requestResource: (type: ResourceType, amount: number) => Promise<ResourceAllocation>;
    releaseResource: (allocation: ResourceAllocation) => Promise<void>;
  };
  
  // Configuration access
  config: MiddlewareConfig;
  
  // Shared state
  state: Map<string, any>;
  
  // Logging
  logger: {
    debug: (message: string, data?: any) => void;
    info: (message: string, data?: any) => void;
    warn: (message: string, data?: any) => void;
    error: (message: string, data?: any) => void;
  };
}

export interface MiddlewareResult<T extends MiddlewareEvent> {
  // Continue with potentially modified event
  continue?: Partial<T>;
  
  // Abort processing
  abort?: {
    reason: string;
    error?: Error;
  };
  
  // Reschedule task with different priority
  reschedule?: {
    priority: 'low' | 'normal' | 'high';
    delay?: number;
  };
  
  // Request additional resources
  requestResources?: ResourceRequest[];
  
  // Modify pipeline behavior
  pipeline?: {
    skipStages?: string[];
    addStages?: string[];
    parallelExecution?: boolean;
  };
}

// Event Types
export interface MiddlewareEvent {
  id: string;
  timestamp: number;
  stage: string;
}

export interface ParseEvent extends MiddlewareEvent {
  stage: 'parse';
  input: {
    specText: string;
    format: 'openapi' | 'json-schema' | 'gherkin';
    options: Record<string, any>;
  };
  output?: {
    semantics: any;
    metadata: any;
  };
}

export interface GenerateEvent extends MiddlewareEvent {
  stage: 'generate';
  input: {
    semantics: any;
    target: 'typescript' | 'vitest' | 'storybook';
    templates: Record<string, string>;
  };
  output?: {
    code: string;
    files: GeneratedFile[];
  };
}

export interface SyncEvent extends MiddlewareEvent {
  stage: 'sync';
  input: {
    specification: string;
    implementation: string;
    direction: 'spec-to-code' | 'code-to-spec' | 'bidirectional';
  };
  output?: {
    updatedSpec?: string;
    updatedCode?: string;
    conflicts: ConflictReport[];
  };
}

export interface ComposeEvent extends MiddlewareEvent {
  stage: 'compose';
  input: {
    pragmas: string[];
    integrationPoints: IntegrationPoint[];
  };
  output?: {
    composedSpec: any;
    dependencies: string[];
  };
}

// Kernel Task Events
export interface TaskScheduledEvent extends MiddlewareEvent {
  taskId: string;
  taskType: TaskType;
  priority: 'low' | 'normal' | 'high';
  estimatedDuration: number;
  resourceRequirements: ResourceRequirement[];
}

export interface TaskApprovedEvent extends MiddlewareEvent {
  taskId: string;
  approvalTime: number;
  allocatedResources: ResourceAllocation[];
}

export interface TaskCompletedEvent extends MiddlewareEvent {
  taskId: string;
  duration: number;
  resourceUsage: ResourceUsage[];
  result: any;
}

export interface TaskFailedEvent extends MiddlewareEvent {
  taskId: string;
  error: Error;
  retryCount: number;
  resourcesReleased: ResourceAllocation[];
}

// Resource Events
export interface ResourceRequestEvent extends MiddlewareEvent {
  resourceType: ResourceType;
  amount: number;
  priority: 'low' | 'normal' | 'high';
  requester: string;
}

export interface ResourceAllocatedEvent extends MiddlewareEvent {
  allocation: ResourceAllocation;
  remainingQuota: number;
}

export interface ResourceExhaustedEvent extends MiddlewareEvent {
  resourceType: ResourceType;
  requestedAmount: number;
  availableAmount: number;
  waitingTasks: string[];
}

// Supporting Types
export type TaskType = 
  | 'spec-parse'
  | 'code-generate'
  | 'test-generate'
  | 'story-generate'
  | 'sync-bidirectional'
  | 'pragma-compose';

export type ResourceType = 
  | 'cpu'
  | 'memory'
  | 'storage'
  | 'network'
  | 'time';

export interface ResourceQuota {
  cpu: number;        // CPU time in milliseconds
  memory: number;     // Memory in bytes
  storage: number;    // Storage in bytes
  network: number;    // Network bandwidth in bytes/sec
  time: number;       // Time quota in milliseconds
}

export interface ResourceRequirement {
  type: ResourceType;
  amount: number;
  duration: number;
  priority: 'low' | 'normal' | 'high';
}

export interface ResourceAllocation {
  id: string;
  type: ResourceType;
  amount: number;
  startTime: number;
  duration: number;
  taskId: string;
}

export interface ResourceUsage {
  type: ResourceType;
  allocated: number;
  used: number;
  efficiency: number;
}

export interface ResourceRequest {
  type: ResourceType;
  amount: number;
  priority: 'low' | 'normal' | 'high';
  justification: string;
}

export interface GeneratedFile {
  path: string;
  content: string;
  type: 'typescript' | 'test' | 'story' | 'documentation';
}

export interface ConflictReport {
  type: 'property' | 'method' | 'structure';
  location: string;
  specValue: any;
  codeValue: any;
  resolution: 'manual' | 'spec-wins' | 'code-wins';
}

export interface IntegrationPoint {
  pragma: string;
  interface: string;
  dependencies: string[];
}

export interface ParseErrorEvent extends MiddlewareEvent {
  stage: 'parse';
  error: Error;
  input: ParseEvent['input'];
}

export interface GenerateErrorEvent extends MiddlewareEvent {
  stage: 'generate';
  error: Error;
  input: GenerateEvent['input'];
}

export interface SyncConflictEvent extends MiddlewareEvent {
  stage: 'sync';
  conflicts: ConflictReport[];
  input: SyncEvent['input'];
}

export const defaultMiddlewareConfig: MiddlewareConfig = {
  plugins: [],
  events: {
    enabled: true,
    async: true,
    errorHandling: 'continue',
    timeout: 30000
  },
  kernel: {
    taskPriority: 'normal',
    resourceQuota: {
      cpu: 5000,      // 5 seconds
      memory: 100 * 1024 * 1024,  // 100MB
      storage: 10 * 1024 * 1024,  // 10MB
      network: 1024 * 1024,       // 1MB/sec
      time: 60000     // 1 minute
    },
    middlewareScheduling: true
  },
  pipeline: {
    parallelExecution: false,
    batchSize: 10,
    retryAttempts: 3
  }
};
