/**
 * Parser configuration for different specification formats
 * 
 * Controls how OpenAPI, JSON Schema, and Gherkin specs are parsed
 */

export interface ParsersConfig {
  openapi: OpenAPIParserConfig;
  jsonSchema: JSONSchemaParserConfig;
  gherkin: GherkinParserConfig;
}

export interface OpenAPIParserConfig {
  // Validation settings
  validation: {
    strict: boolean;
    allowUnknownProperties: boolean;
    validateExamples: boolean;
    validateResponses: boolean;
  };

  // Component extraction
  components: {
    extractSchemas: boolean;
    extractParameters: boolean;
    extractResponses: boolean;
    extractExamples: boolean;
  };

  // Type mapping
  typeMapping: {
    string: string;      // 'string'
    number: string;      // 'number'
    integer: string;     // 'number'
    boolean: string;     // 'boolean'
    array: string;       // 'Array<T>'
    object: string;      // 'object' | 'Record<string, any>'
    null: string;        // 'null'
  };

  // Extensions
  extensions: {
    'x-component-type': boolean;
    'x-react-props': boolean;
    'x-validation': boolean;
  };
}

export interface JSONSchemaParserConfig {
  // Schema validation
  validation: {
    metaSchema: string;  // Draft version
    strict: boolean;
    allowUnknownKeywords: boolean;
    validateFormats: boolean;
  };

  // Type generation
  typeGeneration: {
    generateInterfaces: boolean;
    generateTypes: boolean;
    generateEnums: boolean;
    generateUnions: boolean;
  };

  // Property handling
  properties: {
    requiredByDefault: boolean;
    additionalProperties: boolean;
    patternProperties: boolean;
    dependentSchemas: boolean;
  };

  // Format support
  formats: {
    date: boolean;
    'date-time': boolean;
    email: boolean;
    uri: boolean;
    uuid: boolean;
    regex: boolean;
  };
}

export interface GherkinParserConfig {
  // Language settings
  language: {
    primary: string;     // 'en'
    fallback: string[];  // ['en']
    keywords: Record<string, string[]>;
  };

  // Parsing options
  parsing: {
    strictSyntax: boolean;
    allowComments: boolean;
    allowTags: boolean;
    allowBackground: boolean;
  };

  // Feature extraction
  features: {
    extractScenarios: boolean;
    extractExamples: boolean;
    extractDataTables: boolean;
    extractDocStrings: boolean;
  };

  // Test mapping
  testMapping: {
    given: string;       // 'setup' | 'arrange'
    when: string;        // 'action' | 'act'
    then: string;        // 'assertion' | 'assert'
    and: string;         // 'continuation'
    but: string;         // 'exception'
  };
}

export const defaultParsersConfig: ParsersConfig = {
  openapi: {
    validation: {
      strict: true,
      allowUnknownProperties: false,
      validateExamples: true,
      validateResponses: true
    },
    components: {
      extractSchemas: true,
      extractParameters: true,
      extractResponses: false,
      extractExamples: true
    },
    typeMapping: {
      string: 'string',
      number: 'number',
      integer: 'number',
      boolean: 'boolean',
      array: 'Array<T>',
      object: 'Record<string, any>',
      null: 'null'
    },
    extensions: {
      'x-component-type': true,
      'x-react-props': true,
      'x-validation': true
    }
  },

  jsonSchema: {
    validation: {
      metaSchema: 'https://json-schema.org/draft/2020-12/schema',
      strict: true,
      allowUnknownKeywords: false,
      validateFormats: true
    },
    typeGeneration: {
      generateInterfaces: true,
      generateTypes: true,
      generateEnums: true,
      generateUnions: true
    },
    properties: {
      requiredByDefault: false,
      additionalProperties: true,
      patternProperties: true,
      dependentSchemas: true
    },
    formats: {
      date: true,
      'date-time': true,
      email: true,
      uri: true,
      uuid: true,
      regex: true
    }
  },

  gherkin: {
    language: {
      primary: 'en',
      fallback: ['en'],
      keywords: {
        feature: ['Feature'],
        scenario: ['Scenario'],
        given: ['Given'],
        when: ['When'],
        then: ['Then'],
        and: ['And'],
        but: ['But'],
        background: ['Background'],
        examples: ['Examples']
      }
    },
    parsing: {
      strictSyntax: true,
      allowComments: true,
      allowTags: true,
      allowBackground: true
    },
    features: {
      extractScenarios: true,
      extractExamples: true,
      extractDataTables: true,
      extractDocStrings: true
    },
    testMapping: {
      given: 'arrange',
      when: 'act',
      then: 'assert',
      and: 'continuation',
      but: 'exception'
    }
  }
};
