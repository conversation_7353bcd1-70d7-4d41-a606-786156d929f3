/**
 * API Schema Configuration for Spec Pragma
 * 
 * Defines APIs as schemas following concept 23 patterns
 * All APIs designed as OpenAPI schemas for consistency and tooling
 */

export interface APISchemaConfig {
  // Core API schemas
  schemas: {
    specPragma: OpenAPISchema;
    middleware: OpenAPISchema;
    kernel: OpenAPISchema;
    resources: OpenAPISchema;
  };
  
  // Schema validation
  validation: {
    strict: boolean;
    allowExtensions: boolean;
    validateExamples: boolean;
  };
  
  // Code generation from schemas
  generation: {
    typescript: boolean;
    documentation: boolean;
    clientSDK: boolean;
    serverStubs: boolean;
  };
}

export interface OpenAPISchema {
  openapi: '3.0.3';
  info: {
    title: string;
    version: string;
    description: string;
  };
  paths: Record<string, PathItem>;
  components: {
    schemas: Record<string, SchemaObject>;
    parameters?: Record<string, ParameterObject>;
    responses?: Record<string, ResponseObject>;
  };
}

// Spec Pragma API Schema
export const specPragmaAPISchema: OpenAPISchema = {
  openapi: '3.0.3',
  info: {
    title: 'Spec Pragma API',
    version: '1.0.0',
    description: 'API for transforming specifications into implementation code'
  },
  paths: {
    '/parse': {
      post: {
        summary: 'Parse specification',
        operationId: 'parseSpecification',
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/ParseRequest' }
            }
          }
        },
        responses: {
          '200': {
            description: 'Parsed specification',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ParseResponse' }
              }
            }
          },
          '400': {
            description: 'Parse error',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' }
              }
            }
          }
        }
      }
    },
    '/generate': {
      post: {
        summary: 'Generate code from semantics',
        operationId: 'generateCode',
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/GenerateRequest' }
            }
          }
        },
        responses: {
          '200': {
            description: 'Generated code',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/GenerateResponse' }
              }
            }
          }
        }
      }
    },
    '/sync': {
      post: {
        summary: 'Synchronize specification and implementation',
        operationId: 'syncSpecification',
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/SyncRequest' }
            }
          }
        },
        responses: {
          '200': {
            description: 'Synchronization result',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/SyncResponse' }
              }
            }
          }
        }
      }
    },
    '/compose': {
      post: {
        summary: 'Compose with other pragmas',
        operationId: 'composePragmas',
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/ComposeRequest' }
            }
          }
        },
        responses: {
          '200': {
            description: 'Composition result',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ComposeResponse' }
              }
            }
          }
        }
      }
    }
  },
  components: {
    schemas: {
      ParseRequest: {
        type: 'object',
        required: ['specText', 'format'],
        properties: {
          specText: {
            type: 'string',
            description: 'Specification text to parse'
          },
          format: {
            type: 'string',
            enum: ['openapi', 'json-schema', 'gherkin'],
            description: 'Specification format'
          },
          options: {
            type: 'object',
            description: 'Parser options',
            additionalProperties: true
          }
        }
      },
      ParseResponse: {
        type: 'object',
        required: ['semantics'],
        properties: {
          semantics: {
            type: 'object',
            description: 'Parsed semantic structure',
            additionalProperties: true
          },
          metadata: {
            type: 'object',
            description: 'Parse metadata',
            additionalProperties: true
          },
          warnings: {
            type: 'array',
            items: { type: 'string' },
            description: 'Parse warnings'
          }
        }
      },
      GenerateRequest: {
        type: 'object',
        required: ['semantics', 'target'],
        properties: {
          semantics: {
            type: 'object',
            description: 'Semantic structure from parse phase',
            additionalProperties: true
          },
          target: {
            type: 'string',
            enum: ['typescript', 'vitest', 'storybook'],
            description: 'Generation target'
          },
          templates: {
            type: 'object',
            description: 'Custom templates',
            additionalProperties: { type: 'string' }
          },
          options: {
            type: 'object',
            description: 'Generation options',
            additionalProperties: true
          }
        }
      },
      GenerateResponse: {
        type: 'object',
        required: ['files'],
        properties: {
          files: {
            type: 'array',
            items: { $ref: '#/components/schemas/GeneratedFile' },
            description: 'Generated files'
          },
          metadata: {
            type: 'object',
            description: 'Generation metadata',
            additionalProperties: true
          }
        }
      },
      GeneratedFile: {
        type: 'object',
        required: ['path', 'content', 'type'],
        properties: {
          path: {
            type: 'string',
            description: 'File path'
          },
          content: {
            type: 'string',
            description: 'File content'
          },
          type: {
            type: 'string',
            enum: ['typescript', 'test', 'story', 'documentation'],
            description: 'File type'
          }
        }
      },
      SyncRequest: {
        type: 'object',
        required: ['specification', 'implementation'],
        properties: {
          specification: {
            type: 'string',
            description: 'Current specification'
          },
          implementation: {
            type: 'string',
            description: 'Current implementation'
          },
          direction: {
            type: 'string',
            enum: ['spec-to-code', 'code-to-spec', 'bidirectional'],
            default: 'bidirectional',
            description: 'Sync direction'
          },
          options: {
            type: 'object',
            description: 'Sync options',
            properties: {
              preserveCustomCode: { type: 'boolean', default: true },
              conflictResolution: {
                type: 'string',
                enum: ['manual', 'spec-wins', 'code-wins'],
                default: 'manual'
              }
            }
          }
        }
      },
      SyncResponse: {
        type: 'object',
        properties: {
          updatedSpecification: {
            type: 'string',
            description: 'Updated specification'
          },
          updatedImplementation: {
            type: 'string',
            description: 'Updated implementation'
          },
          conflicts: {
            type: 'array',
            items: { $ref: '#/components/schemas/ConflictReport' },
            description: 'Sync conflicts'
          },
          changesSummary: {
            type: 'object',
            description: 'Summary of changes made',
            additionalProperties: true
          }
        }
      },
      ConflictReport: {
        type: 'object',
        required: ['type', 'location', 'specValue', 'codeValue'],
        properties: {
          type: {
            type: 'string',
            enum: ['property', 'method', 'structure'],
            description: 'Conflict type'
          },
          location: {
            type: 'string',
            description: 'Conflict location'
          },
          specValue: {
            description: 'Value in specification'
          },
          codeValue: {
            description: 'Value in implementation'
          },
          resolution: {
            type: 'string',
            enum: ['manual', 'spec-wins', 'code-wins'],
            description: 'Conflict resolution strategy'
          }
        }
      },
      ComposeRequest: {
        type: 'object',
        required: ['pragmas'],
        properties: {
          pragmas: {
            type: 'array',
            items: { type: 'string' },
            description: 'Pragma names to compose with'
          },
          integrationPoints: {
            type: 'array',
            items: { $ref: '#/components/schemas/IntegrationPoint' },
            description: 'Integration points'
          },
          options: {
            type: 'object',
            description: 'Composition options',
            additionalProperties: true
          }
        }
      },
      ComposeResponse: {
        type: 'object',
        required: ['composedSpec'],
        properties: {
          composedSpec: {
            type: 'object',
            description: 'Composed specification',
            additionalProperties: true
          },
          dependencies: {
            type: 'array',
            items: { type: 'string' },
            description: 'Pragma dependencies'
          },
          metadata: {
            type: 'object',
            description: 'Composition metadata',
            additionalProperties: true
          }
        }
      },
      IntegrationPoint: {
        type: 'object',
        required: ['pragma', 'interface'],
        properties: {
          pragma: {
            type: 'string',
            description: 'Pragma name'
          },
          interface: {
            type: 'string',
            description: 'Interface name'
          },
          dependencies: {
            type: 'array',
            items: { type: 'string' },
            description: 'Dependencies'
          }
        }
      },
      ErrorResponse: {
        type: 'object',
        required: ['error', 'message'],
        properties: {
          error: {
            type: 'string',
            description: 'Error code'
          },
          message: {
            type: 'string',
            description: 'Error message'
          },
          details: {
            type: 'object',
            description: 'Error details',
            additionalProperties: true
          }
        }
      }
    }
  }
};

// Middleware API Schema
export const middlewareAPISchema: OpenAPISchema = {
  openapi: '3.0.3',
  info: {
    title: 'Middleware API',
    version: '1.0.0',
    description: 'API for middleware plugin system'
  },
  paths: {
    '/plugins': {
      get: {
        summary: 'List registered plugins',
        responses: {
          '200': {
            description: 'List of plugins',
            content: {
              'application/json': {
                schema: {
                  type: 'array',
                  items: { $ref: '#/components/schemas/PluginInfo' }
                }
              }
            }
          }
        }
      },
      post: {
        summary: 'Register plugin',
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/PluginRegistration' }
            }
          }
        },
        responses: {
          '201': {
            description: 'Plugin registered',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/PluginInfo' }
              }
            }
          }
        }
      }
    },
    '/events/{eventType}': {
      post: {
        summary: 'Emit event to middleware',
        parameters: [
          {
            name: 'eventType',
            in: 'path',
            required: true,
            schema: {
              type: 'string',
              enum: [
                'beforeParse', 'afterParse', 'onParseError',
                'beforeGenerate', 'afterGenerate', 'onGenerateError',
                'beforeSync', 'afterSync', 'onSyncConflict',
                'beforeCompose', 'afterCompose',
                'onTaskScheduled', 'onTaskApproved', 'onTaskCompleted', 'onTaskFailed',
                'onResourceRequest', 'onResourceAllocated', 'onResourceExhausted'
              ]
            }
          }
        ],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/MiddlewareEvent' }
            }
          }
        },
        responses: {
          '200': {
            description: 'Event processed',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/MiddlewareResult' }
              }
            }
          }
        }
      }
    }
  },
  components: {
    schemas: {
      PluginInfo: {
        type: 'object',
        required: ['name', 'version', 'priority'],
        properties: {
          name: { type: 'string' },
          version: { type: 'string' },
          priority: { type: 'number' },
          hooks: {
            type: 'array',
            items: { type: 'string' }
          },
          dependencies: {
            type: 'array',
            items: { type: 'string' }
          }
        }
      },
      PluginRegistration: {
        type: 'object',
        required: ['name', 'version', 'hooks'],
        properties: {
          name: { type: 'string' },
          version: { type: 'string' },
          priority: { type: 'number', default: 100 },
          hooks: {
            type: 'object',
            additionalProperties: { type: 'string' }
          },
          config: {
            type: 'object',
            additionalProperties: true
          },
          dependencies: {
            type: 'array',
            items: { type: 'string' }
          }
        }
      },
      MiddlewareEvent: {
        type: 'object',
        required: ['id', 'timestamp', 'stage'],
        properties: {
          id: { type: 'string' },
          timestamp: { type: 'number' },
          stage: { type: 'string' },
          input: {
            type: 'object',
            additionalProperties: true
          },
          output: {
            type: 'object',
            additionalProperties: true
          }
        }
      },
      MiddlewareResult: {
        type: 'object',
        properties: {
          continue: {
            type: 'object',
            additionalProperties: true
          },
          abort: {
            type: 'object',
            properties: {
              reason: { type: 'string' },
              error: { type: 'string' }
            }
          },
          reschedule: {
            type: 'object',
            properties: {
              priority: {
                type: 'string',
                enum: ['low', 'normal', 'high']
              },
              delay: { type: 'number' }
            }
          },
          requestResources: {
            type: 'array',
            items: { $ref: '#/components/schemas/ResourceRequest' }
          }
        }
      },
      ResourceRequest: {
        type: 'object',
        required: ['type', 'amount', 'priority'],
        properties: {
          type: {
            type: 'string',
            enum: ['cpu', 'memory', 'storage', 'network', 'time']
          },
          amount: { type: 'number' },
          priority: {
            type: 'string',
            enum: ['low', 'normal', 'high']
          },
          justification: { type: 'string' }
        }
      }
    }
  }
};

export const defaultAPISchemaConfig: APISchemaConfig = {
  schemas: {
    specPragma: specPragmaAPISchema,
    middleware: middlewareAPISchema,
    kernel: {} as OpenAPISchema, // Would be defined separately
    resources: {} as OpenAPISchema // Would be defined separately
  },
  validation: {
    strict: true,
    allowExtensions: true,
    validateExamples: true
  },
  generation: {
    typescript: true,
    documentation: true,
    clientSDK: true,
    serverStubs: false
  }
};

// Supporting types for OpenAPI schema structure
interface PathItem {
  get?: OperationObject;
  post?: OperationObject;
  put?: OperationObject;
  delete?: OperationObject;
  patch?: OperationObject;
}

interface OperationObject {
  summary?: string;
  operationId?: string;
  parameters?: ParameterObject[];
  requestBody?: RequestBodyObject;
  responses: Record<string, ResponseObject>;
}

interface ParameterObject {
  name: string;
  in: 'path' | 'query' | 'header' | 'cookie';
  required?: boolean;
  schema: SchemaObject;
}

interface RequestBodyObject {
  required?: boolean;
  content: Record<string, MediaTypeObject>;
}

interface ResponseObject {
  description: string;
  content?: Record<string, MediaTypeObject>;
}

interface MediaTypeObject {
  schema: SchemaObject;
}

interface SchemaObject {
  type?: string;
  enum?: string[];
  items?: SchemaObject;
  properties?: Record<string, SchemaObject>;
  required?: string[];
  additionalProperties?: boolean | SchemaObject;
  $ref?: string;
  description?: string;
  default?: any;
}
