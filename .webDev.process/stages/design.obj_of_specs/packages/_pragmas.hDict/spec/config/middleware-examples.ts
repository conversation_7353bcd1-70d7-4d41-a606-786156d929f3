/**
 * Example middleware plugins demonstrating the plugin system
 * 
 * Shows how middleware can customize behavior at different pipeline stages
 */

import { MiddlewarePlugin, MiddlewareHook, ParseEvent, GenerateEvent, TaskScheduledEvent } from './middleware.config';

// Example 1: Custom Parser Middleware
export const customParserPlugin: MiddlewarePlugin = {
  name: 'custom-parser',
  version: '1.0.0',
  priority: 100,
  
  hooks: {
    beforeParse: async (event: ParseEvent, context) => {
      // Custom preprocessing of specification text
      if (event.input.format === 'openapi') {
        // Add custom OpenAPI extensions
        const modifiedSpec = addCustomExtensions(event.input.specText);
        
        return {
          continue: {
            ...event,
            input: {
              ...event.input,
              specText: modifiedSpec
            }
          }
        };
      }
      
      return { continue: event };
    },
    
    afterParse: async (event: ParseEvent, context) => {
      // Validate parsed semantics
      if (!validateSemantics(event.output?.semantics)) {
        context.logger.warn('Semantics validation failed', event.output?.semantics);
        
        // Request additional CPU time for re-parsing
        return {
          requestResources: [{
            type: 'cpu',
            amount: 1000,
            priority: 'high',
            justification: 'Re-parsing with stricter validation'
          }]
        };
      }
      
      return { continue: event };
    }
  }
};

// Example 2: Code Quality Middleware
export const codeQualityPlugin: MiddlewarePlugin = {
  name: 'code-quality',
  version: '1.0.0',
  priority: 200,
  
  hooks: {
    afterGenerate: async (event: GenerateEvent, context) => {
      // Run code quality checks
      const qualityScore = await analyzeCodeQuality(event.output?.code);
      
      if (qualityScore < 0.8) {
        context.logger.warn(`Code quality score: ${qualityScore}`, {
          taskId: context.task.id,
          target: event.input.target
        });
        
        // Reschedule with higher priority for quality improvement
        return {
          reschedule: {
            priority: 'high',
            delay: 1000
          }
        };
      }
      
      // Add quality metadata
      const enhancedOutput = {
        ...event.output,
        metadata: {
          ...event.output?.metadata,
          qualityScore,
          qualityChecks: ['typescript', 'eslint', 'prettier']
        }
      };
      
      return {
        continue: {
          ...event,
          output: enhancedOutput
        }
      };
    }
  },
  
  config: {
    qualityThreshold: 0.8,
    checks: ['typescript', 'eslint', 'prettier', 'complexity']
  }
};

// Example 3: Performance Monitoring Middleware
export const performanceMonitorPlugin: MiddlewarePlugin = {
  name: 'performance-monitor',
  version: '1.0.0',
  priority: 50,
  
  hooks: {
    onTaskScheduled: async (event: TaskScheduledEvent, context) => {
      // Track task scheduling metrics
      context.state.set(`task-${event.taskId}-start`, Date.now());
      
      context.logger.info('Task scheduled', {
        taskId: event.taskId,
        type: event.taskType,
        priority: event.priority,
        estimatedDuration: event.estimatedDuration
      });
      
      return { continue: event };
    },
    
    onTaskCompleted: async (event, context) => {
      // Calculate actual vs estimated duration
      const startTime = context.state.get(`task-${event.taskId}-start`);
      const actualDuration = Date.now() - startTime;
      const estimatedDuration = context.state.get(`task-${event.taskId}-estimated`) || 0;
      
      const variance = Math.abs(actualDuration - estimatedDuration) / estimatedDuration;
      
      if (variance > 0.5) {
        context.logger.warn('Task duration variance exceeded threshold', {
          taskId: event.taskId,
          estimated: estimatedDuration,
          actual: actualDuration,
          variance
        });
      }
      
      // Store performance metrics for future estimation
      await storePerformanceMetrics({
        taskType: context.task.type,
        actualDuration,
        estimatedDuration,
        resourceUsage: event.resourceUsage
      });
      
      return { continue: event };
    },
    
    onResourceExhausted: async (event, context) => {
      // Handle resource exhaustion
      context.logger.error('Resource exhausted', {
        resourceType: event.resourceType,
        requested: event.requestedAmount,
        available: event.availableAmount,
        waitingTasks: event.waitingTasks.length
      });
      
      // Request quota increase from kernel
      const quotaIncrease = await context.kernel.requestResource(
        event.resourceType,
        event.requestedAmount - event.availableAmount
      );
      
      if (quotaIncrease) {
        context.logger.info('Additional resources allocated', quotaIncrease);
      }
      
      return { continue: event };
    }
  }
};

// Example 4: Custom Template Middleware
export const customTemplatePlugin: MiddlewarePlugin = {
  name: 'custom-template',
  version: '1.0.0',
  priority: 150,
  
  hooks: {
    beforeGenerate: async (event: GenerateEvent, context) => {
      // Use custom templates based on project configuration
      const projectConfig = await loadProjectConfig();
      
      if (projectConfig.useCustomTemplates) {
        const customTemplates = await loadCustomTemplates(projectConfig.templatePath);
        
        return {
          continue: {
            ...event,
            input: {
              ...event.input,
              templates: {
                ...event.input.templates,
                ...customTemplates
              }
            }
          }
        };
      }
      
      return { continue: event };
    }
  },
  
  dependencies: ['file-system', 'template-loader']
};

// Example 5: Kernel Integration Middleware
export const kernelIntegrationPlugin: MiddlewarePlugin = {
  name: 'kernel-integration',
  version: '1.0.0',
  priority: 10, // High priority for kernel coordination
  
  hooks: {
    onTaskScheduled: async (event: TaskScheduledEvent, context) => {
      // Coordinate with kernel for optimal scheduling
      const systemLoad = await getSystemLoad();
      
      if (systemLoad > 0.8 && event.priority === 'low') {
        // Defer low priority tasks when system is under load
        return {
          reschedule: {
            priority: 'low',
            delay: 5000 // 5 second delay
          }
        };
      }
      
      return { continue: event };
    },
    
    onTaskApproved: async (event, context) => {
      // Optimize resource allocation based on task type
      const optimizedAllocation = await optimizeResourceAllocation(
        event.allocatedResources,
        context.task.type
      );
      
      if (optimizedAllocation) {
        context.logger.info('Resource allocation optimized', {
          taskId: event.taskId,
          original: event.allocatedResources,
          optimized: optimizedAllocation
        });
      }
      
      return { continue: event };
    }
  }
};

// Helper functions (would be implemented elsewhere)
function addCustomExtensions(specText: string): string {
  // Add custom OpenAPI extensions
  return specText;
}

function validateSemantics(semantics: any): boolean {
  // Validate parsed semantics
  return semantics && typeof semantics === 'object';
}

async function analyzeCodeQuality(code?: string): Promise<number> {
  // Analyze code quality and return score 0-1
  return 0.85;
}

async function storePerformanceMetrics(metrics: any): Promise<void> {
  // Store performance metrics for future use
}

async function loadProjectConfig(): Promise<any> {
  // Load project-specific configuration
  return { useCustomTemplates: false };
}

async function loadCustomTemplates(path: string): Promise<Record<string, string>> {
  // Load custom templates from path
  return {};
}

async function getSystemLoad(): Promise<number> {
  // Get current system load 0-1
  return 0.5;
}

async function optimizeResourceAllocation(
  allocations: any[],
  taskType: string
): Promise<any[] | null> {
  // Optimize resource allocation for task type
  return null;
}

// Export all example plugins
export const examplePlugins = [
  customParserPlugin,
  codeQualityPlugin,
  performanceMonitorPlugin,
  customTemplatePlugin,
  kernelIntegrationPlugin
];
