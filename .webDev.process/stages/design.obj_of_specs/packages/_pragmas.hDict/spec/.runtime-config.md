# Runtime Configuration for Spec Pragma

## Overview

Runtime configuration files that control how the spec pragma converts specifications into implementation code during execution.

## Configuration Files

### spec-pragma.config.ts
Main configuration for spec-to-code transformation pipeline.

### parsers.config.ts  
Configuration for specification format parsers (OpenAPI, JSON Schema, <PERSON>herkin).

### generators.config.ts
Configuration for code generators (TypeScript, Vitest, Storybook).

### templates.config.ts
Template configuration for generated code patterns.

### sync.config.ts
Configuration for bidirectional synchronization between specs and code.

### integration.config.ts
Configuration for pragma ecosystem integration and kernel coordination.

## Usage

These configuration files are loaded at runtime by the spec pragma implementation to control:

- **Parsing behavior** - How different spec formats are interpreted
- **Generation patterns** - What code gets generated and how
- **Template selection** - Which templates to use for different scenarios  
- **Sync strategies** - How to maintain spec-code consistency
- **Integration points** - How to compose with other pragmas

## File Locations

```
packages/_pragmas.hDict/spec/
├── config/
│   ├── spec-pragma.config.ts      # Main runtime config
│   ├── parsers.config.ts          # Parser configurations
│   ├── generators.config.ts       # Generator configurations  
│   ├── templates.config.ts        # Template configurations
│   ├── sync.config.ts             # Sync configurations
│   └── integration.config.ts      # Integration configurations
└── templates/
    ├── typescript/                # TypeScript generation templates
    ├── vitest/                    # Vitest generation templates
    └── storybook/                 # Storybook generation templates
```

These runtime configs are separate from the development/build configuration files and are used by the actual spec pragma implementation during spec-to-code transformation.
