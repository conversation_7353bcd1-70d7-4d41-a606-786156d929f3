{"name": "@spicetime/spec-pragma", "version": "1.0.0", "description": "Transform industry-standard specifications into TypeScript implementation code, tests, and documentation", "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}}, "scripts": {"build": "tsc", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit", "dev": "tsc --watch"}, "dependencies": {"@apidevtools/swagger-parser": "^10.1.0", "@cucumber/gherkin": "^27.0.0", "ajv": "^8.12.0", "ajv-formats": "^2.1.1", "openapi-types": "^12.1.3", "json-schema": "^0.4.0"}, "devDependencies": {"@types/json-schema": "^7.0.15", "@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "@vitest/coverage-v8": "^1.0.0", "@vitest/ui": "^1.0.0", "eslint": "^8.54.0", "typescript": "^5.3.0", "vitest": "^1.0.0"}, "peerDependencies": {"react": "^18.0.0", "@testing-library/react": "^14.0.0", "@storybook/react": "^7.0.0"}, "keywords": ["specification", "openapi", "json-schema", "g<PERSON>kin", "typescript", "code-generation", "testing", "react", "pragma", "spicetime"], "author": "SpiceTime Architecture", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/spicetime/architecture.git", "directory": "packages/spec-pragma"}, "engines": {"node": ">=18.0.0"}, "files": ["dist", "README.md", "package.json"]}