openapi: 3.0.3
info:
  title: ST Pragma API
  version: 1.0.0
  description: Foundational pragma system API - core infrastructure for all SpiceTime pragmas

paths:
  /pragmas:
    get:
      summary: List registered pragmas
      operationId: listPragmas
      parameters:
        - name: type
          in: query
          schema:
            type: string
        - name: status
          in: query
          schema:
            type: string
            enum: [active, inactive, error]
      responses:
        '200':
          description: List of pragmas
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PragmaInfo'

    post:
      summary: Register a new pragma
      operationId: registerPragma
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PragmaRegistration'
      responses:
        '201':
          description: Pragma registered successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PragmaInfo'

  /pragmas/{pragmaId}/execute:
    post:
      summary: Execute pragma
      operationId: executePragma
      parameters:
        - name: pragmaId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ExecutionRequest'
      responses:
        '200':
          description: Execution completed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExecutionResponse'

  /pragmas/{pragmaId}/children:
    get:
      summary: List child pragmas
      operationId: listChildPragmas
      parameters:
        - name: pragmaId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: List of child pragmas
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ChildPragmaInfo'

  /events:
    post:
      summary: Emit event
      operationId: emitEvent
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EventEmission'
      responses:
        '200':
          description: Event emitted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EventResult'

  /events/subscribe:
    post:
      summary: Subscribe to events
      operationId: subscribeToEvents
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EventSubscription'
      responses:
        '201':
          description: Subscription created successfully

components:
  schemas:
    # Core Pragma Types
    PragmaRegistration:
      type: object
      required:
        - name
        - version
        - schema
      properties:
        name:
          type: string
          description: Pragma name
        version:
          type: string
          description: Pragma version (semver)
        description:
          type: string
        schema:
          $ref: '#/components/schemas/PragmaSchema'
        children:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/PragmaRegistration'
        dependencies:
          type: array
          items:
            type: string

    PragmaInfo:
      allOf:
        - $ref: '#/components/schemas/PragmaRegistration'
        - type: object
          required:
            - id
            - status
            - registeredAt
          properties:
            id:
              type: string
              format: uuid
            status:
              type: string
              enum: [active, inactive, error]
            registeredAt:
              type: number
            lastExecuted:
              type: number
            executionCount:
              type: number

    PragmaSchema:
      type: object
      required:
        - input
        - output
      properties:
        input:
          $ref: '#/components/schemas/InputSchema'
        output:
          $ref: '#/components/schemas/OutputSchema'
        events:
          type: array
          items:
            type: string
        hooks:
          type: array
          items:
            type: string
        resources:
          $ref: '#/components/schemas/ResourceRequirements'

    InputSchema:
      type: object
      required:
        - type
      properties:
        type:
          type: string
        properties:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/PropertySchema'
        required:
          type: array
          items:
            type: string

    OutputSchema:
      type: object
      required:
        - type
      properties:
        type:
          type: string
        properties:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/PropertySchema'

    PropertySchema:
      type: object
      required:
        - type
      properties:
        type:
          type: string
        description:
          type: string
        enum:
          type: array
        format:
          type: string
        items:
          $ref: '#/components/schemas/PropertySchema'

    ChildPragmaInfo:
      type: object
      required:
        - name
        - type
        - status
      properties:
        name:
          type: string
        type:
          type: string
        status:
          type: string
          enum: [active, inactive, error]
        dependencies:
          type: array
          items:
            type: string

    # Execution Types
    ExecutionRequest:
      type: object
      required:
        - input
      properties:
        input:
          type: object
          additionalProperties: true
        context:
          $ref: '#/components/schemas/ExecutionContext'
        options:
          type: object
          additionalProperties: true

    ExecutionResponse:
      type: object
      required:
        - output
        - metadata
      properties:
        output:
          type: object
          additionalProperties: true
        metadata:
          $ref: '#/components/schemas/ExecutionMetadata'
        events:
          type: array
          items:
            $ref: '#/components/schemas/STEvent'

    ExecutionContext:
      type: object
      required:
        - taskId
        - correlationId
      properties:
        taskId:
          type: string
          format: uuid
        correlationId:
          type: string
          format: uuid
        priority:
          type: string
          enum: [low, normal, high, critical]
        timeout:
          type: number
        config:
          type: object
          additionalProperties: true

    ExecutionMetadata:
      type: object
      properties:
        duration:
          type: number
          description: Execution duration in milliseconds
        resourceUsage:
          $ref: '#/components/schemas/ResourceUsage'
        childResults:
          type: object
          additionalProperties:
            type: object
        errors:
          type: array
          items:
            $ref: '#/components/schemas/ExecutionError'

    ExecutionError:
      type: object
      required:
        - code
        - message
      properties:
        code:
          type: string
        message:
          type: string
        source:
          type: string
        stack:
          type: string
        recoverable:
          type: boolean

    # Event Types
    EventEmission:
      type: object
      required:
        - type
        - source
        - data
      properties:
        type:
          type: string
        source:
          type: string
        data:
          type: object
          additionalProperties: true
        correlationId:
          type: string
          format: uuid

    EventResult:
      type: object
      required:
        - eventId
        - processed
      properties:
        eventId:
          type: string
          format: uuid
        processed:
          type: boolean
        middlewareResults:
          type: array
          items:
            $ref: '#/components/schemas/MiddlewareResult'

    EventSubscription:
      type: object
      required:
        - eventTypes
        - handler
      properties:
        eventTypes:
          type: array
          items:
            type: string
        handler:
          type: string
          description: Handler endpoint or function reference
        conditions:
          type: array
          items:
            $ref: '#/components/schemas/EventCondition'

    EventCondition:
      type: object
      required:
        - field
        - operator
        - value
      properties:
        field:
          type: string
        operator:
          type: string
          enum: [equals, notEquals, contains, startsWith, endsWith, greaterThan, lessThan]
        value:
          description: Value to compare against

    STEvent:
      type: object
      required:
        - id
        - type
        - timestamp
        - source
      properties:
        id:
          type: string
          format: uuid
        type:
          type: string
        timestamp:
          type: number
        source:
          type: string
        correlationId:
          type: string
          format: uuid
        data:
          type: object
          additionalProperties: true
        context:
          type: object
          properties:
            taskId:
              type: string
            pragmaPath:
              type: string
            executionDepth:
              type: number

    MiddlewareResult:
      type: object
      properties:
        pluginId:
          type: string
        action:
          type: string
          enum: [continue, abort, reschedule, modify]
        modifiedEvent:
          $ref: '#/components/schemas/STEvent'
        error:
          type: string

    # Resource Types
    ResourceRequirements:
      type: object
      properties:
        cpu:
          type: number
          description: CPU time in milliseconds
        memory:
          type: number
          description: Memory in bytes
        storage:
          type: number
          description: Storage in bytes
        network:
          type: number
          description: Network bandwidth in bytes/sec
        time:
          type: number
          description: Time quota in milliseconds

    ResourceUsage:
      type: object
      properties:
        cpu:
          type: number
        memory:
          type: number
        storage:
          type: number
        network:
          type: number
        efficiency:
          type: number
          minimum: 0
          maximum: 1
