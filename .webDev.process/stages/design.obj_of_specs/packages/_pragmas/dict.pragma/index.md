
## Overview

`TypedDict<T>` is a type-safe dictionary implementation that enforces value type constraints while preserving the simplicity of standard JavaScript objects. It enables nested dictionary structures while ensuring all values conform to a predefined set of allowed types.

## Type Definition

```typescript
/**
 * A dictionary type that constrains all values to be either:
 * 1. One of the permitted types from array T
 * 2. A nested dictionary with the same type constraints
 * 
 * @template T - An array of allowed value types
 */
type TypedDict<T extends any[]> = {
  [key: string]: T[number] | TypedDict<T>;
};
```

## Characteristics

### Core Properties

1. **Key Type**: All keys must be strings
2. **Value Type Constraints**: Values must be either:
    - One of the types specified in the type parameter array `T`
    - A nested `TypedDict` with the same type constraints
3. **Structural**: Supports arbitrary nesting depth while maintaining type constraints
4. **Zero Runtime Overhead**: Pure TypeScript type with no runtime implementation cost

### Behavior

- Uses native JavaScript object access patterns
- No custom methods or properties
- Purely a compile-time type constraint

## Usage Patterns

### Creation

```typescript
type AllowedTypes = [string, number, boolean];

const config: TypedDict<AllowedTypes> = {
  server: {
    port: 8080,
    host: "localhost"
  }
};
```

### Access

```typescript
// Direct property access
const port = config.server.port;

// Dynamic property access
const propName = "port";
const value = config.server[propName];
```

### Modification

```typescript
// Direct property assignment
config.server.timeout = 5000;

// Adding nested structures
config.logging = {
  level: "debug",
  enabled: true
};
```

## Type Safety Guarantees

The `TypedDict<T>` type ensures:

1. Values can only be of types specified in `T` or nested dictionaries
2. This constraint is applied recursively to all nested dictionaries
3. TypeScript will raise compilation errors for invalid assignments

## Limitations

1. No runtime type checking (TypeScript only)
2. Cannot use non-string keys
3. No special handling for methods or prototype inheritance
4. No built-in serialization/deserialization

## Example Extensions

While the core `TypedDict<T>` type is intentionally minimal, it can be extended for specific use cases:

```typescript
// Read-only dictionary
type ReadonlyTypedDict<T extends any[]> = Readonly<TypedDict<T>>;

// Dictionary with specific required keys
type ConfigDict<T extends any[]> = TypedDict<T> & {
  version: string;
  environment: string;
};
```

## Comparison to Similar Types

| Feature | TypedDict<T> | Record<K,V> | Map<K,V> | Plain Object |
|---------|-------------|-------------|----------|-------------|
| Nested value constraints | ✓ | ✗ | ✗ | ✗ |
| Custom access methods | ✗ | ✗ | ✓ | ✗ |
| Non-string keys | ✗ | ✓ | ✓ | ✗ |
| Runtime type checking | ✗ | ✗ | ✗ | ✗ |
| Native syntax | ✓ | ✓ | ✗ | ✓ |

## Performance Considerations

As `TypedDict<T>` is purely a TypeScript type with no runtime implementation, it has zero performance overhead compared to using plain JavaScript objects.

