# Spec Pragma Types

## Industry Standard Type Definitions

### OpenAPI Schema Support
```typescript
// Based on OpenAPI 3.0 specification
import type { OpenAPIV3 } from 'openapi-types';

interface OpenAPIComponentSpec {
  openapi: '3.0.0' | '3.0.1' | '3.0.2' | '3.0.3';
  info: OpenAPIV3.InfoObject;
  components: {
    schemas: Record<string, OpenAPIV3.SchemaObject>;
  };
}
```

### JSON Schema Support
```typescript
// Based on JSON Schema Draft 2020-12
import type { JSONSchema7 } from 'json-schema';

interface JSONSchemaSpec extends JSONSchema7 {
  $schema: 'https://json-schema.org/draft/2020-12/schema';
  type: 'object';
  properties: Record<string, JSONSchema7>;
  required?: string[];
}
```

### Gherkin BDD Support
```typescript
// Based on Cucumber Gherkin syntax
interface GherkinFeature {
  feature: {
    name: string;
    description?: string;
    scenarios: GherkinScenario[];
  };
}

interface GherkinScenario {
  name: string;
  steps: GherkinStep[];
}

interface GherkinStep {
  keyword: 'Given' | 'When' | 'Then' | 'And' | 'But';
  text: string;
  docString?: string;
  dataTable?: string[][];
}
```

### Core Spec Pragma Interface
```typescript
interface SpecPragma {
  parseOpenAPI(spec: OpenAPIComponentSpec): ParsedSpec;
  parseJSONSchema(schema: JSONSchemaSpec): ParsedSpec;
  parseGherkin(feature: GherkinFeature): ParsedSpec;
  generateTypeScript(spec: ParsedSpec): string;
  generateVitest(spec: ParsedSpec): string;
  generateStorybook(spec: ParsedSpec): string;
}
```

### SemanticStructure
```typescript
interface SemanticStructure {
  description: string;
  props: PropSpecification[];
  behavior: BehaviorSpecification[];
  performance: PerformanceSpecification[];
  examples: ExampleSpecification[];
  dependencies: DependencySpecification[];
  metadata: SpecMetadata;
}
```

### PropSpecification
```typescript
interface PropSpecification {
  name: string;
  type: string;
  required: boolean;
  default?: any;
  description?: string;
  validation?: ValidationRule[];
  scopeReference?: string;
}
```

### BehaviorSpecification
```typescript
interface BehaviorSpecification {
  description: string;
  trigger: string;
  action: string;
  expected: string;
  testCase: boolean;
  priority: 'low' | 'normal' | 'high';
}
```

### PerformanceSpecification
```typescript
interface PerformanceSpecification {
  requirement: string;
  metric: string;
  threshold: number | string;
  optimization: string[];
  measurement: string;
}
```

## Format Types

### SpecFormat
```typescript
enum SpecFormat {
  NATURAL_LANGUAGE = 'natural',
  STRUCTURED_YAML = 'structured',
  MINIMAL_SHORTHAND = 'minimal',
  GHERKIN_BDD = 'gherkin',
  JSON_SCHEMA = 'json'
}
```

### GenerationTarget
```typescript
enum GenerationTarget {
  IMPLEMENTATION = 'implementation',
  TESTS = 'tests',
  TYPES = 'types',
  DOCUMENTATION = 'docs',
  ALL = 'all'
}
```

## Result Types

### GeneratedArtifact
```typescript
interface GeneratedArtifact {
  code: string;
  target: GenerationTarget;
  dependencies: string[];
  metadata: GenerationMetadata;
  validation: ValidationResult;
}
```

### SyncResult
```typescript
interface SyncResult {
  updatedSpecification?: string;
  updatedImplementation?: string;
  conflicts: ConflictReport[];
  changesSummary: ChangesSummary;
  success: boolean;
}
```

### ComposedSpec
```typescript
interface ComposedSpec {
  spec: SpecPragma;
  composedPragmas: Pragma[];
  integrationPoints: IntegrationPoint[];
  kernelIntegration: KernelIntegration;
  metadata: CompositionMetadata;
}
```

## Integration Types

### KernelIntegration
```typescript
interface KernelIntegration {
  taskScheduling: boolean;
  priorityManagement: boolean;
  resourceAllocation: boolean;
  errorRecovery: boolean;
}
```

### IntegrationPoint
```typescript
interface IntegrationPoint {
  pragma: string;
  interface: string;
  dependencies: string[];
  compatibility: CompatibilityLevel;
}
```

### CompatibilityLevel
```typescript
enum CompatibilityLevel {
  FULL = 'full',
  PARTIAL = 'partial',
  REQUIRES_ADAPTATION = 'adaptation',
  INCOMPATIBLE = 'incompatible'
}
```
