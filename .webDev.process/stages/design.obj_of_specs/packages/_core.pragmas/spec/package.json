{"name": "@spicetime/spec-pragma", "version": "1.0.0", "description": "Transform industry-standard specifications into TypeScript implementation code, tests, and documentation", "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}}, "scripts": {"build": "tsc", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit", "dev": "tsc --watch"}, "keywords": ["specification", "openapi", "json-schema", "g<PERSON>kin", "typescript", "code-generation", "testing", "react", "pragma", "spicetime"], "author": "SpiceTime Architecture", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/spicetime/architecture.git", "directory": "packages/spec-pragma"}, "engines": {"node": ">=18.0.0"}, "files": ["dist", "README.md", "package.json"]}