# Docker Configuration for Spec Pragma

## Root Docker Configuration Modifications

### Add to docker-compose.dev.yml

**Spec pragma development service:**

```yaml
services:
  spec-pragma-dev:
    build:
      context: .
      dockerfile: packages/_pragmas.hDict/spec/Dockerfile.dev
    volumes:
      - ./packages/_pragmas.hDict/spec:/app/packages/spec-pragma
      - /app/node_modules
      - /app/packages/spec-pragma/node_modules
    environment:
      - NODE_ENV=development
    command: pnpm run dev
    depends_on:
      - base-dev
```

### Add to docker-compose.test.yml

**Spec pragma testing service:**

```yaml
services:
  spec-pragma-test:
    build:
      context: .
      dockerfile: packages/_pragmas.hDict/spec/Dockerfile.test
    volumes:
      - ./packages/_pragmas.hDict/spec:/app/packages/spec-pragma
      - /app/node_modules
    environment:
      - NODE_ENV=test
      - CI=true
    command: pnpm run test:coverage
    depends_on:
      - base-test
```

## Local Docker Configuration

### Dockerfile.dev
```dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy root package files
COPY package.json pnpm-lock.yaml ./
COPY packages/_pragmas.hDict/spec/package.json ./packages/spec-pragma/

# Install dependencies (hoisted to root)
RUN corepack enable pnpm
RUN pnpm install --frozen-lockfile

# Copy spec pragma source
COPY packages/_pragmas.hDict/spec ./packages/spec-pragma

WORKDIR /app/packages/spec-pragma

EXPOSE 3000

CMD ["pnpm", "run", "dev"]
```

### Dockerfile.test
```dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy root package files
COPY package.json pnpm-lock.yaml ./
COPY packages/_pragmas.hDict/spec/package.json ./packages/spec-pragma/

# Install dependencies
RUN corepack enable pnpm
RUN pnpm install --frozen-lockfile

# Copy spec pragma source and tests
COPY packages/_pragmas.hDict/spec ./packages/spec-pragma

WORKDIR /app/packages/spec-pragma

CMD ["pnpm", "run", "test:coverage"]
```

### .dockerignore
```
node_modules
dist
coverage
*.log
.env
.env.local
.DS_Store
```

## Integration with Existing Docker Setup

### Reuse Base Images
- Extends existing Docker configuration
- Shares base Node.js setup
- Consistent environment across services

### Volume Management
- Hoisted node_modules handling
- Development file watching
- Test isolation
