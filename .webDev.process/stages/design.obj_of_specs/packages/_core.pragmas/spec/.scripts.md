# Scripts Configuration for Spec Pragma

## Root package.json Script Modifications

### Add Spec Pragma Scripts

**Add to root package.json scripts section:**

```json
{
  "scripts": {
    "spec-pragma:build": "turbo run build --filter=spec-pragma",
    "spec-pragma:test": "turbo run test --filter=spec-pragma",
    "spec-pragma:test:watch": "turbo run test:watch --filter=spec-pragma",
    "spec-pragma:test:ui": "turbo run test:ui --filter=spec-pragma",
    "spec-pragma:lint": "turbo run lint --filter=spec-pragma",
    "spec-pragma:lint:fix": "turbo run lint:fix --filter=spec-pragma",
    "spec-pragma:type-check": "turbo run type-check --filter=spec-pragma",
    "spec-pragma:dev": "turbo run dev --filter=spec-pragma",
    "spec-pragma:clean": "turbo run clean --filter=spec-pragma"
  }
}
```

### Integration with Global Scripts

**Extend existing global scripts:**

```json
{
  "scripts": {
    "build": "turbo run build",
    "test": "turbo run test", 
    "lint": "turbo run lint",
    "type-check": "turbo run type-check",
    "clean": "turbo run clean"
  }
}
```

## Local Scripts Configuration

### Local package.json Scripts
```json
{
  "scripts": {
    "build": "tsc",
    "test": "vitest run",
    "test:watch": "vitest",
    "test:ui": "vitest --ui",
    "test:coverage": "vitest run --coverage",
    "lint": "eslint src --ext .ts,.tsx",
    "lint:fix": "eslint src --ext .ts,.tsx --fix",
    "type-check": "tsc --noEmit",
    "dev": "tsc --watch",
    "clean": "rimraf dist coverage"
  }
}
```

## Development Workflow Scripts

### Quick Development Commands

**From root directory:**
```bash
# Start spec pragma development
pnpm spec-pragma:dev

# Run spec pragma tests
pnpm spec-pragma:test

# Run all pragma tests with UI
pnpm spec-pragma:test:ui

# Lint and fix spec pragma
pnpm spec-pragma:lint:fix

# Type check spec pragma
pnpm spec-pragma:type-check
```

### CI/CD Integration Scripts

**For continuous integration:**
```bash
# Full build and test pipeline
pnpm build && pnpm test && pnpm lint

# Spec pragma specific CI
pnpm spec-pragma:build && pnpm spec-pragma:test && pnpm spec-pragma:lint
```

## Script Dependencies

### Build Dependencies
- TypeScript compilation
- Type checking
- Asset bundling

### Test Dependencies  
- Vitest test runner
- Coverage reporting
- UI test interface

### Lint Dependencies
- ESLint with TypeScript rules
- Automatic fixing
- Code formatting

## Integration with Existing Tooling

### Turbo Integration
- Leverages existing Turbo configuration
- Parallel execution with other packages
- Intelligent caching and dependencies

### PNPM Integration
- Uses workspace filtering
- Hoisted dependency management
- Consistent with existing setup
