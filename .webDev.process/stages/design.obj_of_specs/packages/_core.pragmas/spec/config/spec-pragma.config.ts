/**
 * Main runtime configuration for Spec Pragma
 * 
 * Controls the overall behavior of spec-to-code transformation
 */

export interface SpecPragmaConfig {
  // Parser configuration
  parsers: {
    openapi: {
      version: '3.0.3';
      strictValidation: boolean;
      allowExtensions: boolean;
    };
    jsonSchema: {
      draft: '2020-12';
      strictMode: boolean;
      allowUnknownFormats: boolean;
    };
    gherkin: {
      language: string;
      strictSyntax: boolean;
      allowTables: boolean;
    };
  };

  // Generator configuration
  generators: {
    typescript: {
      target: 'ES2022';
      module: 'ESNext';
      strict: boolean;
      generateInterfaces: boolean;
      generateTypes: boolean;
    };
    vitest: {
      framework: 'vitest';
      testingLibrary: '@testing-library/react';
      coverage: boolean;
      mocking: 'vi' | 'jest';
    };
    storybook: {
      format: 'CSF';
      version: '7.x';
      generateDocs: boolean;
      generateControls: boolean;
    };
  };

  // Template configuration
  templates: {
    typescript: {
      componentTemplate: string;
      hooksTemplate: string;
      typesTemplate: string;
    };
    vitest: {
      unitTestTemplate: string;
      integrationTestTemplate: string;
      mockTemplate: string;
    };
    storybook: {
      storyTemplate: string;
      docsTemplate: string;
    };
  };

  // Output configuration
  output: {
    directory: string;
    fileNaming: {
      component: string; // e.g., '{name}.tsx'
      test: string;      // e.g., '{name}.test.tsx'
      story: string;     // e.g., '{name}.stories.tsx'
      types: string;     // e.g., '{name}.types.ts'
    };
    formatting: {
      prettier: boolean;
      eslint: boolean;
      organizeImports: boolean;
    };
  };

  // Sync configuration
  sync: {
    bidirectional: boolean;
    conflictResolution: 'manual' | 'spec-wins' | 'code-wins';
    preserveCustomCode: boolean;
    backupBeforeSync: boolean;
  };

  // Integration configuration
  integration: {
    kernel: {
      enabled: boolean;
      priority: 'low' | 'normal' | 'high';
      timeout: number;
    };
    pragmas: {
      foundation: string[]; // ['obj', 'prop', 'ttree', 'context']
      react: string[];      // ['component', 'hook', 'provider']
      state: string[];      // ['provider', 'context']
    };
  };
}

export const defaultConfig: SpecPragmaConfig = {
  parsers: {
    openapi: {
      version: '3.0.3',
      strictValidation: true,
      allowExtensions: true
    },
    jsonSchema: {
      draft: '2020-12',
      strictMode: true,
      allowUnknownFormats: false
    },
    gherkin: {
      language: 'en',
      strictSyntax: true,
      allowTables: true
    }
  },

  generators: {
    typescript: {
      target: 'ES2022',
      module: 'ESNext',
      strict: true,
      generateInterfaces: true,
      generateTypes: true
    },
    vitest: {
      framework: 'vitest',
      testingLibrary: '@testing-library/react',
      coverage: true,
      mocking: 'vi'
    },
    storybook: {
      format: 'CSF',
      version: '7.x',
      generateDocs: true,
      generateControls: true
    }
  },

  templates: {
    typescript: {
      componentTemplate: 'templates/typescript/component.hbs',
      hooksTemplate: 'templates/typescript/hooks.hbs',
      typesTemplate: 'templates/typescript/types.hbs'
    },
    vitest: {
      unitTestTemplate: 'templates/vitest/unit.hbs',
      integrationTestTemplate: 'templates/vitest/integration.hbs',
      mockTemplate: 'templates/vitest/mock.hbs'
    },
    storybook: {
      storyTemplate: 'templates/storybook/story.hbs',
      docsTemplate: 'templates/storybook/docs.hbs'
    }
  },

  output: {
    directory: './generated',
    fileNaming: {
      component: '{name}.tsx',
      test: '{name}.test.tsx',
      story: '{name}.stories.tsx',
      types: '{name}.types.ts'
    },
    formatting: {
      prettier: true,
      eslint: true,
      organizeImports: true
    }
  },

  sync: {
    bidirectional: true,
    conflictResolution: 'manual',
    preserveCustomCode: true,
    backupBeforeSync: true
  },

  integration: {
    kernel: {
      enabled: true,
      priority: 'normal',
      timeout: 30000
    },
    pragmas: {
      foundation: ['obj', 'prop', 'ttree', 'context'],
      react: ['component', 'hook', 'provider'],
      state: ['provider', 'context']
    }
  }
};
