/**
 * Generator configuration for code generation
 * 
 * Controls how TypeScript, Vitest, and Storybook code is generated
 */

export interface GeneratorsConfig {
  typescript: TypeScriptGeneratorConfig;
  vitest: VitestGeneratorConfig;
  storybook: StorybookGeneratorConfig;
}

export interface TypeScriptGeneratorConfig {
  // Compiler options
  compiler: {
    target: string;           // 'ES2022'
    module: string;           // 'ESNext'
    moduleResolution: string; // 'bundler'
    strict: boolean;
    exactOptionalPropertyTypes: boolean;
    noUncheckedIndexedAccess: boolean;
  };

  // Code generation
  generation: {
    generateInterfaces: boolean;
    generateTypes: boolean;
    generateEnums: boolean;
    generateUnions: boolean;
    generateComments: boolean;
  };

  // React specific
  react: {
    version: string;          // '18.x'
    importStyle: 'named' | 'default' | 'namespace';
    memo: boolean;
    forwardRef: boolean;
    displayName: boolean;
  };

  // Import management
  imports: {
    organizeImports: boolean;
    removeUnusedImports: boolean;
    sortImports: boolean;
    groupImports: boolean;
  };

  // Formatting
  formatting: {
    prettier: boolean;
    eslint: boolean;
    semicolons: boolean;
    quotes: 'single' | 'double';
    trailingComma: 'none' | 'es5' | 'all';
  };
}

export interface VitestGeneratorConfig {
  // Test framework
  framework: {
    runner: 'vitest';
    testingLibrary: '@testing-library/react';
    userEvents: '@testing-library/user-event';
    jsdom: boolean;
  };

  // Test generation
  generation: {
    unitTests: boolean;
    integrationTests: boolean;
    e2eTests: boolean;
    visualTests: boolean;
  };

  // Mocking
  mocking: {
    framework: 'vi' | 'jest';
    autoMock: boolean;
    mockModules: boolean;
    mockFunctions: boolean;
  };

  // Coverage
  coverage: {
    enabled: boolean;
    provider: 'v8' | 'istanbul';
    threshold: {
      statements: number;
      branches: number;
      functions: number;
      lines: number;
    };
    exclude: string[];
  };

  // Assertions
  assertions: {
    matchers: 'vitest' | 'jest-dom';
    customMatchers: boolean;
    asyncMatchers: boolean;
  };
}

export interface StorybookGeneratorConfig {
  // Storybook version
  version: {
    major: string;           // '7.x'
    format: 'CSF' | 'MDX';
    typescript: boolean;
  };

  // Story generation
  stories: {
    generateDefault: boolean;
    generateVariants: boolean;
    generateDocs: boolean;
    generateControls: boolean;
  };

  // Documentation
  docs: {
    autodocs: boolean;
    mdx: boolean;
    description: boolean;
    argTypes: boolean;
  };

  // Controls
  controls: {
    generateControls: boolean;
    inferControls: boolean;
    customControls: boolean;
  };

  // Addons
  addons: {
    essentials: boolean;
    a11y: boolean;
    viewport: boolean;
    backgrounds: boolean;
    actions: boolean;
  };
}

export const defaultGeneratorsConfig: GeneratorsConfig = {
  typescript: {
    compiler: {
      target: 'ES2022',
      module: 'ESNext',
      moduleResolution: 'bundler',
      strict: true,
      exactOptionalPropertyTypes: true,
      noUncheckedIndexedAccess: true
    },
    generation: {
      generateInterfaces: true,
      generateTypes: true,
      generateEnums: true,
      generateUnions: true,
      generateComments: true
    },
    react: {
      version: '18.x',
      importStyle: 'named',
      memo: true,
      forwardRef: true,
      displayName: true
    },
    imports: {
      organizeImports: true,
      removeUnusedImports: true,
      sortImports: true,
      groupImports: true
    },
    formatting: {
      prettier: true,
      eslint: true,
      semicolons: true,
      quotes: 'single',
      trailingComma: 'es5'
    }
  },

  vitest: {
    framework: {
      runner: 'vitest',
      testingLibrary: '@testing-library/react',
      userEvents: '@testing-library/user-event',
      jsdom: true
    },
    generation: {
      unitTests: true,
      integrationTests: true,
      e2eTests: false,
      visualTests: false
    },
    mocking: {
      framework: 'vi',
      autoMock: false,
      mockModules: true,
      mockFunctions: true
    },
    coverage: {
      enabled: true,
      provider: 'v8',
      threshold: {
        statements: 80,
        branches: 80,
        functions: 80,
        lines: 80
      },
      exclude: [
        'node_modules/',
        'dist/',
        '**/*.d.ts',
        '**/*.config.*',
        '**/*.stories.*'
      ]
    },
    assertions: {
      matchers: 'vitest',
      customMatchers: true,
      asyncMatchers: true
    }
  },

  storybook: {
    version: {
      major: '7.x',
      format: 'CSF',
      typescript: true
    },
    stories: {
      generateDefault: true,
      generateVariants: true,
      generateDocs: true,
      generateControls: true
    },
    docs: {
      autodocs: true,
      mdx: false,
      description: true,
      argTypes: true
    },
    controls: {
      generateControls: true,
      inferControls: true,
      customControls: false
    },
    addons: {
      essentials: true,
      a11y: true,
      viewport: true,
      backgrounds: true,
      actions: true
    }
  }
};
