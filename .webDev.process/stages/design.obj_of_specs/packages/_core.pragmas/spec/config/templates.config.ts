/**
 * Template configuration for code generation
 * 
 * Controls which templates are used for different code generation scenarios
 */

export interface TemplatesConfig {
  typescript: TypeScriptTemplatesConfig;
  vitest: VitestTemplatesConfig;
  storybook: StorybookTemplatesConfig;
  shared: SharedTemplatesConfig;
}

export interface TypeScriptTemplatesConfig {
  // Component templates
  components: {
    functional: string;       // 'templates/typescript/functional-component.hbs'
    class: string;           // 'templates/typescript/class-component.hbs'
    memo: string;            // 'templates/typescript/memo-component.hbs'
    forwardRef: string;      // 'templates/typescript/forward-ref-component.hbs'
  };

  // Hook templates
  hooks: {
    useState: string;        // 'templates/typescript/use-state-hook.hbs'
    useEffect: string;       // 'templates/typescript/use-effect-hook.hbs'
    useContext: string;      // 'templates/typescript/use-context-hook.hbs'
    custom: string;          // 'templates/typescript/custom-hook.hbs'
  };

  // Type templates
  types: {
    interface: string;       // 'templates/typescript/interface.hbs'
    type: string;           // 'templates/typescript/type.hbs'
    enum: string;           // 'templates/typescript/enum.hbs'
    union: string;          // 'templates/typescript/union.hbs'
  };

  // Provider templates
  providers: {
    context: string;         // 'templates/typescript/context-provider.hbs'
    state: string;          // 'templates/typescript/state-provider.hbs'
    compound: string;       // 'templates/typescript/compound-provider.hbs'
  };
}

export interface VitestTemplatesConfig {
  // Test templates
  tests: {
    unit: string;           // 'templates/vitest/unit-test.hbs'
    integration: string;    // 'templates/vitest/integration-test.hbs'
    component: string;      // 'templates/vitest/component-test.hbs'
    hook: string;          // 'templates/vitest/hook-test.hbs'
  };

  // Mock templates
  mocks: {
    module: string;         // 'templates/vitest/module-mock.hbs'
    function: string;       // 'templates/vitest/function-mock.hbs'
    component: string;      // 'templates/vitest/component-mock.hbs'
    api: string;           // 'templates/vitest/api-mock.hbs'
  };

  // Setup templates
  setup: {
    testSetup: string;      // 'templates/vitest/test-setup.hbs'
    globalSetup: string;    // 'templates/vitest/global-setup.hbs'
    mockSetup: string;      // 'templates/vitest/mock-setup.hbs'
  };

  // Assertion templates
  assertions: {
    render: string;         // 'templates/vitest/render-assertion.hbs'
    interaction: string;    // 'templates/vitest/interaction-assertion.hbs'
    state: string;         // 'templates/vitest/state-assertion.hbs'
    async: string;         // 'templates/vitest/async-assertion.hbs'
  };
}

export interface StorybookTemplatesConfig {
  // Story templates
  stories: {
    default: string;        // 'templates/storybook/default-story.hbs'
    variant: string;        // 'templates/storybook/variant-story.hbs'
    interactive: string;    // 'templates/storybook/interactive-story.hbs'
    docs: string;          // 'templates/storybook/docs-story.hbs'
  };

  // Meta templates
  meta: {
    component: string;      // 'templates/storybook/component-meta.hbs'
    page: string;          // 'templates/storybook/page-meta.hbs'
    layout: string;        // 'templates/storybook/layout-meta.hbs'
  };

  // Documentation templates
  docs: {
    mdx: string;           // 'templates/storybook/component-docs.mdx.hbs'
    description: string;    // 'templates/storybook/description.hbs'
    argTypes: string;      // 'templates/storybook/arg-types.hbs'
  };

  // Control templates
  controls: {
    text: string;          // 'templates/storybook/text-control.hbs'
    boolean: string;       // 'templates/storybook/boolean-control.hbs'
    select: string;        // 'templates/storybook/select-control.hbs'
    object: string;        // 'templates/storybook/object-control.hbs'
  };
}

export interface SharedTemplatesConfig {
  // Common templates
  common: {
    header: string;         // 'templates/shared/file-header.hbs'
    imports: string;        // 'templates/shared/imports.hbs'
    exports: string;        // 'templates/shared/exports.hbs'
    comments: string;       // 'templates/shared/comments.hbs'
  };

  // Utility templates
  utilities: {
    typeGuard: string;      // 'templates/shared/type-guard.hbs'
    validator: string;      // 'templates/shared/validator.hbs'
    transformer: string;   // 'templates/shared/transformer.hbs'
  };
}

export const defaultTemplatesConfig: TemplatesConfig = {
  typescript: {
    components: {
      functional: 'templates/typescript/functional-component.hbs',
      class: 'templates/typescript/class-component.hbs',
      memo: 'templates/typescript/memo-component.hbs',
      forwardRef: 'templates/typescript/forward-ref-component.hbs'
    },
    hooks: {
      useState: 'templates/typescript/use-state-hook.hbs',
      useEffect: 'templates/typescript/use-effect-hook.hbs',
      useContext: 'templates/typescript/use-context-hook.hbs',
      custom: 'templates/typescript/custom-hook.hbs'
    },
    types: {
      interface: 'templates/typescript/interface.hbs',
      type: 'templates/typescript/type.hbs',
      enum: 'templates/typescript/enum.hbs',
      union: 'templates/typescript/union.hbs'
    },
    providers: {
      context: 'templates/typescript/context-provider.hbs',
      state: 'templates/typescript/state-provider.hbs',
      compound: 'templates/typescript/compound-provider.hbs'
    }
  },

  vitest: {
    tests: {
      unit: 'templates/vitest/unit-test.hbs',
      integration: 'templates/vitest/integration-test.hbs',
      component: 'templates/vitest/component-test.hbs',
      hook: 'templates/vitest/hook-test.hbs'
    },
    mocks: {
      module: 'templates/vitest/module-mock.hbs',
      function: 'templates/vitest/function-mock.hbs',
      component: 'templates/vitest/component-mock.hbs',
      api: 'templates/vitest/api-mock.hbs'
    },
    setup: {
      testSetup: 'templates/vitest/test-setup.hbs',
      globalSetup: 'templates/vitest/global-setup.hbs',
      mockSetup: 'templates/vitest/mock-setup.hbs'
    },
    assertions: {
      render: 'templates/vitest/render-assertion.hbs',
      interaction: 'templates/vitest/interaction-assertion.hbs',
      state: 'templates/vitest/state-assertion.hbs',
      async: 'templates/vitest/async-assertion.hbs'
    }
  },

  storybook: {
    stories: {
      default: 'templates/storybook/default-story.hbs',
      variant: 'templates/storybook/variant-story.hbs',
      interactive: 'templates/storybook/interactive-story.hbs',
      docs: 'templates/storybook/docs-story.hbs'
    },
    meta: {
      component: 'templates/storybook/component-meta.hbs',
      page: 'templates/storybook/page-meta.hbs',
      layout: 'templates/storybook/layout-meta.hbs'
    },
    docs: {
      mdx: 'templates/storybook/component-docs.mdx.hbs',
      description: 'templates/storybook/description.hbs',
      argTypes: 'templates/storybook/arg-types.hbs'
    },
    controls: {
      text: 'templates/storybook/text-control.hbs',
      boolean: 'templates/storybook/boolean-control.hbs',
      select: 'templates/storybook/select-control.hbs',
      object: 'templates/storybook/object-control.hbs'
    }
  },

  shared: {
    common: {
      header: 'templates/shared/file-header.hbs',
      imports: 'templates/shared/imports.hbs',
      exports: 'templates/shared/exports.hbs',
      comments: 'templates/shared/comments.hbs'
    },
    utilities: {
      typeGuard: 'templates/shared/type-guard.hbs',
      validator: 'templates/shared/validator.hbs',
      transformer: 'templates/shared/transformer.hbs'
    }
  }
};
