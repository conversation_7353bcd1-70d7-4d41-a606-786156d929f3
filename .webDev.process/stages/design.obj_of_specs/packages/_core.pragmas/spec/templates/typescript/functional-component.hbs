{{!-- 
Handlebars template for generating functional React components
Used by TypeScript generator when creating components from specifications
--}}
{{> shared/file-header }}

import React{{#if config.react.memo}}, { memo }{{/if}}{{#if config.react.forwardRef}}, { forwardRef }{{/if}} from 'react';
{{#each imports}}
import {{#if default}}{{default}}{{/if}}{{#if named}}{ {{join named ', '}} }{{/if}} from '{{module}}';
{{/each}}

{{> shared/comments description=component.description }}

{{#if types.props}}
{{> typescript/interface name=types.props.name properties=types.props.properties }}
{{/if}}

{{#if config.react.memo}}
const {{component.name}} = memo{{#if config.react.forwardRef}}<{{types.props.name}}>{{/if}}({{#if config.react.forwardRef}}forwardRef<{{types.props.name}}, {{types.ref.type}}>({{/if}}({{#if types.props}}{ 
  {{#each types.props.properties}}
  {{name}}{{#unless required}}?{{/unless}},
  {{/each}}
}{{#if config.react.forwardRef}}, ref{{/if}}{{else}}props{{/if}}: {{types.props.name}}) => {
{{else}}
const {{component.name}}{{#if config.react.forwardRef}} = forwardRef<{{types.ref.type}}, {{types.props.name}}>{{/if}}({{#if types.props}}{ 
  {{#each types.props.properties}}
  {{name}}{{#unless required}}?{{/unless}},
  {{/each}}
}{{#if config.react.forwardRef}}, ref{{/if}}{{else}}props{{/if}}: {{types.props.name}}) => {
{{/if}}

{{#each hooks}}
  {{> typescript/hook-statement hook=this }}
{{/each}}

{{#each refs}}
  {{> typescript/ref-statement ref=this }}
{{/each}}

{{#if behavior.conditionalRendering}}
  {{#each behavior.conditionalRendering}}
  if ({{condition}}) {
    return {{returnValue}};
  }
  {{/each}}
{{/if}}

  return (
    {{#if jsx.wrapper}}
    <{{jsx.wrapper.tag}}{{#if jsx.wrapper.props}} {{> shared/props-string props=jsx.wrapper.props}}{{/if}}>
    {{/if}}
    {{#each jsx.elements}}
      {{> typescript/jsx-element element=this depth=@index }}
    {{/each}}
    {{#if jsx.wrapper}}
    </{{jsx.wrapper.tag}}>
    {{/if}}
  );
}{{#if config.react.forwardRef}}){{/if}}{{#if config.react.memo}}){{/if}};

{{#if config.react.displayName}}
{{component.name}}.displayName = '{{component.name}}';
{{/if}}

export default {{component.name}};

{{#if types.props}}
export type { {{types.props.name}} };
{{/if}}
