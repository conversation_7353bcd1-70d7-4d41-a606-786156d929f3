# Parse Pragma

## Overview

Parses industry-standard specification formats (OpenAPI, JSON Schema, <PERSON>herkin) into structured semantic representations for code generation.

## API Interface

```typescript
interface ParsePragma {
  parseOpenAPI(spec: OpenAPIV3.Document): Promise<ParsedSpec>;
  parseJSONSchema(schema: JSONSchema7): Promise<ParsedSpec>;
  parseGherkin(feature: GherkinDocument): Promise<ParsedSpec>;
  detectFormat(content: string): SpecFormat;
  validate(spec: ParsedSpec): ValidationResult;
}
```

## Supported Input Formats

### OpenAPI 3.0 Specifications
```yaml
# Standard OpenAPI component schema
openapi: 3.0.3
info:
  title: Component API
  version: 1.0.0
components:
  schemas:
    ComponentProps:
      type: object
      properties:
        # Component properties
```

### JSON Schema Draft 2020-12
```json
{
  "$schema": "https://json-schema.org/draft/2020-12/schema",
  "type": "object",
  "properties": {
    // Component properties
  }
}
```

### Gherkin BDD Features
```gherkin
Feature: Component Behavior
  Scenario: User interaction
    Given initial state
    When user action occurs
    Then expected outcome
```

## Processing Pipeline

### 1. Format Detection
```typescript
// Automatic format detection
const format = detectFormat(inputContent);
// Returns: 'openapi' | 'json-schema' | 'gherkin'
```

### 2. Schema Validation
```typescript
// Validate against format-specific schemas
const validation = await validateInput(inputContent, format);
if (!validation.valid) {
  throw new ValidationError(validation.errors);
}
```

### 3. Semantic Extraction
```typescript
// Extract semantic structure
const semantics = await extractSemantics(validatedInput, format);
// Returns structured representation for code generation
```

## Output Structure

```typescript
interface ParsedSpec {
  metadata: {
    format: SpecFormat;
    version: string;
    title: string;
  };
  components: ComponentSpec[];
  behaviors: BehaviorSpec[];
  types: TypeDefinition[];
  examples: ExampleSpec[];
}
```

## Error Handling

### Validation Errors
- Schema validation against OpenAPI/JSON Schema specifications
- Gherkin syntax validation using Cucumber parser
- Clear error messages with line numbers and suggestions

### Format Detection Failures
- Fallback to manual format specification
- Support for mixed-format documents
- Graceful degradation for partial specifications

## Integration

### Standard Parser Libraries
- **OpenAPI**: `@apidevtools/swagger-parser`
- **JSON Schema**: `ajv` with JSON Schema Draft 2020-12
- **Gherkin**: `@cucumber/gherkin-parser`

### TypeScript Integration
- Full TypeScript type safety
- Integration with `typescript` compiler API
- Support for existing TypeScript projects
