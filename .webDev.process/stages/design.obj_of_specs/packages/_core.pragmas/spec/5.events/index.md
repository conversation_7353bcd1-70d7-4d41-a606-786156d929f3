# Events Child Pragma

## Description

Defines and manages the event system for the spec pragma transformation pipeline. Events are child pragmas that specify the data structure and behavior for each event type emitted during specification processing.

## Event Child Pragmas

### Parse Events
- **1.beforeParse** - Emitted before specification parsing begins
- **2.afterParse** - Emitted after successful specification parsing  
- **3.parseError** - Emitted when specification parsing fails

### Generate Events
- **4.beforeGenerate** - Emitted before code generation begins
- **5.afterGenerate** - Emitted after successful code generation
- **6.generateError** - Emitted when code generation fails

### Sync Events
- **7.beforeSync** - Emitted before synchronization begins
- **8.afterSync** - Emitted after successful synchronization
- **9.syncConflict** - Emitted when synchronization conflicts occur

### Compose Events
- **10.beforeCompose** - Emitted before pragma composition begins
- **11.afterCompose** - Emitted after successful pragma composition

### Custom Events
- **12.specValidated** - Emitted when specification validation completes
- **13.codeGenerated** - Emitted when code generation artifacts are created
- **14.templatesLoaded** - Emitted when templates are loaded and ready

## Event Orchestration

Each event child pragma defines:
- **Event data schema** - Structure of event payload
- **Middleware capabilities** - What middleware can do with the event
- **Integration points** - How event connects to kernel and other services
- **Usage examples** - How to handle the event in middleware

## Event Flow

```
spec.pragma.execute()
├── events.1.beforeParse.emit()
├── parse.execute()
├── events.2.afterParse.emit()
├── events.4.beforeGenerate.emit()
├── generate.execute()
├── events.5.afterGenerate.emit()
├── sync.execute()
└── compose.execute()
```

## Middleware Integration

Events are the primary integration point for middleware:

```typescript
// Middleware registers for specific events
middleware.register({
  name: 'my-plugin',
  hooks: {
    beforeParse: 'handleBeforeParse',
    afterGenerate: 'handleAfterGenerate',
    syncConflict: 'handleSyncConflict'
  }
});
```

## Event Data Standards

All events follow the base event schema from `@spicetime/events`:

```yaml
BaseEvent:
  properties:
    id: { type: string, format: uuid }
    type: { type: string }
    timestamp: { type: number }
    source: { type: string }
    correlationId: { type: string }
    metadata: { type: object }
```

Each event child pragma extends this base with specific data structures for its use case.
