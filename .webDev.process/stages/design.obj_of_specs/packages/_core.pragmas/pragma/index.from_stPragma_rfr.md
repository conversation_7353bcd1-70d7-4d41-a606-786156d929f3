## We are building a validatable schema API system

stPragma is a system for building scopes and validatable schema APIs. It transforms file extensions and directory structures into a rich, typed API that enables declarative programming through filesystem structure. Each API is a validatable schema with runtime type checking and validation capabilities.

```
Root functors generate trees, by generating branch functors
They define morphisms and morphist transforms, for generating new branch functors
So, we will define how pragma packages are structured in general
And the common ops that build any pragma
then each pragma extends the stPragma
but in a way constrained by our transform morphisms
```

## lets consider example
of /Users/<USER>/WebstormProjects/spicetime-architecture/.webDev.process/stages/production.dict_of_structures/pragmas.dict_of_stPragmas/dict.pragma
- _.pragma file indicates its the local pragma def
  its private term, in this case - means available only to index.ts file
  index does all work of shaping and exporting of api
  and it does it based on pragmas
  so _.pragma.ts file exports a func that gets called in index.ts
  and that func returns an object that gets injected in index module as part of globalThis
  the type of that pragma func is defined and exposed in pragma.type.ts
- we following each file and folder, node, in each folder of fs. each file gets a spec, including a index.ts
  index creates api of a node, so we specing out api and how its constructed
  - a spec of a file has two parts
    - test spec - description of each test, as it would appear in it() part of a test
    - impl details - just what it says
  - we dont have to have a spec for each node. we can have extra files, as this context. and if we have specs for extra files
    that dont exist in impl stage, they will be created out of those specs. that is orchestrated by webdev process
    the name of design_stage.context indicates its not part of impl, as there are context files there and they have specific meaning
    and this file has different meaning, relates to the design stage
  - pragma.type and pragma.test are not protected or private, and would be visible in scope to children nodes, if there were any
    so, it simply dont matter what visibility scope we give them
  - dict_of_stPragmas, in parent name,is a pragma with a pragma arg, indicated by _of syntax.
  ```
    dict is a pragma we are defining in this example
    its typed as a generic that takes a list of types, list of node types that get ored for each node
    so, _of_stPragmas means each node is a typeof of stPragma
    means every node is a pragma with lineage to stPragma type
  ```

## lets define all child pragmas of stPragma
it has an index file if its a folder - thats not a pragmatic file. But, as part of stPragma impl, index file
builds and exports api of a node, and stPragma builds that api, by wrapping whatever index stub exports and feeding it to
pragma operator, defined in pragma child
```
so, we got first pragma child - pragma.child
and it starts getting confusing - we have stPragma children and we have children of pragmas its building, per its transform morphisms
so we will use alternative syntax of defining pragmas - thru a single _.pragma.st file
again, _ is just to assign visibility scope of private
. means theres an ext, pragmatic ext. and its pragma ext. that means its a pragma definition
and lack of name, prefix before . indicates it defines this very stPragma we are building
```
- inside _.pragma.st file
```
it exports a pragma operator used in index files of nodes, to produce exported api.
such as export default import('_.pragma)(...args)
the type of pragma func is defined in pragma.type.ts - again, dont fixate on _ or . prefix or lack of it. those are just visibility scopes

so, in case of stPragma, as the root pragma, we will actually implement the very func that does the job for every pragma that inherits
that means theres function composition involved
first, we define pragma profile object.

it has to be tyoed btw
then we build the profile for stPragma
then we feed that profile to pragmaOperator func
that builds the api of stPreagma

that stPragma api is to export whatever api the pragma profile defines/types - so, its a generic , the pragmaOperator
it takes the api type, which is part of profile, and these types are composed
```
- so, profile has instructions how to construct the scope of the node
thats one of functionalities of stPragma - to build the scope, to be extended by children that use their pragmas
- and thats where those visibility scopes are built
```
all files with wo _ prefixes, are in public scope
if they have _ prefix, they in private scope
```
  - both public abd private scopes are propagated thru structure, as scopes do.
  ```
  in private scope, _ prefixes are dropped off the names
  _ prefix, in both private and public scopes are innumerable terms

  . means protected, in oop parlance,
  its implemented by injecting only numerables out of both private and public scopes
  into the scripts as globals
  but then, innumerables of just local node are added
  so, innumerablkes dont propagate thru structure
  ```
- where those private and public scopes are used
```they are used in defferent scripts, marked by specific pragmas
private scope is injected into index file and .pragma nodes
.pragma nodes can be many - anything that has pragma ext
as it indicates some aspect of host node pragma definition
but only self pragma, without a name prefix, in front of first .
these private terms can be mapped on pragma props and pragma deps and pragma contexts
allowing reactive composition of pragmas, as well as prop driven composition

those private files and nodes do receive access to the public scope, as its inserted into
private scope as a scope namespace - they have a const scope as a global
```
## numerable and enumerable terms in scopes.
whats the use case
```numerables are a way for a parent node/package to control api to its children
thats internal/inward api, in a nested package structure
and what index exports is outward api

they are not necessarily the same, tho in simplest cases they might be
example is obj pragma. it collects all .prop nodes into an object
and if you make all .prop nodes numerable, every child downstream has access to the props directly thru scope
however, they can access the object itself thru parent node api, which is a term in scope, under its own name
name of the object

the difference is the binding of those props - the object, or the scope
that solves a lot of confusion about context and this and that, and eliminates, for most, the use of bind scorge

and puts control of that mechanism in hands of dev
he can arrange only needed props in scope, to minimize scope polution
this simplifies name management

and that goes for both private and public scopes
theres public and private api
```

## Term Validation Functions

Each term in the scope includes static validation functions:

```typescript
interface Term<T = any> {
  id: string;
  name: string;
  value: T;
  type: string;

  // Static validation functions
  static is(value: any): boolean;
  static validate(value: any): boolean;
}
```

- **is**: A static function returning a boolean that checks if a value is of the term's type
  ```typescript
  // Example for a component term
  static is(value: any): boolean {
    return typeof value === 'function' ||
           (typeof value === 'object' && value !== null && typeof value.render === 'function');
  }
  ```

- **validate**: A static function returning a boolean that performs deeper validation of a value
  ```typescript
  // Example for a component term
  static validate(value: any): boolean {
    if (!ComponentTerm.is(value)) {
      return false;
    }

    // Additional validation logic
    try {
      const propTypes = (value as any).propTypes || {};
      const requiredProps = Object.keys(propTypes).filter(prop =>
        propTypes[prop].isRequired
      );

      // A valid component should have documentation for required props
      return requiredProps.every(prop =>
        (value as any).__docgenInfo?.props?.[prop]?.description
      );
    } catch (error) {
      return false;
    }
  }
  ```

These validation functions enable type checking and validation of terms at runtime, complementing TypeScript's static type checking.

## what are public and private apis
- for internal api, looking downward at nested packages, its orchestrated thru numerable terms
in either private or public scope
- for external apis
  - public api is constructed by a public pragma, like described above
  - private api is very simple tho
  ```
  it is accessed by acessing terms in private scope, within index or .pragma nodes/files
  and what you get on access, is pragma of the node
  you get actual pragmaOperator func when a term is accessed
  and you get its type from types namespace in that private scope
  when namesake type is accessed

  so, private scope is a tool for pragma composition, primarily
  its a one stop shopping destination for anything pragma - pragmasRus
  pragmas can share contexts, declare deps, at different stages of webdev process
  get organized in compounds via context sharing patterns
  ```

  ## how pragma children pragmas are accessed
thru private scope,
```
by accessing terms in that scope,
which are same terms as in public scope, but expose private api
which is its pragma

and those pragmaOperator funcs have statics hanging off them
these are children pragmas
and yes, they can be parents as any other pragma
and yes they have statics
so, each term in scope is a deep structure of nested pragmas

but you dont have to drill thru those structures
and hit or miss, for who knows whats down there, hidden

we encourage a flat structure of pragmas,
similar to flat structure of react components
which directly map on pragma components

and, you get a duel syntax of access
thru flat structure of prototypical scope
or thru the logical structure of pragma hierarchy for each pragma

that addresses the problem of namesake pragmas shadowing each other in the scope
a shadowed name can bve accessed thru a hierarchy of one of its parents

or a chained pattern of access can inform on lineage
to showcase intent
```

## but how do pragmas compose
- first, how do they access any pragma available in repo fs
- what is about to be said relates to both private and public scopes and terms in them
  - in both scopes, upstream nodes are visible thru the layers of scope as numerable terms
  ```
  thats the internal, numerable, api of parent nodes
  the innumerable, protected terms are not for public use, sorry
  ```
  - what about pragmas that are defined by nodes in parallel branches, not accessible thru js scoping rules
  ```
  thats when a package needs to declare local deps, in its package.json,
  and make it available to all its children downscope

  but it does not done have to be done thru package.json directly
   *.deps file is synched to package.json - whatever is declared in in one is matched in the other
  there are dev.deps and prod.deps and porbly other stages of webdev process in addition to traditional
  thats for public deps and for protected deps

  let me xplained why it matters
  deps form a sopespace in scopes - private or public, depending on presence of _ prefix
  and they propagate downscope

  but for private deps, which are pragma deps
  we dont have a package json - instead, its _.pragma.ts file
  where a deps namespace can be exported as part of default pragma export/profile
  or it can be a _.deps node, that exports that same namespace

  what about .deps, or someName.deps and _someName.deps
  .deps is the public deps, but not specifying which stage - dev or prod. has to be done inside,
  by namespacing.
  someName.deps better be dev.deps or prod.deps
  otherwise youll get a weird record in package.json
  _someName.deps means you are defining  some other pragma then yopur own, with someName
  thats fine and useful - a way to define pragmas common to downstream children
  and expose them thru scope, rather then thru deps or complicated access patterns
  ```

- now that we can access any pragma porev defined by any node in fs, or in some pragma branch of fs structure,
how do we actually compose pragmaOperator functions
```
by definition, pragmas take local node public scope as input
and produce node api as return
the node scope is typed, as its arg
and so is the api it returns - thats a func type

so, we compose them by actually coding a normal function composition
there are several patterns of composition

chaining
lets consider a simple example - obj prop chain
this is something that will be used as linguistic sequence of pathname exts
means prop of an object
so, obj pragma builds an object out of child nodes that have prop pragma in their exts
thats what it spits out
but prop pragma runs first, as the pipe processes from tail end
but, by the time obj.prop pragma runs on child node, the oparent obj pragma already ran
and it placed output of parent node, under its name in scope, as an object
that can accept props, as they jump in from children, like flees on a dog (you better use it in actual doc wo me yelling at you)
so, prop pragma is designed to find its dog ... whoops, object under the name of the parent in scope
like we said above - it has access to scope namespace, as the public scope, in its global object
and it tries to insert itself into the parent object
but it does it by some proxy mechanism, for that prop is a returned by a getter wrapper of that prop node, in scope
so, it needs to be a getter in the object, cos we are building a func structure
to be invoked later

i want this example chewed and almost digested, as presented in the docs
its crucial to understand the exact mechanics of this func composition

and thats where ts type inference will be excesized
ts will generate an error, if that prop is not of correct type, if lets say the parent is a dict, that does not list
the type of that prop in its generic props. maybe, its allergic to strings
that will throw an error
and our webdev process will have to get involved
in the family dispute, and its not a babysitter
but the dev is - so it marks the prop child node with a red marker
in the project window

next pattern is mixing
we simply run several pragmas on the same public scope
get several apis returned, and mix those apis
thats kinda what chaining does in the prev example,
but i guess, the dif is prop uses the scope just to pick what the prev pragma spit out - the object
thats the composition chain of compose func in ramda - a pipe

and another pattern is inheritance
that can be viewed as an HOC pattern of evolving components in react
a pragma can be a class, for example, or evolving the scope, or its internal structure
i dont have the use case, so that has to wait

actually, i do have a use case
we will map react over pragma components
per concept /Users/<USER>/WebstormProjects/spicetime-architecture/.webDev.process/stages/ideation.order_of_concepts/7.fs_css,react_over_pragmas,thematicd_app_structure
but thats a separate concern
```

## contexts, what are they and what for, and how does stPragma handle them
we already described how contexts work, in other specs and concepts.
theres a lot that can be gotten on that subject from this concept
/Users/<USER>/WebstormProjects/spicetime-architecture/.webDev.process/stages/ideation.order_of_concepts/8.dymnamic_rusted_pragmas,sgared_across_net,security_impl,community_solution/context.md

- let me explain how contexts are orchestrated
```
thru scopes - either private or public
a *.context node utoilizers context pragma
context pragma operates on the scope supplied to it
either public if no _ prefix
or private if theres

that creates context available either t pragmas only or the public nodes
same usual visibility rules apply to . prefix
if present, itrs protected and does not propagate passed local branch of scope
otherwise, available to all downstream
in either scope - thats defined by _ prefix

what context pragma does
pases the context of the node specified by the pragma prop
as in .context_of_somePackageName
lets the script modify that context, as exampled in that concept 8 ref
whatever is exported gets stuck into context namescope/namespace of one of the scopes of the node
under the name indicated in the pragma prop
if no node is specified in pragma prop, it gets merged in context namespace, wo namespacing
```
  - what exactly context gets exposed by a node when its referenced by concept pragma prop
```
its the context namespace in whatever scope is indicated by presence/abscence of _ prefix
that context, as well as sdcopes of a node, are not readily exposed by node api
of course, some provider pattern might be orchestrated by ,provider pragma
but its not necessary - maybe it can expose a loading function
but same cam be achieved by writing code in .context file
that does it explicitly
again, we would take that boilerplate away into .provider pragma
alternatively, instead of .context we can build a .provide.context or .provideContext or .provide_context_of_someNode
those are a chain of pragmas or a single compopsitional pragma, or a pragma that takes an arg of another pragma that takes
an arg of node name - a fp pipe

but all that is domain of linguistics - grammar, vocabulary and dictionary
with a flexible and dynamic lingustics package in deep down node proto, we can make pragmatic ext stribngs a free hand excecise
and let linguistics, orchestyrated by webdev process or stPragma handle the traslation to existing pragmas
or create those compositional pragmas as intended by semantics
 but lets not do it in this pohase, so we will delegate it to webdev process
 stPragma is not that smart - just composes precisely what pragmas are specified to it
```
  - what if we do someName.context_of_someNodePackageName.ts as a script
```
we get namespace of someName in context namespace, instead of someNodePackageName
scope.context.someName.myState
```
- how do these pragmatic contexts map on react contexts
```
they serve same purpose with same pattern of usage, if not syntax
but syntax can be arranged to match react very precisely, or not
depends on dev preference

the fundamental difference - pragmatic contexts tie you to the fs structure
react contexts dont

but division boundaries are very flexible,
as pragmas can be designed to transfer react contexts
to pragmatic contests,
to be passed around thru structure

the difference is where and how you access the contexts, syntacticaly
thru globa consts
or thru react pattern of useContext hooks
```
- whats inside those contexts?
```
basicaly, anything you put there
usually - its state and/or hooks and/or state reducers
which are state manipulator funcs, along setState generated by useState hook

but, in reactive impl of contexts
we make them pure state
as described in concept 8
contexts are instances of reactive trees, prbly treenity trees
and each script is wrapped by stPragma into an autoexec func
i dont know treenity syntax for that
but i do know how mobx orchestrates it, similar to meteor

and that illiminates need to pass hooks and reducers around thru context
just state
and whatever you access in the context, thats what will trigger reinvokation
which upodates some state in some context

so, take a note, those contexts are dynamic
they are dynamic state, and get manipulated by public nodes, in public scope, at runtime

and by pragmas in private scopes, at build time
its up to webdev process to react to private context changes
to rebuild the app

and that brings us to the concept 7, which describes how pragmas are mapped on react component
and how webdevProcess actually runs a legit react app
and thats what triggers fs rebuild on private context change, in fractal manner
orchestrated by machinery of react
running in node, instead of browser
but why not, lets make it run in webstorm plugin view window, called project window
and we can have css that styles linter errors and orchestrate contextual menues and actually dynamicaly adjusts them
and the most amazing feat, rearranges fs by changing css theme to some preset
and actually implementing variants of a package/app wo changing version , by applying different fsCss theme
then, the dev extends it more, if fine grained customization is needed
and publishes new theme
to make a few extra bux, in addition to client fees

and thats not the css themes we are used to
this is an app type boilerplate
with config files included
```

## lets limit stPragma scope of concerns
```
any css related or react related stuff - not our concern
stPragma provides pragma props mecanism, and context mechanism and deps as namespaces and access to nodes thru the the structure
and directly links namepaths to scopes of modules

thats enopugh to do any css or any other crazy schemes by outside controllers
stPragma provides the model
and React mapping can provide the view
that maps on MVC style of dev

so, thats the scoipe of stPragma - the model, in MVC structure of webdev collaborative environment
```
## lets explain exactly how scopes are constructed
- scopes are propagated thru structure of fs as js function scopes do thru structure of func defs,
by inheritance
- each term in local extension of scope is a script or a branching node
```
they all nodes, just either branching or leafs
they are implemented as scopes of lingusticTrees in forestry package
and the federation of public and private and fs tree are a forest
that gives us a controller, below webdev process - a local controller
and the forest lives inside the kernel node - the proto layer of node of spice.space.0.0.0
and thats what webdev process will deal with
```
- the scopedTree in forestry defines how terms in scopes are inserted as getters
```
but we have to extend those getters
we have to build wrappers around modules
to inject one of the scopes into them,. by a func with args as numerable terms in scope
then add innumerable terms of just local proto layer
in each scope - provate and public
```
- and we have to orchestrate the stPragma children pragmas - to construct the structure of scopes
```
those cildren are context, deps
these form namespaces in scopes

another child is pragma pragma
but that does not operate on scope
it generates api of host node, using the scopes
```
- pragma child of stPragma
```
this is where parent pragma is extended
alternatively, it can be done in index file
but that does not expose the new pragma for pragma composition

this separation also simplifies fs structure by eliminating index files,
where no pragma extensionm is necessary
this is majority of use cases
and it compensate for extra pragma script
necessary when opragma IS EXTENDED

this creates a new pragma
and such new pragma components should live in a special pragmas folder in fs structure
in my fs impl, its in .webdev.process/stages/pragmas.dict_of_stPragmas

it can be seen how dict_of_stPragmas is used before it is defined
thats a pattern

if an ext is found that does not match anyb pragma in private scope
its just ignored as a noop in pragma pipe composition
then, its _.pragma is exposed as its api in private scope

and stPragma itself is built in /Users/<USER>/WebstormProjects/spicetime-architecture/packages/core/spice.space/stPagma.y
it can be seen that even in this primordial node, a few void pragmas are used
to be defined later in pragmas stage of webdev process
well get to it sooner or later
but we can impl the stP{ragma before that

then, webdev process decides where that pragmas structure of pragma components should live
and thats up to webdev profile, as each dev might want to have his own way
its just projections of underlying source of truth - the graph in persistent storage layer

so, inside _.pragma script
whats exposed to the module is generic pragma and its alias pragmaOperator and the same func as <parentNodeName> in private scope
if .pragma is used to define pragma extension
it does not get private scope - it gets public scope
and it can not build an external api of the node

but it can build internal api
for the consumption of nested nodes
and it can export a dict of lovcal name translations
to rearrange the scope names, or introduce new terms
thats not a very declarative feature, and ad hoc use of that is an antipattern
what its good for is introduction of linguistics specific to the node l dialect

but a we will introduce an l pragma for that

still, for our _.pragma
we simply do func composition
export default myPragmaExtender(pragma)

note that no func invokation is needed for pragma
but it is needed for myPragmaExtender(pragmaOperator(...pragmaArgsOveride))
thtas how we extend args of pragma, passed thru pragmatic ext args using _ syntax
and, of course, we have to supply _.pragma.type.ts with pragmaOperator type
```
- l pragma child
```
we talked about that above
it allows extension of linguistics functionality
in either private or public scope
wo possibility of rearranging scope itself - a sa=fe transform, preserving declarative principle

and we can have someDomainName.l and it will setuo a namespace in l namespace of the scope
this is machinery to impl and use l ytemplate literal
```

## how pragma children, as pragmas that form parent pragma compound, are propagated thru pragma composition
pragmas have children, along compound component pattern in react.
```
children hang off parent as statics
and, when pragma node structures are nested,
using _nestedPragmaNode.pragma syntax
children in each branch form a scope of children - a tree

thats what actually hangs off the parant - the children scope
and any other pragma in scope can be destructed to access iots nested children

this is a compound component pattern in react
and we couldv created a compound pragma that does this in public scopes
but we already have it, if nestedPragma.pragma is used
but its kinda confusing
and frankly an antipattern
simply, why create another structure
of compounds of structural co0m[ponents,
deliberately not declarative thru file exts that pragmas do

we have react compounds for that
and it defeats the whole purpose of stPragma
but if you need it, be your own guest
create that compound pragma, by reusing that public .pragma child
```

## how types of pragmaOperator funcs are accessed
thru the t scope, in .pragma.type pragma scripts
```
each pragma defines its type in _.pragma.type file
again its the same visibility rules and _ and . prefix syntax
and it applies to typing same exact aliases wo .type pragma
and these types get stuffed into a t scope that parallels the corresponding private or public node scope

so, we have 4 scopes in a node
```

## TypeScope with Dot Notation Access

stPragma implements a TypeScope system that provides a structured way to access types throughout the filesystem hierarchy:

```typescript
// TypeScope interface
interface TypeScope {
  public: Record<string, any>;
  private: Record<string, any>;
}
```

### Private and Public TypeScopes

TypeScope maintains separate private and public type scopes:

- **Public TypeScope**: Defined in `.type.ts` files, accessible to all downstream nodes
- **Private TypeScope**: Defined in `_*.type.ts` files, accessible only to the local node

```typescript
// Button.type.ts (public)
export interface ButtonProps {
  onClick?: () => void;
  disabled?: boolean;
  variant?: 'primary' | 'secondary' | 'tertiary';
}

// _Button.type.ts (private)
export interface ButtonState {
  isHovered: boolean;
  isPressed: boolean;
  isFocused: boolean;
}
```

### Runtime Type System with T Namespace

stPragma implements a dual namespace structure for types:

- **t namespace**: Contains static type definitions (available at compile time)
- **T namespace**: Contains runtime type operations (available at runtime)

```typescript
// Static type definitions in the t namespace
namespace t {
  export interface ButtonProps {
    onClick?: () => void;
    disabled?: boolean;
    variant?: 'primary' | 'secondary' | 'tertiary';
  }
}

// Runtime type operations in the T namespace
namespace T {
  export const ButtonProps = {
    is(value: any): value is t.ButtonProps {
      return typeof value === 'object' && value !== null &&
             (typeof value.onClick === 'function' || value.onClick === undefined) &&
             (typeof value.disabled === 'boolean' || value.disabled === undefined) &&
             (typeof value.variant === 'string' || value.variant === undefined);
    },
    validate(value: any): boolean {
      if (!T.ButtonProps.is(value)) {
        return false;
      }

      // Additional validation logic
      if (value.variant && !['primary', 'secondary', 'tertiary'].includes(value.variant)) {
        return false;
      }

      return true;
    },
    create(props: Partial<t.ButtonProps>): t.ButtonProps {
      // Default values
      const defaults: t.ButtonProps = {
        onClick: () => {},
        disabled: false,
        variant: 'primary'
      };

      // Merge defaults with provided props
      const buttonProps = { ...defaults, ...props };

      // Validate the created object
      if (!T.ButtonProps.validate(buttonProps)) {
        throw new Error('Invalid ButtonProps created');
      }

      return buttonProps;
    }
  };
}
```

Each type in the `T` namespace includes three key methods for runtime type operations:

1. **is**: Type guard for checking if a value is of the correct type
2. **validate**: Deep validation of a value against the type and business rules
3. **create**: Create a new instance of the type with default values

### Namespaced Interfaces with Dot Notation

TypeScript supports namespaced interfaces that enable dot notation access to nested structures:

```typescript
namespace API {
  export interface User {
    id: string;
    name: string;
    email: string;
  }

  export namespace Auth {
    export interface Credentials {
      username: string;
      password: string;
    }

    export interface Token {
      value: string;
      expiresAt: string;
    }

    export interface Session {
      user: User;
      token: Token;
      isActive: boolean;
    }
  }
}
```

This enables clean, hierarchical access to types:

```typescript
function createUser(user: API.User): API.User {
  return user;
}

function login(credentials: API.Auth.Credentials): API.Auth.Session {
  // Implementation...
}
```

### Pragma Types with Children

Pragma types expose their children as namespaces with dot notation access:

```typescript
// _pragma.type.ts
export interface PragmaType {
  name: string;
  transform: (node: NodeScope) => NodeAPI;
  children: {
    [key: string]: PragmaType;
  };
}
```

Accessing pragma types:

```typescript
// Accessing pragma types
type ComponentPragma = t.pragma.component;
type StatePragma = t.pragma.component.withState;
type StyledPragma = t.pragma.component.withState.styled;

// Using runtime methods on pragma types from the T namespace
const isComponentPragma = T.pragma.component.is(someValue);
const validComponentPragma = T.pragma.component.validate(someValue);
const newComponentPragma = T.pragma.component.create({ name: 'myComponent' });
```

### TypeScope Propagation

TypeScope is propagated through the filesystem hierarchy in a manner similar to term scope:

1. Each node has its own TypeScope
2. Public types from parent nodes are inherited by child nodes
3. Private types are only accessible within the local node
4. Types from parallel branches are accessible through imports

```typescript
// Parent node
// Parent.type.ts
export interface ParentType {
  id: string;
  name: string;
}

// Child node
// Child.ts
// ParentType is accessible through the TypeScope
const child: t.ParentType = {
  id: '1',
  name: 'Child'
};

// Using runtime methods from the T namespace
const isParentType = T.ParentType.is(child);
const validParentType = T.ParentType.validate(child);
const newParentType = T.ParentType.create({ id: '2', name: 'New Child' });
```

This approach is used in stPragma to organize types in a hierarchical structure that mirrors the scope hierarchy, enabling a uniform way of accessing both types and terms with the same dot notation pattern. The addition of runtime type methods (`is`, `validate`, `create`) makes each API a validatable schema with runtime type checking capabilities.

- also, types of all terms in scope of a module, private or publiuc, are injected into the module
````
by directly prepending type imports to the script. the names are exactly what they are in scopes, but they are types
and for namespaces in scope, oh well, theres nothing we can do, except typeof
we dont wanna nameclash by flattening the namespaces
but the truth is, accessing typoes in scripts is not needed at all
if pragma does its job, itll type every func in and scope itself
- so, all types will be perfectly infered by ts
````


