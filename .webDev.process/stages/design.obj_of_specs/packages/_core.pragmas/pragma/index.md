# ST Pragma - Foundation Pragma System

## Description

The foundational pragma system that provides the core infrastructure for all SpiceTime pragmas. Defines the basic pragma structure, execution model, event system, and integration patterns that all other pragmas extend.

## Core Functionality

### Pragma Definition
```typescript
interface STPragma {
  name: string;
  version: string;
  children: Record<string, STPragma>;
  
  // Core execution
  execute(input: any, context: PragmaContext): Promise<any>;
  
  // Event system
  emit(eventType: string, data: any): Promise<void>;
  on(eventType: string, handler: EventHandler): void;
  
  // Child pragma management
  addChild(name: string, pragma: STPragma): void;
  getChild(name: string): STPragma | undefined;
  
  // Composition
  compose(other: STPragma): STPragma;
  
  // Kernel integration
  scheduleTask(type: string, priority: string, payload: any): Promise<string>;
  requestResources(requirements: ResourceRequirement[]): Promise<ResourceAllocation>;
}
```

### Pragma Context
```typescript
interface PragmaContext {
  // Execution context
  taskId: string;
  correlationId: string;
  timestamp: number;
  
  // Services
  kernel: KernelService;
  events: EventService;
  middleware: MiddlewareService;
  schemas: SchemaRegistry;
  
  // State management
  state: Map<string, any>;
  scope: Record<string, any>;
  
  // Configuration
  config: Record<string, any>;
  
  // Logging
  logger: Logger;
}
```

## Child Pragma Structure

### 1. execute - Core Execution Engine
Handles pragma execution lifecycle and orchestration
- Input validation and preprocessing
- Child pragma coordination
- Output generation and postprocessing
- Error handling and recovery

### 2. events - Event System
Manages event emission and subscription
- Event type registration
- Event emission with middleware interception
- Event subscription and handler management
- Event correlation and tracing

### 3. compose - Composition Engine
Handles pragma composition and integration
- Dependency resolution
- Interface compatibility checking
- Composition validation
- Integration point management

### 4. kernel - Kernel Integration
Provides kernel service integration
- Task scheduling and management
- Resource allocation and monitoring
- Priority management
- Performance tracking

### 5. middleware - Middleware System
Manages middleware plugin system
- Plugin registration and lifecycle
- Hook execution and management
- Resource coordination
- Error isolation

### 6. schema - Schema Management
Handles schema definition and validation
- Schema registration and discovery
- Input/output validation
- Type generation
- Documentation generation

## Execution Model

### Basic Execution Flow
```
1. st.execute.validate(input)
2. st.events.emit('beforeExecute', { input })
3. st.middleware.processEvent('beforeExecute')
4. st.execute.orchestrateChildren()
5. st.events.emit('afterExecute', { output })
6. st.middleware.processEvent('afterExecute')
7. return output
```

### Child Pragma Orchestration
```typescript
async function orchestrateChildren(input: any, context: PragmaContext) {
  const results = new Map();
  
  // Execute children in dependency order
  for (const [name, child] of this.children) {
    const childInput = this.prepareChildInput(input, results, name);
    const childOutput = await child.execute(childInput, context);
    results.set(name, childOutput);
  }
  
  return this.combineChildResults(results);
}
```

## Event System Integration

### Standard Event Types
```typescript
const standardEventTypes = [
  // Lifecycle events
  'beforeExecute',
  'afterExecute',
  'onError',
  
  // Child events
  'beforeChild',
  'afterChild',
  'childError',
  
  // Composition events
  'beforeCompose',
  'afterCompose',
  'composeError',
  
  // Resource events
  'resourceRequest',
  'resourceAllocated',
  'resourceReleased'
];
```

### Event Data Schema
```yaml
STEvent:
  type: object
  required:
    - id
    - type
    - timestamp
    - source
  properties:
    id:
      type: string
      format: uuid
    type:
      type: string
    timestamp:
      type: number
    source:
      type: string
      description: Pragma name that emitted the event
    correlationId:
      type: string
      format: uuid
    data:
      type: object
      additionalProperties: true
    context:
      type: object
      properties:
        taskId:
          type: string
        pragmaPath:
          type: string
        executionDepth:
          type: number
```

## Kernel Integration

### Task Scheduling
```typescript
// ST pragma schedules tasks through kernel
async function executeWithKernel(input: any) {
  const taskId = await this.kernel.scheduleTask({
    type: `${this.name}-execute`,
    priority: 'normal',
    payload: { input },
    resourceRequirements: this.getResourceRequirements()
  });
  
  await this.kernel.waitForApproval(taskId);
  
  try {
    const result = await this.executeInternal(input);
    await this.kernel.completeTask(taskId, result);
    return result;
  } catch (error) {
    await this.kernel.failTask(taskId, error);
    throw error;
  }
}
```

### Resource Management
```typescript
// ST pragma manages resources
async function withResources<T>(
  requirements: ResourceRequirement[],
  operation: () => Promise<T>
): Promise<T> {
  const allocation = await this.kernel.requestResources({
    requirements,
    requesterId: this.name,
    justification: 'Pragma execution'
  });
  
  try {
    return await operation();
  } finally {
    await this.kernel.releaseResources(allocation.id);
  }
}
```

## Middleware Integration

### Plugin Hook Points
```typescript
const standardHookPoints = [
  'beforeExecute',
  'afterExecute',
  'beforeChild',
  'afterChild',
  'onError',
  'beforeCompose',
  'afterCompose'
];
```

### Middleware Execution
```typescript
async function executeWithMiddleware(hookPoint: string, data: any) {
  const event = {
    id: generateUUID(),
    type: hookPoint,
    timestamp: Date.now(),
    source: this.name,
    data
  };
  
  const result = await this.middleware.processEvent(event, this.context);
  
  if (result.abort) {
    throw new Error(result.abort.reason);
  }
  
  if (result.reschedule) {
    await this.rescheduleExecution(result.reschedule);
    return;
  }
  
  return result.continue || event;
}
```

## Schema System

### Pragma Schema Definition
```yaml
STPragmaSchema:
  type: object
  required:
    - name
    - version
    - input
    - output
  properties:
    name:
      type: string
    version:
      type: string
    description:
      type: string
    input:
      $ref: '#/components/schemas/InputSchema'
    output:
      $ref: '#/components/schemas/OutputSchema'
    children:
      type: object
      additionalProperties:
        $ref: '#/components/schemas/STPragmaSchema'
    events:
      type: array
      items:
        type: string
    dependencies:
      type: array
      items:
        type: string
```

## Usage Pattern

### Creating a New Pragma
```typescript
// Extend ST pragma to create spec pragma
class SpecPragma extends STPragma {
  constructor() {
    super('spec', '1.0.0');
    
    // Add child pragmas
    this.addChild('parse', new ParsePragma());
    this.addChild('generate', new GeneratePragma());
    this.addChild('sync', new SyncPragma());
    this.addChild('compose', new ComposePragma());
    this.addChild('events', new EventsPragma());
    this.addChild('middleware', new MiddlewarePragma());
  }
  
  async execute(input: SpecInput, context: PragmaContext) {
    // Spec-specific execution logic
    return super.execute(input, context);
  }
}
```

### Pragma Registration
```typescript
// Register pragma with the system
await pragmaRegistry.register(new SpecPragma());
```

This foundational ST pragma provides:
- **Core execution model** that all pragmas follow
- **Event system** for middleware integration
- **Kernel integration** for resource management
- **Child pragma orchestration** for composition
- **Schema system** for validation and documentation
- **Middleware hooks** for extensibility

The spec pragma then extends this foundation with its specific transformation logic.
