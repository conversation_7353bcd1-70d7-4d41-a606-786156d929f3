pragmas can be base or default impl
eg process pragma
but its not necessarily the case
as obj pragma is noit really an impl of anything - just a recepi to create an obj

pragmas should specify their pkural and other linguistic patterns, thru l child pragma
it extends l in scope of consuming node

which scope?
depends if _ prefix is used
in case of this folder - it has _ prefix, meaning it will be placed in nodes private scopoe when consumed
if a pragma was picked from internal structure of _pragmas, it would go into public scope
but it will be consumed as a pragmas.hDict object - a private scope namespace

who will consume it?
webdev process - it would go into _scope.deps.pragmas, as a dep
and it would feed it to st pragma - the base pragma

st pragma has no idea, on its own what other pragmas been extending it
but its told, by feeding _pragmas as an arg
and _ prefix determines which scope it goes into
actuall, webdev process creates fs tree, and trees have scopes and scopes have a proto scope
and thats what it is
but webdev process maintains a forest - at least private public and l trees
then prbly many more, as each stage is some kinda structure, and most of them are trees

i changed parent folder name to _core.pragmas
pragmas is a pragma and it implies an hDict as api type
pragmas is a linguistic variant of pragma pragma


