# Phase 5: Components

## Overview

Implements the component ecosystem for building user interfaces and interactive applications. This phase creates React components, Web Components, state management, and UI composition patterns that integrate seamlessly with the SpiceTime pragma and service systems.

## Status: Planned 📋

### Dependencies from Previous Phases
- ✅ **Phase 1**: Foundation and component pragma basics
- 🔄 **Phase 2**: Core pragmas (obj, prop, event, context) - needed for component implementation
- 🔄 **Phase 3**: Linguistic system - needed for natural component composition
- 🔄 **Phase 4**: Services - needed for data integration and state management

### Key Deliverables
- **React Component System** - React-specific component pragmas
- **Web Component System** - Standards-based web components
- **State Management** - Reactive state with SpiceTime integration
- **UI Composition** - Layout and styling systems
- **Component Registry** - Component discovery and reuse
- **Testing Framework** - Component testing and validation
- **Storybook Integration** - Component documentation and playground

## Component Architecture

### Component Ecosystem Structure
```
components/
├── react/                  # React-specific components
│   ├── component/         # React component pragma
│   ├── hook/              # React hook pragma
│   ├── provider/          # React provider pragma
│   ├── hoc/               # Higher-order component pragma
│   └── context/           # React context pragma
├── web/                   # Web Components
│   ├── element/           # Custom element pragma
│   ├── shadow/            # Shadow DOM pragma
│   ├── template/          # HTML template pragma
│   └── slot/              # Slot system pragma
├── state/                 # State management
│   ├── reactive/          # Reactive state pragma
│   ├── store/             # State store pragma
│   ├── reducer/           # Reducer pragma
│   ├── selector/          # Selector pragma
│   └── effect/            # Side effect pragma
├── ui/                    # UI composition
│   ├── layout/            # Layout system pragma
│   ├── style/             # Styling system pragma
│   ├── theme/             # Theming pragma
│   ├── animation/         # Animation pragma
│   └── interaction/       # Interaction pragma
├── testing/               # Component testing
│   ├── render/            # Component rendering for tests
│   ├── mock/              # Component mocking
│   ├── snapshot/          # Snapshot testing
│   └── e2e/               # End-to-end testing
└── registry/              # Component registry
    ├── catalog/           # Component catalog
    ├── discovery/         # Component discovery
    ├── versioning/        # Component versioning
    └── composition/       # Component composition
```

## Deliverables

### 1. React Component System
```
📋 react.component - React component pragma with lifecycle
📋 react.hook - React hook pragma with state and effects
📋 react.provider - React provider pragma for context
📋 react.hoc - Higher-order component pragma
📋 react.context - React context management
```

### 2. Web Component System
```
📋 web.element - Custom element pragma
📋 web.shadow - Shadow DOM encapsulation
📋 web.template - HTML template system
📋 web.slot - Slot-based composition
📋 web.lifecycle - Web component lifecycle
```

### 3. State Management System
```
📋 state.reactive - Reactive state with observables
📋 state.store - Centralized state store
📋 state.reducer - State reduction patterns
📋 state.selector - State selection and derivation
📋 state.effect - Side effect management
```

### 4. UI Composition System
```
📋 ui.layout - Flexible layout system
📋 ui.style - CSS-in-JS styling
📋 ui.theme - Design system theming
📋 ui.animation - Animation and transitions
📋 ui.interaction - User interaction handling
```

### 5. Testing & Documentation
```
📋 testing.component - Component testing framework
📋 testing.visual - Visual regression testing
📋 storybook.integration - Storybook component documentation
📋 registry.catalog - Component catalog and discovery
```

## Round-Robin Development Strategy

### Round 1: React Foundation (Week 1)
**Focus**: Establish React component system

**React Component Pragma**:
- Component definition and lifecycle
- Props and state management
- Event handling and refs
- Performance optimization (memo, callback)

**React Hook Pragma**:
- Custom hook creation and composition
- State hooks (useState, useReducer)
- Effect hooks (useEffect, useLayoutEffect)
- Context hooks (useContext)

**React Provider Pragma**:
- Context provider creation
- Value management and updates
- Consumer pattern support
- Performance optimization

**React HOC Pragma**:
- Higher-order component patterns
- Props injection and transformation
- Component enhancement
- Composition patterns

**Validation**: Ensure React components integrate with pragma system

### Round 2: State Management (Week 2)
**Focus**: Build reactive state management

**Reactive State Pragma**:
- Observable state with MobX-style reactivity
- Automatic dependency tracking
- Computed values and derivations
- Integration with React components

**State Store Pragma**:
- Centralized state management
- Action-based state updates
- Middleware support
- Time travel debugging

**Reducer Pragma**:
- Redux-style reducers
- Immutable state updates
- Action creators and types
- Integration with store

**Selector Pragma**:
- Memoized state selection
- Derived state computation
- Performance optimization
- Reselect-style selectors

**Validation**: Ensure state management integrates with components

### Round 3: UI Composition (Week 3)
**Focus**: Build UI composition and styling

**Layout Pragma**:
- Flexbox and Grid layouts
- Responsive design patterns
- Container and item components
- Spacing and alignment

**Style Pragma**:
- CSS-in-JS styling
- Theme integration
- Dynamic styling
- Performance optimization

**Theme Pragma**:
- Design system integration
- Theme provider and consumer
- Dark/light mode support
- Custom theme creation

**Animation Pragma**:
- CSS and JavaScript animations
- Transition management
- Performance optimization
- Gesture integration

**Validation**: Ensure UI composition works across components

### Round 4: Web Components & Testing (Week 4)
**Focus**: Complete component ecosystem

**Web Element Pragma**:
- Custom element definition
- Lifecycle callbacks
- Attribute observation
- Event handling

**Shadow DOM Pragma**:
- Shadow DOM encapsulation
- Style isolation
- Slot composition
- Event delegation

**Component Testing Pragma**:
- Component rendering for tests
- Event simulation
- Assertion helpers
- Snapshot testing

**Component Registry**:
- Component catalog and discovery
- Version management
- Composition validation
- Documentation generation

**Validation**: Ensure complete component ecosystem works together

## React Component Implementation

### React Component Pragma
```typescript
interface ReactComponentPragma extends ComponentPragma {
  // React-specific properties
  reactVersion: string;
  displayName: string;
  defaultProps: Record<string, any>;
  
  // Component definition
  render(): JSX.Element;
  
  // Lifecycle hooks
  componentDidMount?(): void;
  componentDidUpdate?(prevProps: any, prevState: any): void;
  componentWillUnmount?(): void;
  
  // Error boundaries
  componentDidCatch?(error: Error, errorInfo: ErrorInfo): void;
  static getDerivedStateFromError?(error: Error): any;
  
  // Performance optimization
  shouldComponentUpdate?(nextProps: any, nextState: any): boolean;
  getSnapshotBeforeUpdate?(prevProps: any, prevState: any): any;
  
  // React hooks integration
  useHooks(): HookResult[];
  
  // SpiceTime integration
  usePragma<T>(pragma: Pragma): T;
  useService<T>(service: Service): T;
  useLinguistic(expression: string): any;
}

class ReactComponentFactory extends ComponentFactory<ReactComponentPragma> {
  create(definition: ReactComponentDefinition): ReactComponentPragma {
    const component = new ReactComponentPragma({
      ...definition,
      type: 'react-component',
      framework: 'react',
      version: this.getReactVersion()
    });
    
    // Add React-specific enhancements
    this.addReactLifecycle(component);
    this.addHookSupport(component);
    this.addPerformanceOptimizations(component);
    
    return component;
  }
  
  private addReactLifecycle(component: ReactComponentPragma): void {
    // Add lifecycle method wrappers
    component.onMount = (callback) => {
      component.componentDidMount = callback;
    };
    
    component.onUpdate = (callback) => {
      component.componentDidUpdate = callback;
    };
    
    component.onUnmount = (callback) => {
      component.componentWillUnmount = callback;
    };
  }
}
```

### React Hook Pragma
```typescript
interface ReactHookPragma extends STPragma {
  // Hook definition
  hookName: string;
  dependencies: string[];
  
  // Hook implementation
  useHook(...args: any[]): any;
  
  // Hook composition
  compose(other: ReactHookPragma): ReactHookPragma;
  
  // State management
  useState<T>(initialState: T): [T, Dispatch<SetStateAction<T>>];
  useEffect(effect: EffectCallback, deps?: DependencyList): void;
  useContext<T>(context: Context<T>): T;
  
  // Custom hooks
  useCustom<T>(hookFn: (...args: any[]) => T): T;
  
  // SpiceTime integration
  usePragmaState<T>(pragma: Pragma): T;
  useServiceData<T>(service: Service): T;
  useLinguisticExpression(expression: string): any;
}

// Example custom hook
const useDataFetching = hook.create({
  name: 'useDataFetching',
  dependencies: ['http.service', 'cache.service'],
  
  useHook: (url: string, options?: RequestOptions) => {
    const [data, setData] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    
    const httpService = useService('http');
    const cacheService = useService('cache');
    
    useEffect(() => {
      const fetchData = async () => {
        setLoading(true);
        try {
          // Check cache first
          const cached = await cacheService.get(url);
          if (cached) {
            setData(cached);
            setLoading(false);
            return;
          }
          
          // Fetch from network
          const response = await httpService.get(url, options);
          setData(response.data);
          
          // Cache result
          await cacheService.set(url, response.data);
        } catch (err) {
          setError(err);
        } finally {
          setLoading(false);
        }
      };
      
      fetchData();
    }, [url, options]);
    
    return { data, loading, error };
  }
});
```

## State Management Implementation

### Reactive State Pragma
```typescript
interface ReactiveStatePragma extends STPragma {
  // Observable state
  observable<T>(initialValue: T): Observable<T>;
  computed<T>(computation: () => T): Computed<T>;
  action<T extends (...args: any[]) => any>(fn: T): T;
  
  // Reactions
  autorun(reaction: () => void): Disposer;
  when(predicate: () => boolean, effect: () => void): Disposer;
  reaction<T>(expression: () => T, effect: (value: T) => void): Disposer;
  
  // Integration with React
  observer<T extends ComponentType<any>>(component: T): T;
  useObserver<T>(fn: () => T): T;
  
  // SpiceTime integration
  observePragma<T>(pragma: Pragma): Observable<T>;
  observeService<T>(service: Service): Observable<T>;
}

class ReactiveStateImpl implements ReactiveStatePragma {
  private mobx = require('mobx');
  private mobxReact = require('mobx-react-lite');
  
  observable<T>(initialValue: T): Observable<T> {
    return this.mobx.observable(initialValue);
  }
  
  computed<T>(computation: () => T): Computed<T> {
    return this.mobx.computed(computation);
  }
  
  action<T extends (...args: any[]) => any>(fn: T): T {
    return this.mobx.action(fn);
  }
  
  observer<T extends ComponentType<any>>(component: T): T {
    return this.mobxReact.observer(component);
  }
  
  // SpiceTime-specific observables
  observePragma<T>(pragma: Pragma): Observable<T> {
    const observable = this.observable(pragma.getState());
    
    // Listen for pragma state changes
    pragma.on('stateChange', this.action((newState: T) => {
      Object.assign(observable, newState);
    }));
    
    return observable;
  }
}
```

## UI Composition Implementation

### Layout Pragma
```typescript
interface LayoutPragma extends STPragma {
  // Layout types
  flex(config: FlexConfig): LayoutComponent;
  grid(config: GridConfig): LayoutComponent;
  stack(config: StackConfig): LayoutComponent;
  
  // Responsive design
  responsive(breakpoints: Breakpoints): ResponsiveLayout;
  container(config: ContainerConfig): ContainerComponent;
  
  // Spacing and alignment
  spacing(config: SpacingConfig): SpacingComponent;
  alignment(config: AlignmentConfig): AlignmentComponent;
  
  // Layout composition
  compose(layouts: LayoutComponent[]): ComposedLayout;
  
  // Integration with components
  applyToComponent(component: ComponentPragma, layout: LayoutComponent): ComponentPragma;
}

// Example layout usage
const pageLayout = layout.flex({
  direction: 'column',
  height: '100vh'
}).compose([
  layout.container({ maxWidth: '1200px' }).containing(
    header.component
  ),
  layout.flex({
    direction: 'row',
    flex: 1
  }).compose([
    sidebar.component.with(layout.flex({ basis: '250px' })),
    main.component.with(layout.flex({ flex: 1 }))
  ]),
  footer.component
]);
```

### Style Pragma
```typescript
interface StylePragma extends STPragma {
  // Style definition
  css(styles: CSSProperties): StyleDefinition;
  styled<T extends ComponentType>(component: T): StyledComponent<T>;
  
  // Theme integration
  themed(styles: ThemedStyles): StyleDefinition;
  useTheme(): Theme;
  
  // Dynamic styling
  conditional(condition: boolean, trueStyles: CSSProperties, falseStyles?: CSSProperties): StyleDefinition;
  responsive(breakpoints: ResponsiveStyles): StyleDefinition;
  
  // Animation integration
  animated(styles: AnimatedStyles): StyleDefinition;
  transition(config: TransitionConfig): StyleDefinition;
  
  // Performance optimization
  memoized(styles: CSSProperties): StyleDefinition;
  cached(key: string, styles: CSSProperties): StyleDefinition;
}

// Example styled component
const StyledButton = style.styled(Button)`
  ${style.themed(theme => ({
    backgroundColor: theme.colors.primary,
    color: theme.colors.onPrimary,
    padding: theme.spacing.medium,
    borderRadius: theme.borderRadius.medium
  }))}
  
  ${style.conditional(props => props.disabled, {
    opacity: 0.5,
    cursor: 'not-allowed'
  })}
  
  ${style.responsive({
    mobile: { fontSize: '14px' },
    tablet: { fontSize: '16px' },
    desktop: { fontSize: '18px' }
  })}
  
  ${style.animated({
    hover: {
      transform: 'scale(1.05)',
      transition: 'transform 0.2s ease'
    }
  })}
`;
```

## Integration with Linguistic System

### Component Composition through Linguistics
```typescript
// Natural language component composition
const userProfile = 
  layout.flex({ direction: 'column' })
  |> containing_(
       avatar.component |> styled_with_(avatar_theme),
       name.component |> styled_with_(name_theme),
       bio.component |> styled_with_(bio_theme)
     )
  |> wrapped_in_(card.component)
  |> themed_with_(profile_theme);

// State management composition
const todoApp =
  state.store({ todos: [], filter: 'all' })
  |> enhanced_with_(
       add_todo |> triggered_by_('form.submit'),
       toggle_todo |> triggered_by_('todo.click'),
       filter_todos |> triggered_by_('filter.change')
     )
  |> connected_to_(
       todo_list.component,
       todo_filter.component,
       todo_stats.component
     );

// Event-driven composition
const chatApp =
  listen_for_('message.received')
  |> validate_with_(message_validator)
  |> transform_through_(message_formatter)
  |> update_state_('messages')
  |> render_in_(message_list.component)
  |> notify_via_(notification_service);
```

### Component Vocabulary
```typescript
const componentVocabulary = {
  // Component operations
  'render_as': 'component.render',
  'styled_with': 'style.apply',
  'themed_with': 'theme.apply',
  'animated_with': 'animation.apply',
  'laid_out_as': 'layout.apply',
  
  // State operations
  'state_from': 'state.create',
  'connected_to': 'state.connect',
  'derived_from': 'state.derive',
  'updated_by': 'state.update',
  
  // Event operations
  'triggered_by': 'event.listen',
  'emits_to': 'event.emit',
  'handles_with': 'event.handle',
  
  // Composition operations
  'containing': 'layout.contain',
  'wrapped_in': 'component.wrap',
  'enhanced_with': 'component.enhance',
  'composed_of': 'component.compose'
};
```

## Testing Strategy

### Component Testing
- **Unit Testing** - Test individual components in isolation
- **Integration Testing** - Test component interactions and data flow
- **Visual Testing** - Test component appearance and styling
- **Accessibility Testing** - Test ARIA compliance and keyboard navigation

### State Management Testing
- **State Updates** - Test state changes and side effects
- **Selectors** - Test derived state computation
- **Actions** - Test action creators and reducers
- **Performance** - Test state update performance

### UI Composition Testing
- **Layout Testing** - Test responsive layouts and breakpoints
- **Style Testing** - Test CSS generation and theme application
- **Animation Testing** - Test animation timing and performance
- **Interaction Testing** - Test user interactions and event handling

## Success Criteria

### Technical Criteria
- [ ] React components integrate with pragma system
- [ ] State management provides reactive updates
- [ ] UI composition enables flexible layouts
- [ ] Web components work alongside React
- [ ] Component testing framework validates behavior

### Quality Criteria
- [ ] Components are performant and accessible
- [ ] State management prevents common pitfalls
- [ ] Styling system supports design systems
- [ ] Documentation includes interactive examples
- [ ] Testing covers all component scenarios

### Integration Criteria
- [ ] Components integrate with services
- [ ] Linguistic composition works naturally
- [ ] IDE provides component discovery
- [ ] Storybook documents all components
- [ ] Component registry enables reuse

## Phase 5 Exit Criteria

### Must Have
- [ ] React component system fully implemented
- [ ] State management with reactive updates
- [ ] Basic UI composition (layout, styling)
- [ ] Component testing framework
- [ ] Integration with pragma and service systems

### Should Have
- [ ] Web component system implemented
- [ ] Advanced UI features (theming, animation)
- [ ] Component registry and catalog
- [ ] Storybook integration
- [ ] Performance optimization features

### Nice to Have
- [ ] Advanced state management patterns
- [ ] Comprehensive design system
- [ ] Visual regression testing
- [ ] Component marketplace
- [ ] Advanced animation capabilities

**Phase 5 completion provides a comprehensive component ecosystem that enables building modern, reactive user interfaces with natural language composition and full SpiceTime integration.**
