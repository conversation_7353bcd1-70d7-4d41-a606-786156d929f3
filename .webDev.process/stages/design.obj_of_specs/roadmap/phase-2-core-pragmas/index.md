# Phase 2: Core Pragmas

## Overview

Implements the essential pragmas that form the backbone of the SpiceTime ecosystem. These pragmas are referenced throughout the system and provide fundamental data structures, type management, and behavioral patterns.

## Status: 20% Complete 🔄

### Completed ✅
- **Chain Pragma** - Sequential composition (from Phase 1)

### In Progress 🔄
- **Component Pragma** - Basic structure created

### Remaining 📋
- **obj** - Object pragma (highest priority - referenced everywhere)
- **dict** - Dictionary pragma
- **hDict** - Homogeneous dictionary pragma
- **t** - Type system foundation
- **factory** - Factory pattern implementation
- **list** - List pragma
- **seq** - Sequence pragma
- **arr** - Array pragma
- **vSpace** - Virtual space pragma
- **context** - Context management pragma
- **scope** - Scope management pragma
- **prop** - Property pragma
- **ttree** - Tree structure pragma
- **event** - Event handling pragma

## Deliverables

### 1. Data Structure Pragmas (Critical Path)
```
📋 obj - Object pragma (referenced by all other pragmas)
📋 dict - Dictionary pragma (key-value structures)
📋 hDict - Homogeneous dictionary pragma (typed collections)
📋 list - List pragma (ordered collections)
📋 seq - Sequence pragma (sequential data)
📋 arr - Array pragma (indexed collections)
```

### 2. Type System Pragmas (Critical Path)
```
📋 t - Type system foundation (parallel to spec object)
📋 factory - Factory pattern for type creation
📋 prop - Property pragma (object properties)
```

### 3. Spatial & Organizational Pragmas
```
📋 vSpace - Virtual space pragma (spatial organization)
📋 context - Context management pragma (execution context)
📋 scope - Scope management pragma (variable scoping)
📋 ttree - Tree structure pragma (hierarchical data)
```

### 4. Behavioral Pragmas
```
📋 event - Event handling pragma (event system foundation)
🔄 component - Component pragma (UI components)
```

## Priority Matrix

### P0 - Critical (Blocks other development)
1. **obj** - Referenced by almost every other pragma
2. **t** - Type system foundation needed for type safety
3. **factory** - Pattern used throughout the system

### P1 - High (Needed for Phase 3)
4. **dict** - Dictionary operations needed for linguistic system
5. **hDict** - Homogeneous collections for type safety
6. **prop** - Property management for object system

### P2 - Medium (Needed for Phase 4-5)
7. **list** - List operations for data processing
8. **seq** - Sequential operations for pipelines
9. **arr** - Array operations for collections
10. **event** - Event system for services
11. **context** - Context management for execution

### P3 - Lower (Can be deferred)
12. **vSpace** - Spatial organization (advanced feature)
13. **scope** - Scope management (optimization)
14. **ttree** - Tree structures (specialized use cases)

## Round-Robin Coordination Strategy

### Round 1: Basic API Structure (Week 1)
**Focus**: Define basic APIs for all P0-P1 pragmas
- **obj**: Basic object operations (get, set, has, keys, values)
- **t**: Basic type operations (create, validate, infer)
- **factory**: Basic factory operations (create, register, resolve)
- **dict**: Basic dictionary operations (get, set, delete, iterate)
- **hDict**: Basic homogeneous operations (typed get/set)
- **prop**: Basic property operations (define, access, validate)

**Validation**: Ensure all APIs support required inter-pragma interactions

### Round 2: Detailed Data Structures (Week 2)
**Focus**: Refine data structures based on sibling needs
- **obj**: Advanced object operations (merge, clone, transform)
- **dict**: Advanced dictionary operations (filter, map, reduce)
- **hDict**: Type enforcement and validation
- **list**: List operations (append, prepend, slice, concat)
- **seq**: Sequential operations (next, prev, iterate)
- **arr**: Array operations (index, splice, sort)

**Validation**: Ensure data flows between pragmas are complete

### Round 3: Type System Integration (Week 3)
**Focus**: Complete type system integration
- **t**: Type inference, validation, composition
- **factory**: Type-aware factory methods
- **prop**: Property type validation
- **obj**: Object type enforcement
- **dict/hDict**: Collection type safety

**Validation**: Ensure type safety across all pragma interactions

### Round 4: Behavioral Integration (Week 4)
**Focus**: Event handling and behavioral patterns
- **event**: Event emission, subscription, handling
- **context**: Context creation, management, inheritance
- **component**: Component lifecycle, props, state
- **ttree**: Tree traversal, manipulation, queries

**Validation**: Ensure behavioral patterns work across pragmas

## Critical Dependencies

### obj Pragma Dependencies
```
obj → (foundation for all other pragmas)
├── dict → (extends obj with key-value semantics)
├── hDict → (extends dict with type homogeneity)
├── prop → (extends obj with property management)
├── context → (extends obj with execution context)
└── component → (extends obj with UI semantics)
```

### Type System Dependencies
```
t → (type system foundation)
├── factory → (depends on t for type creation)
├── obj → (depends on t for object typing)
├── prop → (depends on t for property typing)
└── all pragmas → (depend on t for type safety)
```

### Data Flow Dependencies
```
obj + dict + hDict → list + seq + arr
list + seq + arr → event + context
event + context → component
```

## Implementation Strategy

### Week 1: Foundation Trio (obj, t, factory)
**Objective**: Establish the three most critical pragmas

**obj Pragma**:
- Basic object operations and API
- Integration with type system
- Property access patterns
- Validation framework

**t Pragma**:
- Type definition and creation
- Type validation and inference
- Type composition and transformation
- Integration with factory pattern

**factory Pragma**:
- Factory registration and resolution
- Type-aware object creation
- Validation and error handling
- Integration with obj and t

### Week 2: Collection Pragmas (dict, hDict, list)
**Objective**: Build collection abstractions on obj foundation

**dict Pragma**:
- Key-value operations
- Iteration and transformation
- Integration with obj
- Type safety through t

**hDict Pragma**:
- Homogeneous type enforcement
- Type-safe operations
- Performance optimizations
- Integration with dict and t

**list Pragma**:
- Ordered collection operations
- Functional programming patterns
- Integration with arr and seq
- Type safety for elements

### Week 3: Advanced Collections (seq, arr, prop)
**Objective**: Complete collection ecosystem

**seq Pragma**:
- Sequential access patterns
- Iterator protocol
- Lazy evaluation support
- Integration with list and chain

**arr Pragma**:
- Array-specific operations
- Index-based access
- Performance optimizations
- Integration with list and seq

**prop Pragma**:
- Property definition and management
- Getter/setter patterns
- Validation and constraints
- Integration with obj and t

### Week 4: Behavioral Pragmas (event, context, component)
**Objective**: Add behavioral capabilities

**event Pragma**:
- Event emission and subscription
- Event bubbling and capturing
- Type-safe event data
- Integration with all pragmas

**context Pragma**:
- Execution context management
- Context inheritance and scoping
- Resource management
- Integration with event and component

**component Pragma**:
- Component lifecycle management
- Props and state management
- Event handling integration
- Type-safe component APIs

## Testing Strategy

### Unit Testing
- **Individual Pragma APIs** - Test each pragma in isolation
- **Type Safety** - Verify type enforcement and inference
- **Error Handling** - Test validation and error scenarios
- **Performance** - Benchmark critical operations

### Integration Testing
- **Cross-Pragma Operations** - Test pragma interactions
- **Type System Integration** - Verify type safety across pragmas
- **Data Flow Validation** - Test data passing between pragmas
- **Event System Integration** - Test event propagation

### Round-Robin Validation
- **API Consistency** - Ensure consistent patterns across pragmas
- **Type Compatibility** - Verify type system integration
- **Data Flow Completeness** - Ensure all required data flows work
- **Performance Benchmarks** - Meet performance targets

## Success Criteria

### Technical Criteria
- [ ] All P0-P1 pragmas fully implemented and tested
- [ ] Type system provides complete type safety
- [ ] All pragma interactions validated
- [ ] Performance benchmarks met
- [ ] Integration tests passing

### Quality Criteria
- [ ] 100% TypeScript coverage
- [ ] 90%+ test coverage
- [ ] Zero ESLint errors
- [ ] API documentation complete
- [ ] Working examples for all pragmas

### Integration Criteria
- [ ] All pragmas integrate with obj foundation
- [ ] Type system works across all pragmas
- [ ] Event system enables communication
- [ ] Factory pattern works for all types
- [ ] Round-robin validation passes

## Risk Assessment

### High Risks
1. **obj Pragma Complexity** - Foundation may be too complex
   - *Mitigation*: Start simple, add complexity incrementally
   - *Contingency*: Split into smaller, focused pragmas

2. **Type System Performance** - Type checking may be slow
   - *Mitigation*: Optimize critical paths, lazy evaluation
   - *Contingency*: Provide performance vs. safety trade-offs

3. **Circular Dependencies** - Pragmas may have circular references
   - *Mitigation*: Careful dependency analysis and design
   - *Contingency*: Refactor to eliminate cycles

### Medium Risks
1. **API Stability** - APIs may need changes during development
   - *Mitigation*: Extensive validation in each round
   - *Contingency*: Version APIs and provide migration

2. **Integration Complexity** - Pragma interactions may be complex
   - *Mitigation*: Continuous integration testing
   - *Contingency*: Simplify interaction patterns

## Phase 2 Exit Criteria

### Must Have
- [ ] obj, t, factory pragmas fully implemented
- [ ] dict, hDict, prop pragmas fully implemented
- [ ] Type system providing complete type safety
- [ ] All pragma interactions validated
- [ ] Integration tests passing

### Should Have
- [ ] list, seq, arr pragmas implemented
- [ ] event, context pragmas implemented
- [ ] Performance benchmarks met
- [ ] API documentation complete

### Nice to Have
- [ ] component pragma fully implemented
- [ ] vSpace, scope, ttree pragmas implemented
- [ ] Advanced type system features
- [ ] Optimization for common patterns

**Phase 2 completion provides the essential pragma foundation needed for Phase 3 (Linguistic System) and Phase 4 (Services).**
