# Pragma Ecosystem Specification

## Context

This concept defines the comprehensive ecosystem of pragmas that form the foundational building blocks of the SpiceTime architecture. The pragma ecosystem provides atomic, composable units for building React applications through filesystem structure, eliminating imports and enabling declarative component composition.

Key aspects:
- Complete separation of state management (reactive trees) from JSX composition
- Atomic pragma ecosystem accommodating any component pattern or style
- Type system foundation built on cat.types and forestry.types
- Runtime type validation and creation through t scope construction
- Filesystem-to-JSX mapping through structured pragma composition
- Integration with kernel-controlled React scheduling and treenityTree reactivity

This concept represents the foundational work that enables the pragmatic BDD workflows and component patterns envisioned in concept 23, built upon the API standard defined in concept 15, and integrated with the kernel priority synchronization from concept 25.
