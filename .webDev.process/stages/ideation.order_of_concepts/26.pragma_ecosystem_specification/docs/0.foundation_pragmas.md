# Foundation Pragmas

## Overview

Foundation pragmas provide the atomic building blocks upon which all other pragmas are constructed. These pragmas implement the core categorical types and reactive structures that form the mathematical foundation of the SpiceTime architecture.

## Core Foundation Pragmas

### 1. obj.pragma

**Purpose**: Object composition with properties, methods, getters, and setters

**Type Definition**: 
```typescript
// obj.pragma.type
interface ObjPragmaType {
  children: Array<
    | { pragma: 'property', name: string, value: any }
    | { pragma: 'method', name: string, implementation: Function }
    | { pragma: 'getter', name: string, implementation: Function }
    | { pragma: 'setter', name: string, implementation: Function }
  >;
}
```

**Implementation Pattern**:
```typescript
// obj.pragma
export default function objPragma(children) {
  const obj = {};
  const descriptors = {};

  children.forEach(child => {
    switch (child.pragma) {
      case 'property':
        obj[child.name] = child.value;
        break;
      case 'method':
        obj[child.name] = child.implementation;
        break;
      case 'getter':
        descriptors[child.name] = {
          ...descriptors[child.name],
          get: child.implementation
        };
        break;
      case 'setter':
        descriptors[child.name] = {
          ...descriptors[child.name],
          set: child.implementation
        };
        break;
    }
  });

  Object.defineProperties(obj, descriptors);
  return obj;
}
```

**Runtime Type**:
```typescript
// In t scope
t.obj = {
  is: (value: any): value is ObjPragmaType => {
    return typeof value === 'object' && 
           Array.isArray(value.children) &&
           value.children.every(child => 
             ['property', 'method', 'getter', 'setter'].includes(child.pragma)
           );
  },
  validate: (value: any): boolean => t.obj.is(value),
  create: (props: Partial<ObjPragmaType>): ObjPragmaType => ({
    children: props.children || []
  })
};
```

### 2. prop.pragma

**Purpose**: Property definition with values resolved from scope

**Type Definition**:
```typescript
// prop.pragma.type
interface PropPragmaType {
  name: string;
  value: any;
  scopeReference?: string; // Reference to scope term
}
```

**Implementation Pattern**:
```typescript
// prop.pragma
export default function propPragma(definition) {
  return {
    name: definition.name,
    getValue: (scope) => {
      if (definition.scopeReference) {
        return resolveScopeTerm(scope, definition.scopeReference);
      }
      return definition.value;
    }
  };
}

function resolveScopeTerm(scope, reference) {
  // Navigate scope using dot notation: "user.profile.name"
  return reference.split('.').reduce((obj, key) => obj?.[key], scope);
}
```

### 3. ttree.pragma

**Purpose**: TreenityTree reactive structures that don't affect scopes

**Type Definition**:
```typescript
// ttree.pragma.type
interface TtreePragmaType {
  children: Array<{
    pragma: 'obj' | 'context' | 'prop';
    name: string;
    [key: string]: any;
  }>;
  reactive: true;
  scopeIndependent: true;
}
```

**Implementation Pattern**:
```typescript
// ttree.pragma
export default function ttreePragma(children) {
  const tree = {};
  
  // Build the tree structure
  children.forEach(child => {
    switch(child.pragma) {
      case 'obj':
        // Non-reactive object
        tree[child.name] = child.compose();
        break;
      case 'context':
        // Nested reactive context
        tree[child.name] = child.createReactiveTree();
        break;
      case 'prop':
        tree[child.name] = child.value;
        break;
    }
  });
  
  // Make the tree reactive using forestry.types
  const reactiveTree = forestry.types.TreeFunctor.create({
    root: tree,
    reactive: true
  });
  
  return reactiveTree;
}
```

### 4. context.pragma

**Purpose**: Context namespaces that affect scope.context and provide reactivity

**Type Definition**:
```typescript
// context.pragma.type
interface ContextPragmaType extends TtreePragmaType {
  affectsScope: true;
  namespace: string;
}
```

**Implementation Pattern**:
```typescript
// context.pragma
export default function contextPragma(children) {
  // Reuse ttree logic for reactive tree creation
  const contextTree = ttreePragma(children);
  
  // This affects scope.context
  scope.context = {
    ...scope.context,
    ...contextTree.getSnapshot()
  };
  
  // Set up reactivity to update scope when tree changes
  contextTree.subscribe((changes) => {
    // Update scope.context with changes
    Object.assign(scope.context, contextTree.getSnapshot());
  });
  
  return contextTree;
}
```

## Type System Integration

### Runtime Type Construction

Each foundation pragma contributes to the t scope:

```typescript
// Built during pragma processing
const t = {
  // From obj.pragma.type
  obj: {
    is: (value: any): value is ObjPragmaType => { /* implementation */ },
    validate: (value: any): boolean => { /* implementation */ },
    create: (props: Partial<ObjPragmaType>): ObjPragmaType => { /* implementation */ }
  },
  
  // From prop.pragma.type
  prop: {
    is: (value: any): value is PropPragmaType => { /* implementation */ },
    validate: (value: any): boolean => { /* implementation */ },
    create: (props: Partial<PropPragmaType>): PropPragmaType => { /* implementation */ }
  },
  
  // From ttree.pragma.type
  ttree: {
    is: (value: any): value is TtreePragmaType => { /* implementation */ },
    validate: (value: any): boolean => { /* implementation */ },
    create: (props: Partial<TtreePragmaType>): TtreePragmaType => { /* implementation */ }
  },
  
  // From context.pragma.type
  context: {
    is: (value: any): value is ContextPragmaType => { /* implementation */ },
    validate: (value: any): boolean => { /* implementation */ },
    create: (props: Partial<ContextPragmaType>): ContextPragmaType => { /* implementation */ }
  }
};
```

### Categorical Foundation

Foundation pragmas are built on cat.types:

```typescript
// Using categorical types from cat.types
import { CatObject, Morphism, Functor } from 'cat.types';

// ttree.pragma uses TreeFunctor from forestry.types
const TreeFunctor = forestry.types.TreeFunctor;

// context.pragma extends TreeFunctor with scope effects
const ContextFunctor = forestry.types.ScopedTree;
```

## Usage Examples

### Basic Object Composition
```
user/
├── obj.pragma
├── name.prop
├── email.prop
└── getDisplayName.method
```

### Reactive Context Tree
```
state/
├── user/
│   ├── context.pragma
│   ├── profile.ttree
│   │   ├── name.prop
│   │   └── avatar.prop
│   └── auth.ttree
│       ├── isLoggedIn.prop
│       └── token.prop
└── app/
    ├── context.pragma
    └── ui.ttree
        ├── theme.prop
        └── language.prop
```

## Integration with Higher-Level Pragmas

Foundation pragmas serve as building blocks for component pragmas:

- **component.pragma** uses obj.pragma for component structure
- **props.pragma** uses prop.pragma for property definitions
- **provider.pragma** uses context.pragma for state management
- **jsx.pragma** uses obj.pragma for element composition

This layered approach ensures that all higher-level pragmas are built on solid mathematical foundations provided by the categorical type system.
