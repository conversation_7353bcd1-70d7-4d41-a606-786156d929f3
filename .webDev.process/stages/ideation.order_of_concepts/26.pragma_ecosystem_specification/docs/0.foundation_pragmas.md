# Foundation Pragmas

## Overview

Foundation pragmas provide the atomic building blocks upon which all other pragmas are constructed. These pragmas implement the core categorical types and reactive structures that form the mathematical foundation of the SpiceTime architecture.

## Core Foundation Pragmas

### 1. obj.pragma

**Purpose**: Object composition with properties, methods, getters, and setters

**Type Definition**: See [obj.pragma.type.ts](../code/foundation/obj.pragma.type.ts)

**Implementation**: See [obj.pragma.ts](../code/foundation/obj.pragma.ts)

**Key Features**:
- Property definitions with scope resolution
- Method implementations
- Getter/setter descriptors
- Categorical object composition

### 2. prop.pragma

**Purpose**: Property definition with values resolved from scope

**Type Definition**: See [prop.pragma.type.ts](../code/foundation/prop.pragma.type.ts)

**Implementation**: See [prop.pragma.ts](../code/foundation/prop.pragma.ts)

**Key Features**:
- Scope term resolution via dot notation
- Static value assignment
- Runtime value computation
- Type-safe property access

### 3. ttree.pragma

**Purpose**: TreenityTree reactive structures that don't affect scopes

**Type Definition**: See [ttree.pragma.type.ts](../code/foundation/ttree.pragma.type.ts)

**Implementation**: See [ttree.pragma.ts](../code/foundation/ttree.pragma.ts)

**Key Features**:
- Pure reactive trees (scope-independent)
- Built on forestry.types TreeFunctor
- Observable state changes
- Transactional updates

### 4. context.pragma

**Purpose**: Context namespaces that affect scope.context and provide reactivity

**Type Definition**: See [context.pragma.type.ts](../code/foundation/context.pragma.type.ts)

**Implementation**: See [context.pragma.ts](../code/foundation/context.pragma.ts)

**Key Features**:
- Affects scope.context
- Reactive namespace management
- Kernel integration for state changes
- Hierarchical context composition

## Type System Integration

### Runtime Type Construction

Each foundation pragma contributes to the t scope through categorical type definitions.

**Type System Implementation**: See [type-system.ts](../code/foundation/type-system.ts)

**Runtime Operations**:
- `is(value)`: Type guard functions
- `validate(value)`: Validation with detailed errors
- `create(props)`: Type-safe construction

### Categorical Foundation

Foundation pragmas are built on cat.types and forestry.types:

**Categorical Integration**: See [categorical-foundation.ts](../code/foundation/categorical-foundation.ts)

**Key Concepts**:
- TreeFunctor for reactive structures
- CatObject for object composition
- Morphisms for transformations
- Functorial composition laws

## Usage Examples

### Basic Object Composition
```
user/
├── obj.pragma
├── name.prop
├── email.prop
└── getDisplayName.method
```

### Reactive Context Tree
```
state/
├── user/
│   ├── context.pragma
│   ├── profile.ttree
│   │   ├── name.prop
│   │   └── avatar.prop
│   └── auth.ttree
│       ├── isLoggedIn.prop
│       └── token.prop
└── app/
    ├── context.pragma
    └── ui.ttree
        ├── theme.prop
        └── language.prop
```

## Integration with Higher-Level Pragmas

Foundation pragmas serve as building blocks for React component pragmas:

- **component.pragma** uses obj.pragma for component structure
- **props.pragma** uses prop.pragma for property definitions
- **provider.pragma** uses context.pragma for state management
- **inst.pragma** uses obj.pragma for element composition

This layered approach ensures that all higher-level pragmas are built on solid mathematical foundations provided by the categorical type system.
