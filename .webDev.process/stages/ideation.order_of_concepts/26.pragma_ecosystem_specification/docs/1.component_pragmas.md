# Component Pragmas

## Overview

Component pragmas provide the building blocks for React component composition through filesystem structure. These pragmas enable the creation of components, their instantiation, and JSX composition while maintaining complete separation from state management.

## Core Component Pragmas

### 1. component.pragma

**Purpose**: Component type definition with numbered child pragma composition

**Type Definition**:
```typescript
// component.pragma.type
interface ComponentPragmaType {
  hooks: Array<{ sequence: number, pragma: 'hook', name: string }>;
  providers: Array<{ sequence: number, pragma: 'provider', name: string }>;
  jsx: { pragma: 'jsx', structure: any };
  props: { pragma: 'props', definitions: any };
  refs: Array<{ pragma: 'ref', name: string }>;
}
```

**Implementation Pattern**:
```typescript
// component.pragma
export default function componentPragma(children) {
  let hooks = [];
  let providers = [];
  let jsx = null;
  let props = {};
  let refs = [];
  
  children.forEach(child => {
    if (child.name.match(/^\d+\.hook/)) {
      hooks.push({
        sequence: parseInt(child.name),
        hook: child
      });
    }
    if (child.name.match(/^\d+\.provider/)) {
      providers.push({
        sequence: parseInt(child.name),
        provider: child
      });
    }
    if (child.pragma === 'jsx') {
      jsx = child;
    }
    if (child.pragma === 'props') {
      props = child;
    }
    if (child.pragma === 'ref') {
      refs.push(child);
    }
  });
  
  // Sort by sequence numbers
  hooks.sort((a, b) => a.sequence - b.sequence);
  providers.sort((a, b) => a.sequence - b.sequence);
  
  return {
    type: 'component',
    hooks,
    providers,
    jsx,
    props,
    refs,
    generateScript: () => generateComponentScript(hooks, providers, jsx, props, refs)
  };
}

function generateComponentScript(hooks, providers, jsx, props, refs) {
  const hookStatements = hooks.map(h => h.hook.generateHookStatement()).join('\n  ');
  const refStatements = refs.map(r => r.generateRefStatement()).join('\n  ');
  const jsxCode = jsx ? jsx.generateJSX() : 'return null;';
  
  return `
function Component(props) {
  ${refStatements}
  ${hookStatements}
  
  ${jsxCode}
}`;
}
```

### 2. inst.pragma

**Purpose**: Component instantiation with props

**Type Definition**:
```typescript
// inst.pragma.type
interface InstPragmaType {
  component: string; // Reference to component
  props: { pragma: 'props' };
  key?: string;
  ref?: string;
}
```

**Implementation Pattern**:
```typescript
// inst.pragma
export default function instPragma(children) {
  let componentRef = null;
  let props = {};
  let key = null;
  let ref = null;
  
  children.forEach(child => {
    if (child.pragma === 'component') {
      componentRef = child;
    }
    if (child.pragma === 'props') {
      props = child;
    }
    if (child.name === 'key') {
      key = child.value;
    }
    if (child.name === 'ref') {
      ref = child.value;
    }
  });
  
  return {
    type: 'instance',
    component: componentRef,
    props,
    key,
    ref,
    generateJSX: (scope) => {
      const componentName = componentRef.name;
      const propsCode = props.generatePropsCode(scope);
      const keyAttr = key ? ` key={${key}}` : '';
      const refAttr = ref ? ` ref={${ref}}` : '';
      
      return `<${componentName}${keyAttr}${refAttr} ${propsCode} />`;
    }
  };
}
```

### 3. hook.pragma

**Purpose**: React hook definitions with kernel integration

**Type Definition**:
```typescript
// hook.pragma.type
interface HookPragmaType {
  hookType: 'useState' | 'useEffect' | 'useContext' | 'useRef' | 'custom';
  name: string;
  parameters: any[];
  kernelIntegration: boolean;
}
```

**Implementation Pattern**:
```typescript
// hook.pragma
export default function hookPragma(definition) {
  return {
    type: 'hook',
    hookType: definition.hookType,
    name: definition.name,
    parameters: definition.parameters || [],
    generateHookStatement: () => {
      switch (definition.hookType) {
        case 'useState':
          const [initialValue] = definition.parameters;
          return `const [${definition.name}, set${capitalize(definition.name)}] = useState(${initialValue});`;
          
        case 'useEffect':
          const [effect, deps] = definition.parameters;
          const depsCode = deps ? `[${deps.join(', ')}]` : '[]';
          return `useEffect(() => { ${effect} }, ${depsCode});`;
          
        case 'useContext':
          const [contextName] = definition.parameters;
          return `const ${definition.name} = useContext(${contextName});`;
          
        case 'useRef':
          const [refInitial] = definition.parameters;
          return `const ${definition.name} = useRef(${refInitial || 'null'});`;
          
        case 'custom':
          const [hookCall] = definition.parameters;
          return `const ${definition.name} = ${hookCall};`;
          
        default:
          return `// Unknown hook type: ${definition.hookType}`;
      }
    }
  };
}

function capitalize(str) {
  return str.charAt(0).toUpperCase() + str.slice(1);
}
```

### 4. jsx.pragma

**Purpose**: JSX tree composition through numbered sequences

**Type Definition**:
```typescript
// jsx.pragma.type
interface JsxPragmaType {
  elements: Array<{
    sequence: number;
    depth: number;
    element: 'inst' | 'text' | 'expression';
    [key: string]: any;
  }>;
}
```

**Implementation Pattern**:
```typescript
// jsx.pragma
export default function jsxPragma(children) {
  const elements = [];
  
  children.forEach(child => {
    if (child.name.match(/^\d+\./)) {
      const sequence = parseInt(child.name);
      const depth = child.name.split('.').length - 1;
      
      elements.push({
        sequence,
        depth,
        element: child,
        type: child.pragma
      });
    }
  });
  
  // Sort by sequence for predictable order
  elements.sort((a, b) => a.sequence - b.sequence);
  
  return {
    type: 'jsx',
    elements,
    generateJSX: (scope) => buildJSXTree(elements, scope)
  };
}

function buildJSXTree(elements, scope) {
  const stack = [];
  let result = '';
  
  elements.forEach(element => {
    const indent = '  '.repeat(element.depth);
    
    switch (element.type) {
      case 'inst':
        const jsx = element.element.generateJSX(scope);
        result += `${indent}${jsx}\n`;
        break;
        
      case 'text':
        result += `${indent}${element.element.text}\n`;
        break;
        
      case 'expression':
        result += `${indent}{${element.element.expression}}\n`;
        break;
    }
  });
  
  return `return (\n${result});`;
}
```

### 5. props.pragma

**Purpose**: Props generation from scope terms

**Type Definition**:
```typescript
// props.pragma.type
interface PropsPragmaType {
  properties: Array<{
    name: string;
    value?: any;
    scopeReference?: string;
    expression?: string;
  }>;
}
```

**Implementation Pattern**:
```typescript
// props.pragma
export default function propsPragma(children) {
  const properties = [];
  
  children.forEach(child => {
    if (child.pragma === 'prop') {
      properties.push({
        name: child.name,
        value: child.value,
        scopeReference: child.scopeReference,
        expression: child.expression
      });
    }
  });
  
  return {
    type: 'props',
    properties,
    generatePropsCode: (scope) => {
      const propStrings = properties.map(prop => {
        let value;
        
        if (prop.scopeReference) {
          // Resolve from scope: scope.user.name
          value = `{${prop.scopeReference}}`;
        } else if (prop.expression) {
          // JavaScript expression
          value = `{${prop.expression}}`;
        } else if (typeof prop.value === 'string') {
          // String literal
          value = `"${prop.value}"`;
        } else {
          // Other values
          value = `{${JSON.stringify(prop.value)}}`;
        }
        
        return `${prop.name}=${value}`;
      });
      
      return propStrings.join(' ');
    }
  };
}
```

### 6. ref.pragma

**Purpose**: React ref handling

**Type Definition**:
```typescript
// ref.pragma.type
interface RefPragmaType {
  name: string;
  type: 'useRef' | 'createRef' | 'forwardRef';
  initialValue?: any;
}
```

**Implementation Pattern**:
```typescript
// ref.pragma
export default function refPragma(definition) {
  return {
    type: 'ref',
    name: definition.name,
    refType: definition.type,
    initialValue: definition.initialValue,
    generateRefStatement: () => {
      switch (definition.type) {
        case 'useRef':
          const initial = definition.initialValue || 'null';
          return `const ${definition.name} = useRef(${initial});`;
          
        case 'createRef':
          return `const ${definition.name} = createRef();`;
          
        case 'forwardRef':
          return `// Forward ref handled in component wrapper`;
          
        default:
          return `// Unknown ref type: ${definition.type}`;
      }
    }
  };
}
```

## Component Generation Process

### 1. Component Script Generation

Components are generated via `Function(script)` in index.js:

```typescript
// In component index.js
const componentDefinition = componentPragma(children);
const componentScript = componentDefinition.generateScript();
const ComponentFunction = Function('React', 'useState', 'useEffect', 'useContext', 'useRef', `
  const { useState, useEffect, useContext, useRef } = React;
  ${componentScript}
  return Component;
`);

export default ComponentFunction(React);
```

### 2. HOC Integration

For kernel integration, components are wrapped with HOCs:

```typescript
// withKernelIntegration HOC
function withKernelIntegration(Component) {
  return function KernelIntegratedComponent(props) {
    // Wait for kernel approval before rendering
    const [approved, setApproved] = useState(false);
    
    useEffect(() => {
      kernel.requestApproval(componentId).then(() => {
        setApproved(true);
      });
    }, []);
    
    if (!approved) return null;
    
    return <Component {...props} />;
  };
}
```

## Usage Examples

### Basic Component Structure
```
header/
├── component.pragma
├── 1.hook.useState
├── 2.hook.useEffect
├── jsx.pragma
│   ├── 1.nav.inst
│   │   ├── component -> nav/component.pragma
│   │   └── props.pragma
│   │       ├── className.prop_{scope.styles.nav}
│   │       └── items.prop_{scope.navigation.items}
│   └── 2.logo.inst
│       ├── component -> logo/component.pragma
│       └── props.pragma
│           └── src.prop_{scope.assets.logo}
└── props.pragma
    ├── title.prop
    └── onMenuClick.prop
```

### Generated Component
```javascript
function Component(props) {
  const [isOpen, setIsOpen] = useState(false);
  
  useEffect(() => {
    // Effect logic
  }, []);
  
  return (
    <nav className={scope.styles.nav} items={scope.navigation.items}>
      <Logo src={scope.assets.logo} />
    </nav>
  );
}
```

## Integration with Foundation Pragmas

Component pragmas build upon foundation pragmas:

- **component.pragma** uses obj.pragma for structure
- **props.pragma** uses prop.pragma for property definitions
- **jsx.pragma** uses obj.pragma for element composition
- **hook.pragma** integrates with kernel through context.pragma

This ensures that all component functionality is built on the solid mathematical foundation provided by the categorical type system.
