# Advanced Composition Pragmas

## Overview

Advanced composition pragmas provide sophisticated patterns for component composition, including sequential ordering, compound components, higher-order components, and complex JSX structures. These pragmas enable any React pattern to be expressed through filesystem structure while maintaining the atomic nature of the pragma ecosystem.

## Core Advanced Pragmas

### 1. seq.pragma

**Purpose**: Sequential composition with numbered ordering for predictable execution

**Type Definition**:
```typescript
// seq.pragma.type
interface SeqPragmaType {
  items: Array<{
    sequence: number;
    pragma: string;
    name: string;
    [key: string]: any;
  }>;
  ordering: 'ascending' | 'descending' | 'custom';
}
```

**Implementation Pattern**:
```typescript
// seq.pragma
export default function seqPragma(children) {
  const items = [];
  
  children.forEach(child => {
    if (child.name.match(/^\d+\./)) {
      const sequence = parseInt(child.name);
      items.push({
        sequence,
        pragma: child.pragma,
        name: child.name,
        child
      });
    }
  });
  
  // Sort by sequence number
  items.sort((a, b) => a.sequence - b.sequence);
  
  return {
    type: 'sequence',
    items,
    
    // Execute items in sequence
    execute: (context) => {
      return items.map(item => item.child.execute(context));
    },
    
    // Compose items into a single result
    compose: (context) => {
      const results = items.map(item => item.child.compose(context));
      return combineResults(results);
    }
  };
}

function combineResults(results) {
  // Combine based on result types
  if (results.every(r => typeof r === 'object')) {
    return Object.assign({}, ...results);
  }
  if (results.every(r => Array.isArray(r))) {
    return results.flat();
  }
  return results;
}
```

### 2. compound.pragma

**Purpose**: Compound component patterns with sub-components

**Type Definition**:
```typescript
// compound.pragma.type
interface CompoundPragmaType {
  mainComponent: { pragma: 'component' };
  subComponents: Array<{
    name: string;
    component: { pragma: 'component' };
    attachTo: 'static' | 'instance';
  }>;
  namespace: string;
}
```

**Implementation Pattern**:
```typescript
// compound.pragma
export default function compoundPragma(children) {
  let mainComponent = null;
  const subComponents = [];
  let namespace = 'Compound';
  
  children.forEach(child => {
    if (child.pragma === 'component' && child.name === 'main') {
      mainComponent = child;
    }
    if (child.pragma === 'component' && child.name !== 'main') {
      subComponents.push({
        name: child.name,
        component: child,
        attachTo: child.attachTo || 'static'
      });
    }
    if (child.name === 'namespace') {
      namespace = child.value;
    }
  });
  
  return {
    type: 'compound',
    mainComponent,
    subComponents,
    namespace,
    
    generateCompound: () => {
      const MainComponent = mainComponent.generateComponent();
      
      // Attach sub-components as static properties
      subComponents.forEach(sub => {
        if (sub.attachTo === 'static') {
          MainComponent[sub.name] = sub.component.generateComponent();
        }
      });
      
      return MainComponent;
    }
  };
}
```

**Usage Example**:
```
modal/
├── compound.pragma
├── main.component.pragma
│   ├── jsx.pragma
│   │   ├── 1.header.inst
│   │   ├── 2.body.inst
│   │   └── 3.footer.inst
│   └── props.pragma
│       ├── isOpen.prop
│       └── onClose.prop
├── header.component.pragma
│   ├── jsx.pragma
│   │   └── 1.title.text
│   └── props.pragma
│       └── title.prop
├── body.component.pragma
│   ├── jsx.pragma
│   │   └── 1.children.expression_{children}
│   └── props.pragma
│       └── children.prop
└── footer.component.pragma
    ├── jsx.pragma
    │   ├── 1.cancel.button.inst
    │   └── 2.confirm.button.inst
    └── props.pragma
        ├── onCancel.prop
        └── onConfirm.prop
```

**Generated Usage**:
```jsx
<Modal isOpen={true} onClose={handleClose}>
  <Modal.Header title="Confirm Action" />
  <Modal.Body>
    Are you sure you want to proceed?
  </Modal.Body>
  <Modal.Footer onCancel={handleCancel} onConfirm={handleConfirm} />
</Modal>
```

### 3. hoc.pragma

**Purpose**: Higher-order component wrappers

**Type Definition**:
```typescript
// hoc.pragma.type
interface HocPragmaType {
  wrapperType: 'memo' | 'forwardRef' | 'withProps' | 'withState' | 'custom';
  configuration: any;
  targetComponent: { pragma: 'component' };
}
```

**Implementation Pattern**:
```typescript
// hoc.pragma
export default function hocPragma(definition) {
  return {
    type: 'hoc',
    wrapperType: definition.wrapperType,
    configuration: definition.configuration,
    targetComponent: definition.targetComponent,
    
    generateHOC: () => {
      const TargetComponent = definition.targetComponent.generateComponent();
      
      switch (definition.wrapperType) {
        case 'memo':
          return React.memo(TargetComponent, definition.configuration?.areEqual);
          
        case 'forwardRef':
          return React.forwardRef((props, ref) => (
            <TargetComponent {...props} ref={ref} />
          ));
          
        case 'withProps':
          return (props) => (
            <TargetComponent {...definition.configuration} {...props} />
          );
          
        case 'withState':
          return (props) => {
            const [state, setState] = useState(definition.configuration.initialState);
            return (
              <TargetComponent 
                {...props} 
                {...state} 
                setState={setState} 
              />
            );
          };
          
        case 'custom':
          return definition.configuration.wrapper(TargetComponent);
          
        default:
          return TargetComponent;
      }
    }
  };
}
```

### 4. render.pragma

**Purpose**: Render prop patterns and function-as-children

**Type Definition**:
```typescript
// render.pragma.type
interface RenderPragmaType {
  renderType: 'children' | 'prop' | 'slot';
  propName?: string;
  parameters: string[];
  implementation: string;
}
```

**Implementation Pattern**:
```typescript
// render.pragma
export default function renderPragma(definition) {
  return {
    type: 'render',
    renderType: definition.renderType,
    propName: definition.propName,
    parameters: definition.parameters,
    
    generateRenderProp: () => {
      const params = definition.parameters.join(', ');
      const impl = definition.implementation;
      
      switch (definition.renderType) {
        case 'children':
          return `{typeof children === 'function' ? children(${params}) : children}`;
          
        case 'prop':
          const propName = definition.propName || 'render';
          return `{${propName} && ${propName}(${params})}`;
          
        case 'slot':
          return `{${definition.propName} || ${impl}}`;
          
        default:
          return impl;
      }
    }
  };
}
```

### 5. portal.pragma

**Purpose**: React portal creation for rendering outside component tree

**Type Definition**:
```typescript
// portal.pragma.type
interface PortalPragmaType {
  target: string; // DOM selector or ref
  component: { pragma: 'component' };
  conditional?: string; // Condition for rendering
}
```

**Implementation Pattern**:
```typescript
// portal.pragma
export default function portalPragma(definition) {
  return {
    type: 'portal',
    target: definition.target,
    component: definition.component,
    conditional: definition.conditional,
    
    generatePortal: () => {
      const Component = definition.component.generateComponent();
      const condition = definition.conditional || 'true';
      
      return `
        {${condition} && ReactDOM.createPortal(
          <Component {...props} />,
          ${definition.target.startsWith('#') || definition.target.startsWith('.') 
            ? `document.querySelector('${definition.target}')`
            : definition.target
          }
        )}
      `;
    }
  };
}
```

## Complex JSX Composition Patterns

### 1. Conditional Rendering

```
component/
├── jsx.pragma
│   ├── 1.conditional.expression_{isLoggedIn}
│   │   ├── true.branch
│   │   │   ├── 1.welcome.inst
│   │   │   └── 2.dashboard.inst
│   │   └── false.branch
│   │       └── 1.login.inst
│   └── 2.footer.inst
```

### 2. List Rendering

```
component/
├── jsx.pragma
│   ├── 1.list.expression_{items.map((item, index) => (
│   │   ├── key.expression_{item.id}
│   │   ├── 1.item.inst
│   │   │   ├── component -> listItem/component.pragma
│   │   │   └── props.pragma
│   │   │       ├── item.expression_{item}
│   │   │       └── index.expression_{index}
│   │   ))}
```

### 3. Fragment Composition

```
component/
├── jsx.pragma
│   ├── 1.fragment.start
│   ├── 2.header.inst
│   ├── 3.content.inst
│   ├── 4.footer.inst
│   └── 5.fragment.end
```

## Integration with Kernel System

### Priority-Based Component Rendering

```typescript
// HOC for kernel integration
function withKernelPriority(Component, priority = 'normal') {
  return function KernelPriorityComponent(props) {
    const [approved, setApproved] = useState(false);
    
    useEffect(() => {
      const scheduleRender = async () => {
        const taskId = await kernel.scheduleTask('component-render', priority);
        await kernel.waitForApproval(taskId);
        setApproved(true);
      };
      
      scheduleRender();
    }, []);
    
    if (!approved) {
      return null; // or loading component
    }
    
    return <Component {...props} />;
  };
}
```

### Batched Component Updates

```typescript
// Batch multiple component updates
function withBatchedUpdates(Component) {
  return function BatchedComponent(props) {
    const [pendingUpdates, setPendingUpdates] = useState([]);
    
    useEffect(() => {
      if (pendingUpdates.length > 0) {
        const processBatch = async () => {
          const taskId = await kernel.scheduleTask('batch-update', 'high');
          await kernel.waitForApproval(taskId);
          
          // Process all pending updates
          pendingUpdates.forEach(update => update());
          setPendingUpdates([]);
        };
        
        processBatch();
      }
    }, [pendingUpdates]);
    
    return <Component {...props} />;
  };
}
```

## Usage Examples

### Complete Advanced Component

```
advancedModal/
├── compound.pragma
├── main.component.pragma
│   ├── 1.hook.useState
│   ├── 2.hook.useEffect
│   ├── jsx.pragma
│   │   ├── 1.portal.pragma
│   │   │   ├── target.prop_#modal-root
│   │   │   └── component.pragma
│   │   │       ├── jsx.pragma
│   │   │       │   ├── 1.overlay.inst
│   │   │       │   └── 2.content.inst
│   │   │       │       ├── 1.header.inst
│   │   │       │       ├── 2.body.inst
│   │   │       │       └── 3.footer.inst
│   │   │       └── props.pragma
│   │   │           ├── isOpen.prop
│   │   │           └── onClose.prop
│   └── hoc.pragma
│       ├── wrapperType.prop_memo
│       └── configuration.prop_{areEqual: (prev, next) => prev.isOpen === next.isOpen}
├── header.component.pragma
├── body.component.pragma
└── footer.component.pragma
```

This advanced composition system enables any React pattern to be expressed through filesystem structure while maintaining the atomic, composable nature of the pragma ecosystem. The integration with the kernel system ensures that all component operations respect the SpiceTime priority system and resource management.
