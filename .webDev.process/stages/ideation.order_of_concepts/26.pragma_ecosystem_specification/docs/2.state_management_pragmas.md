# State Management Pragmas

## Overview

State management pragmas provide a dedicated filesystem branch for reactive state trees, completely separated from JSX composition. These pragmas enable the creation of reactive contexts, providers, and state trees that integrate with the SpiceTime kernel while maintaining independence from React's component lifecycle.

## Dedicated State Branch Architecture

### Filesystem Structure
```
state/
├── user/
│   ├── context.pragma      # Reactive user context
│   ├── profile.ttree       # User profile data tree
│   │   ├── name.prop
│   │   ├── email.prop
│   │   └── avatar.prop
│   └── auth.ttree          # Authentication state tree
│       ├── isLoggedIn.prop
│       └── token.prop
├── app/
│   ├── context.pragma      # Global app context
│   └── ui.ttree           # UI state tree
│       ├── theme.prop
│       └── language.prop
└── provider.pragma         # Root provider composition
    ├── value/
    │   ├── user.context    # References user/context.pragma
    │   └── app.context     # References app/context.pragma
    └── 1.provider          # Sequence in provider chain
```

## Core State Management Pragmas

### 1. provider.pragma

**Purpose**: React context providers with sequenced composition

**Type Definition**:
```typescript
// provider.pragma.type
interface ProviderPragmaType {
  value: { pragma: 'context' | 'ttree' };
  sequence: Array<{ order: number, pragma: 'provider' }>;
  contextName: string;
}
```

**Implementation Pattern**:
```typescript
// provider.pragma
export default function providerPragma(children) {
  let value = {};
  let sequence = [];
  let contextName = 'DefaultContext';
  
  children.forEach(child => {
    if (child.pragma === 'value') {
      value = child.getValue(); // Gets ttree or context
    }
    if (child.name.match(/^\d+\.provider/)) {
      sequence.push({
        order: parseInt(child.name),
        provider: child
      });
    }
    if (child.name === 'contextName') {
      contextName = child.value;
    }
  });
  
  // Sort providers by sequence
  sequence.sort((a, b) => a.order - b.order);
  
  // Create React context
  const Context = React.createContext(value);
  
  return {
    type: 'provider',
    Context,
    contextName,
    value,
    sequence,
    
    // Generate provider component
    Provider: ({ children }) => {
      const [state, setState] = useState(value.getSnapshot ? value.getSnapshot() : value);
      
      useEffect(() => {
        if (value.subscribe) {
          // Subscribe to reactive tree changes
          const unsubscribe = value.subscribe((changes) => {
            setState(value.getSnapshot());
          });
          return unsubscribe;
        }
      }, []);
      
      return (
        <Context.Provider value={state}>
          {children}
        </Context.Provider>
      );
    },
    
    // Generate hook for consuming context
    useContext: () => useContext(Context)
  };
}
```

### 2. Enhanced context.pragma (State-Specific)

**Purpose**: Context namespaces for state management with kernel integration

**Type Definition**:
```typescript
// context.pragma.type (state-specific)
interface StateContextPragmaType extends ContextPragmaType {
  kernelIntegration: boolean;
  persistenceKey?: string;
  syncStrategy: 'immediate' | 'batched' | 'manual';
}
```

**Implementation Pattern**:
```typescript
// context.pragma (in state branch)
export default function stateContextPragma(children) {
  // Build reactive tree using ttree.pragma
  const contextTree = ttreePragma(children);
  
  // Kernel integration for state changes
  const kernelIntegratedTree = wrapWithKernel(contextTree);
  
  // Update scope.context
  scope.context = {
    ...scope.context,
    ...kernelIntegratedTree.getSnapshot()
  };
  
  // Set up persistence if configured
  if (definition.persistenceKey) {
    setupPersistence(kernelIntegratedTree, definition.persistenceKey);
  }
  
  return kernelIntegratedTree;
}

function wrapWithKernel(tree) {
  return {
    ...tree,
    
    // Kernel-controlled updates
    update: async (path, value) => {
      // Request kernel approval for state change
      const taskId = await kernel.scheduleTask('state-update', 'normal');
      await kernel.waitForApproval(taskId);
      
      // Apply the update
      tree.update(path, value);
    },
    
    // Batch updates through kernel
    batchUpdate: async (updates) => {
      const taskId = await kernel.scheduleTask('state-batch-update', 'normal');
      await kernel.waitForApproval(taskId);
      
      updates.forEach(({ path, value }) => {
        tree.update(path, value);
      });
    }
  };
}
```

### 3. Enhanced ttree.pragma (State-Specific)

**Purpose**: Reactive state trees with persistence and synchronization

**Type Definition**:
```typescript
// ttree.pragma.type (state-specific)
interface StateTtreePragmaType extends TtreePragmaType {
  persistence: {
    enabled: boolean;
    key: string;
    strategy: 'localStorage' | 'sessionStorage' | 'indexedDB' | 'remote';
  };
  synchronization: {
    enabled: boolean;
    endpoint?: string;
    conflictResolution: 'client' | 'server' | 'merge';
  };
}
```

**Implementation Pattern**:
```typescript
// ttree.pragma (in state branch)
export default function stateTtreePragma(children, config) {
  // Create base reactive tree
  const baseTree = ttreePragma(children);
  
  // Add state-specific capabilities
  const stateTree = enhanceForState(baseTree, config);
  
  return stateTree;
}

function enhanceForState(tree, config) {
  const enhanced = {
    ...tree,
    
    // Persistence layer
    persist: () => {
      if (config.persistence?.enabled) {
        const data = tree.getSnapshot();
        switch (config.persistence.strategy) {
          case 'localStorage':
            localStorage.setItem(config.persistence.key, JSON.stringify(data));
            break;
          case 'sessionStorage':
            sessionStorage.setItem(config.persistence.key, JSON.stringify(data));
            break;
          case 'indexedDB':
            // IndexedDB implementation
            break;
          case 'remote':
            // Remote persistence implementation
            break;
        }
      }
    },
    
    // Load from persistence
    hydrate: () => {
      if (config.persistence?.enabled) {
        let data;
        switch (config.persistence.strategy) {
          case 'localStorage':
            data = localStorage.getItem(config.persistence.key);
            break;
          case 'sessionStorage':
            data = sessionStorage.getItem(config.persistence.key);
            break;
          // Other strategies...
        }
        
        if (data) {
          tree.loadSnapshot(JSON.parse(data));
        }
      }
    },
    
    // Synchronization
    sync: async () => {
      if (config.synchronization?.enabled && config.synchronization.endpoint) {
        const localData = tree.getSnapshot();
        const response = await fetch(config.synchronization.endpoint, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(localData)
        });
        
        const remoteData = await response.json();
        
        // Handle conflicts based on strategy
        switch (config.synchronization.conflictResolution) {
          case 'client':
            // Client wins, no action needed
            break;
          case 'server':
            tree.loadSnapshot(remoteData);
            break;
          case 'merge':
            const merged = mergeStates(localData, remoteData);
            tree.loadSnapshot(merged);
            break;
        }
      }
    }
  };
  
  // Auto-persist on changes
  if (config.persistence?.enabled) {
    enhanced.subscribe(() => {
      enhanced.persist();
    });
  }
  
  // Auto-sync on changes
  if (config.synchronization?.enabled) {
    enhanced.subscribe(debounce(() => {
      enhanced.sync();
    }, 1000));
  }
  
  return enhanced;
}
```

## State Tree Composition Patterns

### 1. Hierarchical State Structure

```
state/
├── global/
│   ├── context.pragma
│   ├── app.ttree
│   │   ├── version.prop
│   │   ├── environment.prop
│   │   └── features.ttree
│   │       ├── darkMode.prop
│   │       └── notifications.prop
│   └── user.ttree
│       ├── preferences.ttree
│       │   ├── language.prop
│       │   └── timezone.prop
│       └── session.ttree
│           ├── token.prop
│           └── expiresAt.prop
├── ui/
│   ├── context.pragma
│   ├── navigation.ttree
│   │   ├── currentRoute.prop
│   │   ├── breadcrumbs.prop
│   │   └── sidebarOpen.prop
│   └── modals.ttree
│       ├── activeModal.prop
│       └── modalData.prop
└── data/
    ├── context.pragma
    ├── cache.ttree
    │   ├── users.prop
    │   ├── posts.prop
    │   └── comments.prop
    └── loading.ttree
        ├── isLoading.prop
        └── loadingStates.prop
```

### 2. Provider Composition

```
state/
└── provider.pragma
    ├── value/
    │   ├── global.context
    │   ├── ui.context
    │   └── data.context
    ├── 1.provider.global
    ├── 2.provider.ui
    └── 3.provider.data
```

**Generated Provider Structure**:
```jsx
function AppProviders({ children }) {
  return (
    <GlobalProvider>
      <UIProvider>
        <DataProvider>
          {children}
        </DataProvider>
      </UIProvider>
    </GlobalProvider>
  );
}
```

## Integration with Component Pragmas

### State Access in Components

Components access state through generated hooks:

```typescript
// In component
1.hook.useContext
  ├── contextName.prop_GlobalContext
  └── statePath.prop_user.preferences
```

**Generated Hook**:
```javascript
const userPreferences = useContext(GlobalContext).user.preferences;
```

### State Updates

State updates go through kernel-controlled actions:

```typescript
// In component
2.hook.useCallback
  ├── name.prop_updateUserPreference
  ├── callback.expression_{
      (key, value) => {
        globalContext.update(['user', 'preferences', key], value);
      }
    }
  └── deps.prop_[globalContext]
```

## Kernel Integration

### Priority-Based State Updates

```typescript
// State updates respect kernel priorities
const updateState = async (path, value, priority = 'normal') => {
  const taskId = await kernel.scheduleTask('state-update', priority);
  await kernel.waitForApproval(taskId);
  
  // Apply state change
  stateTree.update(path, value);
};
```

### Batched Updates

```typescript
// Multiple state changes batched through kernel
const batchStateUpdates = async (updates) => {
  const taskId = await kernel.scheduleTask('state-batch', 'high');
  await kernel.waitForApproval(taskId);
  
  updates.forEach(({ path, value }) => {
    stateTree.update(path, value);
  });
};
```

## Benefits of Separated State Management

1. **Clean Separation**: State logic completely independent from JSX composition
2. **Kernel Control**: All state changes go through kernel scheduling
3. **Persistence**: Built-in persistence strategies for state trees
4. **Synchronization**: Automatic synchronization with remote state
5. **Type Safety**: Full type safety through categorical type system
6. **Reactivity**: Automatic updates to components when state changes
7. **Performance**: Efficient updates through batching and prioritization

This separation ensures that state management remains clean, predictable, and performant while integrating seamlessly with the SpiceTime kernel and React component system.
