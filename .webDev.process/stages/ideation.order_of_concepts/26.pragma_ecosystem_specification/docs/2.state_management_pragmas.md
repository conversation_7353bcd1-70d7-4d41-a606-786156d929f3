# State Management Pragmas

## Overview

State management pragmas provide a dedicated filesystem branch for reactive state trees, completely separated from React component composition. These pragmas enable the creation of reactive contexts, providers, and state trees that integrate with the SpiceTime kernel while maintaining independence from React's component lifecycle.

## Dedicated State Branch Architecture

### Filesystem Structure
```
state/
├── user/
│   ├── context.pragma      # Reactive user context
│   ├── profile.ttree       # User profile data tree
│   │   ├── name.prop
│   │   ├── email.prop
│   │   └── avatar.prop
│   └── auth.ttree          # Authentication state tree
│       ├── isLoggedIn.prop
│       └── token.prop
├── app/
│   ├── context.pragma      # Global app context
│   └── ui.ttree           # UI state tree
│       ├── theme.prop
│       └── language.prop
└── provider.pragma         # Root provider composition
    ├── value/
    │   ├── user.context    # References user/context.pragma
    │   └── app.context     # References app/context.pragma
    └── 1.provider          # Sequence in provider chain
```

## Core State Management Pragmas

### 1. provider.pragma

**Purpose**: React context providers with sequenced composition

**Type Definition**: See [provider.pragma.type.ts](../code/state/provider.pragma.type.ts)

**Implementation**: See [provider.pragma.ts](../code/state/provider.pragma.ts)

**Key Features**:
- Sequential provider composition (1.provider, 2.provider, etc.)
- Value resolution from ttree or context pragmas
- React context creation and management
- Automatic subscription to reactive state changes

### 2. Enhanced context.pragma (State-Specific)

**Purpose**: Context namespaces for state management with kernel integration

**Type Definition**: See [context.pragma.type.ts](../code/state/context.pragma.type.ts)

**Implementation**: See [context.pragma.ts](../code/state/context.pragma.ts)

**State-Specific Features**:
- Kernel integration for state changes
- Persistence configuration
- Synchronization strategies
- Conflict resolution policies

### 3. Enhanced ttree.pragma (State-Specific)

**Purpose**: Reactive state trees with persistence and synchronization

**Type Definition**: See [ttree.pragma.type.ts](../code/state/ttree.pragma.type.ts)

**Implementation**: See [ttree.pragma.ts](../code/state/ttree.pragma.ts)

**State-Specific Features**:
- Persistence strategies (localStorage, sessionStorage, indexedDB, remote)
- Synchronization with remote endpoints
- Conflict resolution (client, server, merge)
- Auto-persistence and auto-sync

## State Tree Composition Patterns

### 1. Hierarchical State Structure

**Example Structure**: See [hierarchical-state-example.md](../code/state/examples/hierarchical-state-example.md)

```
state/
├── global/
│   ├── context.pragma
│   ├── app.ttree
│   │   ├── version.prop
│   │   ├── environment.prop
│   │   └── features.ttree
│   │       ├── darkMode.prop
│   │       └── notifications.prop
│   └── user.ttree
│       ├── preferences.ttree
│       │   ├── language.prop
│       │   └── timezone.prop
│       └── session.ttree
│           ├── token.prop
│           └── expiresAt.prop
├── ui/
│   ├── context.pragma
│   ├── navigation.ttree
│   │   ├── currentRoute.prop
│   │   ├── breadcrumbs.prop
│   │   └── sidebarOpen.prop
│   └── modals.ttree
│       ├── activeModal.prop
│       └── modalData.prop
└── data/
    ├── context.pragma
    ├── cache.ttree
    │   ├── users.prop
    │   ├── posts.prop
    │   └── comments.prop
    └── loading.ttree
        ├── isLoading.prop
        └── loadingStates.prop
```

### 2. Provider Composition

**Provider Composition**: See [provider-composition.ts](../code/state/provider-composition.ts)

```
state/
└── provider.pragma
    ├── value/
    │   ├── global.context
    │   ├── ui.context
    │   └── data.context
    ├── 1.provider.global
    ├── 2.provider.ui
    └── 3.provider.data
```

**Generated Provider Structure**:
```jsx
function AppProviders({ children }) {
  return (
    <GlobalProvider>
      <UIProvider>
        <DataProvider>
          {children}
        </DataProvider>
      </UIProvider>
    </GlobalProvider>
  );
}
```

## Kernel Integration

### Priority-Based State Updates

**Kernel Integration**: See [kernel-state-integration.ts](../code/state/kernel-state-integration.ts)

**Features**:
- Priority-based state update scheduling
- Kernel approval for state changes
- Batched state updates
- Resource allocation for state operations

### State Update Pipeline

**Update Pipeline**: See [state-update-pipeline.ts](../code/state/state-update-pipeline.ts)

**Process**:
1. State change request
2. Kernel task scheduling
3. Priority-based queuing
4. Approval waiting
5. State tree update
6. Reactive propagation

## Persistence and Synchronization

### Persistence Strategies

**Persistence System**: See [persistence-system.ts](../code/state/persistence-system.ts)

**Supported Strategies**:
- `localStorage`: Browser local storage
- `sessionStorage`: Session-based storage
- `indexedDB`: Client-side database
- `remote`: Server-side persistence

### Synchronization System

**Synchronization**: See [synchronization-system.ts](../code/state/synchronization-system.ts)

**Features**:
- Real-time synchronization with remote endpoints
- Conflict resolution strategies
- Offline support with sync queuing
- Delta synchronization for efficiency

## Integration with React Components

### State Access in Components

**State Access**: See [state-access-patterns.ts](../code/state/state-access-patterns.ts)

Components access state through generated hooks:

```typescript
// In component
1.hook.useContext
  ├── contextName.prop_GlobalContext
  └── statePath.prop_user.preferences
```

**Generated Hook**:
```javascript
const userPreferences = useContext(GlobalContext).user.preferences;
```

### State Updates from Components

**State Updates**: See [state-update-patterns.ts](../code/state/state-update-patterns.ts)

State updates go through kernel-controlled actions:

```typescript
// In component
2.hook.useCallback
  ├── name.prop_updateUserPreference
  ├── callback.expression_{
      (key, value) => {
        globalContext.update(['user', 'preferences', key], value);
      }
    }
  └── deps.prop_[globalContext]
```

## Performance Optimizations

### Selective Subscriptions

**Selective Subscriptions**: See [selective-subscriptions.ts](../code/state/selective-subscriptions.ts)

**Features**:
- Path-based subscriptions
- Granular update notifications
- Subscription batching
- Memory leak prevention

### State Tree Optimization

**Tree Optimization**: See [tree-optimization.ts](../code/state/tree-optimization.ts)

**Features**:
- Structural sharing
- Immutable updates
- Garbage collection
- Memory usage monitoring

## Benefits of Separated State Management

1. **Clean Separation**: State logic completely independent from JSX composition
2. **Kernel Control**: All state changes go through kernel scheduling
3. **Persistence**: Built-in persistence strategies for state trees
4. **Synchronization**: Automatic synchronization with remote state
5. **Type Safety**: Full type safety through categorical type system
6. **Reactivity**: Automatic updates to components when state changes
7. **Performance**: Efficient updates through batching and prioritization

This separation ensures that state management remains clean, predictable, and performant while integrating seamlessly with the SpiceTime kernel and React component system.
