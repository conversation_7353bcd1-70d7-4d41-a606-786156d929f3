# React Component Pragmas

## Overview

React component pragmas provide the building blocks for React component composition through filesystem structure. These pragmas are organized in a dedicated `react/` folder and enable the creation of components, their instantiation, and deep component hierarchies while maintaining complete separation from state management.

## React Pragma Structure

```
_pragma/
└── react/
    ├── component.pragma/
    │   ├── inst.child.pragma
    │   ├── hook.child.pragma
    │   ├── provider.child.pragma
    │   ├── props.child.pragma
    │   └── ref.child.pragma
    ├── compound.pragma/
    └── hoc.pragma/
```

## Core React Component Pragmas

### 1. component.pragma

**Purpose**: Component type definition with numbered child pragma composition

**Type Definition**: See [component.pragma.type.ts](../code/react/component.pragma.type.ts)

**Implementation**: See [component.pragma.ts](../code/react/component.pragma.ts)

**Child Pragmas**:
- `inst.child.pragma`: Component instantiation
- `hook.child.pragma`: React hook definitions
- `provider.child.pragma`: Context providers
- `props.child.pragma`: Props generation
- `ref.child.pragma`: React ref handling

**Key Features**:
- Numbered sequence composition (1.hook, 2.hook, etc.)
- Deep component hierarchy support
- Kernel integration through HOC wrappers
- Runtime component generation

### 2. inst.child.pragma

**Purpose**: Component instantiation with props (replaces jsx.pragma)

**Type Definition**: See [inst.child.pragma.type.ts](../code/react/inst.child.pragma.type.ts)

**Implementation**: See [inst.child.pragma.ts](../code/react/inst.child.pragma.ts)

**Key Features**:
- Component reference resolution
- Props composition from child pragmas
- Key and ref attribute handling
- JSX generation with scope access

### 3. hook.child.pragma

**Purpose**: React hook definitions with kernel integration

**Type Definition**: See [hook.child.pragma.type.ts](../code/react/hook.child.pragma.type.ts)

**Implementation**: See [hook.child.pragma.ts](../code/react/hook.child.pragma.ts)

**Supported Hook Types**:
- `useState`: State management
- `useEffect`: Side effects
- `useContext`: Context consumption
- `useRef`: Reference handling
- `custom`: Custom hook calls

### 4. props.child.pragma

**Purpose**: Props generation from scope terms

**Type Definition**: See [props.child.pragma.type.ts](../code/react/props.child.pragma.type.ts)

**Implementation**: See [props.child.pragma.ts](../code/react/props.child.pragma.ts)

**Key Features**:
- Scope term resolution (scope.user.name)
- JavaScript expression evaluation
- Static value assignment
- Type-safe prop generation

### 5. ref.child.pragma

**Purpose**: React ref handling

**Type Definition**: See [ref.child.pragma.type.ts](../code/react/ref.child.pragma.type.ts)

**Implementation**: See [ref.child.pragma.ts](../code/react/ref.child.pragma.ts)

**Ref Types**:
- `useRef`: Hook-based refs
- `createRef`: Class-based refs
- `forwardRef`: Ref forwarding

## Deep Component Branch Structure

### Component Hierarchy Example

```
header/
├── component.pragma
├── 1.hook.useState
│   ├── name.prop_isMenuOpen
│   ├── initialValue.prop_false
│   └── setter.prop_setIsMenuOpen
├── 2.hook.useEffect
│   ├── effect.expression_{
│       document.addEventListener('click', handleOutsideClick);
│       return () => document.removeEventListener('click', handleOutsideClick);
│     }
│   └── deps.prop_[]
├── 1.inst
│   ├── component -> nav/component.pragma
│   ├── props.child.pragma
│   │   ├── className.prop_{scope.styles.nav}
│   │   ├── isOpen.prop_{isMenuOpen}
│   │   └── onToggle.prop_{setIsMenuOpen}
│   └── key.prop_navigation
├── 2.inst
│   ├── component -> logo/component.pragma
│   ├── props.child.pragma
│   │   ├── src.prop_{scope.assets.logo}
│   │   └── alt.prop_Company Logo
│   └── key.prop_logo
└── props.child.pragma
    ├── title.prop
    ├── onMenuClick.prop
    └── className.prop
```

### Generated Component Structure

**Component Generation**: See [component-generator.ts](../code/react/component-generator.ts)

**Generated Output**:
```javascript
function HeaderComponent(props) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  
  useEffect(() => {
    document.addEventListener('click', handleOutsideClick);
    return () => document.removeEventListener('click', handleOutsideClick);
  }, []);
  
  return (
    <>
      <Nav 
        key="navigation"
        className={scope.styles.nav} 
        isOpen={isMenuOpen} 
        onToggle={setIsMenuOpen} 
      />
      <Logo 
        key="logo"
        src={scope.assets.logo} 
        alt="Company Logo" 
      />
    </>
  );
}
```

## Advanced React Patterns

### 1. compound.pragma

**Purpose**: Compound component patterns with sub-components

**Type Definition**: See [compound.pragma.type.ts](../code/react/compound.pragma.type.ts)

**Implementation**: See [compound.pragma.ts](../code/react/compound.pragma.ts)

**Usage Example**:
```
modal/
├── compound.pragma
├── main.component.pragma
├── header.component.pragma
├── body.component.pragma
└── footer.component.pragma
```

**Generated Usage**:
```jsx
<Modal isOpen={true} onClose={handleClose}>
  <Modal.Header title="Confirm Action" />
  <Modal.Body>Are you sure?</Modal.Body>
  <Modal.Footer onCancel={handleCancel} onConfirm={handleConfirm} />
</Modal>
```

### 2. hoc.pragma

**Purpose**: Higher-order component wrappers

**Type Definition**: See [hoc.pragma.type.ts](../code/react/hoc.pragma.type.ts)

**Implementation**: See [hoc.pragma.ts](../code/react/hoc.pragma.ts)

**Wrapper Types**:
- `memo`: React.memo optimization
- `forwardRef`: Ref forwarding
- `withProps`: Props injection
- `withState`: State injection
- `custom`: Custom HOC patterns

## Kernel Integration

### Component Scheduling

**Kernel Integration**: See [kernel-integration.ts](../code/react/kernel-integration.ts)

**Features**:
- Priority-based component rendering
- Kernel approval for component lifecycle
- Task scheduling and cancellation
- Resource allocation management

### HOC Wrapper System

**HOC System**: See [hoc-system.ts](../code/react/hoc-system.ts)

**Integration Points**:
- `withKernelIntegration`: Kernel approval wrapper
- `withBatchedUpdates`: Batched state updates
- `withPriorityScheduling`: Priority-based rendering

## Performance Optimizations

### Component Caching

**Caching System**: See [component-cache.ts](../code/react/component-cache.ts)

**Features**:
- Generated component memoization
- Cache key generation from definitions
- Invalidation on pragma changes

### Lazy Loading

**Lazy Loading**: See [lazy-loading.ts](../code/react/lazy-loading.ts)

**Features**:
- On-demand component loading
- Pragma dependency resolution
- Progressive component tree building

## Integration with Foundation Pragmas

React component pragmas build upon foundation pragmas:

- **component.pragma** uses obj.pragma for structure
- **props.child.pragma** uses prop.pragma for property definitions
- **inst.child.pragma** uses obj.pragma for element composition
- **hook.child.pragma** integrates with kernel through context.pragma

This ensures that all React functionality is built on the solid mathematical foundation provided by the categorical type system while maintaining the deep component branch structure that enables complex React applications through filesystem organization.
