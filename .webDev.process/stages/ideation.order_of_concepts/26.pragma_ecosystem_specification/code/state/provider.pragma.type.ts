/**
 * Type definition for provider.pragma
 * 
 * React context providers with sequenced composition
 * Part of dedicated state management branch
 */

import React from 'react';

export interface ProviderPragmaType {
  contextName: string;
  value: any; // ttree or context pragma result
  sequence: Array<{
    order: number;
    pragma: 'provider';
    name: string;
  }>;
  
  // React context
  Context: React.Context<any>;
  
  // Generated components
  Provider: React.ComponentType<{ children: React.ReactNode }>;
  useContext: () => any;
  
  // Provider composition
  composeProviders(providers: ProviderPragmaType[]): React.ComponentType<{ children: React.ReactNode }>;
}

export interface ProviderSequenceItem {
  order: number;
  pragma: 'provider';
  name: string;
  contextType: string;
  value?: any;
}

export interface ProviderValueDefinition {
  pragma: 'value';
  source: 'ttree' | 'context';
  reference: string; // Path to ttree or context pragma
  getValue(): any;
}

export interface ProviderComposition {
  providers: ProviderPragmaType[];
  sequence: number[];
  generateComposedProvider(): React.ComponentType<{ children: React.ReactNode }>;
}
