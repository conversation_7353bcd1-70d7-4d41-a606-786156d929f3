/**
 * Kernel integration for state management
 * 
 * Priority-based state updates through SpiceTime kernel
 * Ensures all state changes respect kernel scheduling
 */

export interface KernelStateIntegration {
  scheduleStateUpdate(path: string[], value: any, priority?: 'low' | 'normal' | 'high'): Promise<void>;
  batchStateUpdates(updates: StateUpdate[], priority?: 'low' | 'normal' | 'high'): Promise<void>;
  cancelStateUpdate(taskId: string): void;
}

export interface StateUpdate {
  path: string[];
  value: any;
  timestamp?: number;
  source?: string;
}

export interface KernelTask {
  id: string;
  type: 'state-update' | 'state-batch-update';
  priority: 'low' | 'normal' | 'high';
  payload: StateUpdate | StateUpdate[];
  status: 'pending' | 'approved' | 'executing' | 'completed' | 'cancelled';
  createdAt: number;
  approvedAt?: number;
  completedAt?: number;
}

export class KernelStateManager {
  private kernel: any; // SpiceTime kernel instance
  private pendingTasks = new Map<string, KernelTask>();
  
  constructor(kernel: any) {
    this.kernel = kernel;
  }
  
  /**
   * Schedule a single state update through kernel
   */
  async scheduleStateUpdate(
    path: string[], 
    value: any, 
    priority: 'low' | 'normal' | 'high' = 'normal'
  ): Promise<void> {
    const update: StateUpdate = {
      path,
      value,
      timestamp: Date.now(),
      source: 'pragma-state'
    };
    
    const taskId = await this.kernel.scheduleTask('state-update', priority);
    
    const task: KernelTask = {
      id: taskId,
      type: 'state-update',
      priority,
      payload: update,
      status: 'pending',
      createdAt: Date.now()
    };
    
    this.pendingTasks.set(taskId, task);
    
    try {
      // Wait for kernel approval
      await this.kernel.waitForApproval(taskId);
      
      task.status = 'approved';
      task.approvedAt = Date.now();
      
      // Execute the state update
      await this.executeStateUpdate(update);
      
      task.status = 'completed';
      task.completedAt = Date.now();
      
    } catch (error) {
      task.status = 'cancelled';
      throw error;
    } finally {
      this.pendingTasks.delete(taskId);
    }
  }
  
  /**
   * Schedule multiple state updates as a batch
   */
  async batchStateUpdates(
    updates: StateUpdate[], 
    priority: 'low' | 'normal' | 'high' = 'high'
  ): Promise<void> {
    const taskId = await this.kernel.scheduleTask('state-batch-update', priority);
    
    const task: KernelTask = {
      id: taskId,
      type: 'state-batch-update',
      priority,
      payload: updates,
      status: 'pending',
      createdAt: Date.now()
    };
    
    this.pendingTasks.set(taskId, task);
    
    try {
      // Wait for kernel approval
      await this.kernel.waitForApproval(taskId);
      
      task.status = 'approved';
      task.approvedAt = Date.now();
      
      // Execute all updates in batch
      await this.executeBatchUpdates(updates);
      
      task.status = 'completed';
      task.completedAt = Date.now();
      
    } catch (error) {
      task.status = 'cancelled';
      throw error;
    } finally {
      this.pendingTasks.delete(taskId);
    }
  }
  
  /**
   * Cancel a pending state update
   */
  cancelStateUpdate(taskId: string): void {
    const task = this.pendingTasks.get(taskId);
    if (task && task.status === 'pending') {
      this.kernel.cancelTask(taskId);
      task.status = 'cancelled';
      this.pendingTasks.delete(taskId);
    }
  }
  
  /**
   * Execute a single state update
   */
  private async executeStateUpdate(update: StateUpdate): Promise<void> {
    // Get the state tree from scope
    const stateTree = this.getStateTree();
    
    // Apply the update
    stateTree.update(update.path, update.value);
    
    // Notify subscribers
    this.notifyStateChange(update);
  }
  
  /**
   * Execute multiple state updates in batch
   */
  private async executeBatchUpdates(updates: StateUpdate[]): Promise<void> {
    const stateTree = this.getStateTree();
    
    // Start transaction for atomic updates
    stateTree.startTransaction();
    
    try {
      // Apply all updates
      updates.forEach(update => {
        stateTree.update(update.path, update.value);
      });
      
      // Commit transaction
      stateTree.commitTransaction();
      
      // Notify subscribers of batch change
      this.notifyBatchChange(updates);
      
    } catch (error) {
      // Rollback on error
      stateTree.rollbackTransaction();
      throw error;
    }
  }
  
  /**
   * Get the current state tree from scope
   */
  private getStateTree(): any {
    // Access the reactive state tree from scope.context
    return scope.context.stateTree;
  }
  
  /**
   * Notify subscribers of state change
   */
  private notifyStateChange(update: StateUpdate): void {
    // Emit change event through reactive system
    const stateTree = this.getStateTree();
    stateTree.emit('change', {
      type: 'single-update',
      update,
      timestamp: Date.now()
    });
  }
  
  /**
   * Notify subscribers of batch change
   */
  private notifyBatchChange(updates: StateUpdate[]): void {
    const stateTree = this.getStateTree();
    stateTree.emit('change', {
      type: 'batch-update',
      updates,
      timestamp: Date.now()
    });
  }
  
  /**
   * Get pending tasks for debugging
   */
  getPendingTasks(): KernelTask[] {
    return Array.from(this.pendingTasks.values());
  }
  
  /**
   * Get task statistics
   */
  getTaskStatistics(): {
    pending: number;
    approved: number;
    executing: number;
    completed: number;
    cancelled: number;
  } {
    const tasks = Array.from(this.pendingTasks.values());
    
    return {
      pending: tasks.filter(t => t.status === 'pending').length,
      approved: tasks.filter(t => t.status === 'approved').length,
      executing: tasks.filter(t => t.status === 'executing').length,
      completed: tasks.filter(t => t.status === 'completed').length,
      cancelled: tasks.filter(t => t.status === 'cancelled').length
    };
  }
}

// Export singleton instance
export const kernelStateManager = new KernelStateManager(kernel);
