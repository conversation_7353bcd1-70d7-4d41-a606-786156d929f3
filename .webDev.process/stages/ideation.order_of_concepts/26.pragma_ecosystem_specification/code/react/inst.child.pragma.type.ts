/**
 * Type definition for inst.child.pragma
 * 
 * Component instantiation with props (replaces jsx.pragma)
 * Child pragma of component.pragma for deep component hierarchies
 */

export interface InstChildPragmaType {
  type: 'instance';
  sequence: number;
  component: string; // Reference to component pragma
  props: PropsChildPragmaType;
  key?: string;
  ref?: string;
  depth?: number;
  
  // Generation methods
  generateJSX(scope: any): string;
  resolveComponent(): ComponentReference;
}

export interface ComponentReference {
  path: string;
  name: string;
  pragma: any; // Reference to actual component pragma
}

export interface PropsChildPragmaType {
  pragma: 'props';
  properties: Array<{
    name: string;
    value?: any;
    scopeReference?: string; // e.g., "scope.user.name"
    expression?: string;     // e.g., "{isOpen ? 'open' : 'closed'}"
    type?: string;
  }>;
  
  // Generation methods
  generatePropsCode(scope: any): string;
  resolveProps(scope: any): Record<string, any>;
}

export interface InstChildDefinition {
  sequence: number;
  component: ComponentReference;
  props: PropsChildPragmaType;
  key?: string;
  ref?: string;
  depth?: number;
  children?: InstChildDefinition[];
}

export interface JSXGenerationContext {
  scope: any;
  depth: number;
  parentComponent?: string;
  availableRefs?: Record<string, any>;
}
