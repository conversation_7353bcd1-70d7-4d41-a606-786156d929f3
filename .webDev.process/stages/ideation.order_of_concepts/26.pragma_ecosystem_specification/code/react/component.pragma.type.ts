/**
 * Type definition for component.pragma
 * 
 * React component type definition with numbered child pragma composition
 * Deep component branch structure for complex React applications
 */

export interface ComponentPragmaType {
  hooks: Array<{
    sequence: number;
    pragma: 'hook';
    hookType: 'useState' | 'useEffect' | 'useContext' | 'useRef' | 'custom';
    name: string;
    parameters?: any[];
  }>;
  
  providers: Array<{
    sequence: number;
    pragma: 'provider';
    name: string;
    contextType: string;
  }>;
  
  instances: Array<{
    sequence: number;
    pragma: 'inst';
    component: string; // Reference to component
    props?: any;
    key?: string;
    ref?: string;
  }>;
  
  props: {
    pragma: 'props';
    properties: Array<{
      name: string;
      type: string;
      required: boolean;
      default?: any;
    }>;
  };
  
  refs: Array<{
    pragma: 'ref';
    name: string;
    refType: 'useRef' | 'createRef' | 'forwardRef';
    initialValue?: any;
  }>;
  
  // Component metadata
  displayName?: string;
  memo?: boolean;
  forwardRef?: boolean;
  
  // Generated component function
  generateComponent(): React.ComponentType;
  generateScript(): string;
}

export interface HookChild {
  sequence: number;
  pragma: 'hook';
  hookType: 'useState' | 'useEffect' | 'useContext' | 'useRef' | 'custom';
  name: string;
  parameters?: any[];
  dependencies?: string[];
}

export interface ProviderChild {
  sequence: number;
  pragma: 'provider';
  name: string;
  contextType: string;
  value?: any;
}

export interface InstChild {
  sequence: number;
  pragma: 'inst';
  component: string;
  props?: any;
  key?: string;
  ref?: string;
  depth?: number;
}

export interface PropsChild {
  pragma: 'props';
  properties: Array<{
    name: string;
    type: string;
    required: boolean;
    default?: any;
    scopeReference?: string;
    expression?: string;
  }>;
}

export interface RefChild {
  pragma: 'ref';
  name: string;
  refType: 'useRef' | 'createRef' | 'forwardRef';
  initialValue?: any;
}
