/**
 * Implementation of inst.child.pragma
 * 
 * Component instantiation with props (replaces jsx.pragma)
 * Generates JSX for component instances with scope-resolved props
 */

import { InstChildPragmaType, ComponentReference, PropsChildPragmaType, JSXGenerationContext } from './inst.child.pragma.type';

export default function instChildPragma(definition: any): InstChildPragmaType {
  let componentRef: ComponentReference | null = null;
  let props: PropsChildPragmaType = { pragma: 'props', properties: [] };
  let key: string | undefined;
  let ref: string | undefined;
  let depth = 0;

  // Process child definitions
  if (definition.component) {
    componentRef = resolveComponentReference(definition.component);
  }
  
  if (definition.props) {
    props = createPropsChild(definition.props);
  }
  
  if (definition.key) {
    key = definition.key;
  }
  
  if (definition.ref) {
    ref = definition.ref;
  }
  
  if (definition.depth !== undefined) {
    depth = definition.depth;
  }

  return {
    type: 'instance',
    sequence: definition.sequence || 0,
    component: componentRef?.path || '',
    props,
    key,
    ref,
    depth,
    
    generateJSX: (scope: any): string => {
      if (!componentRef) {
        throw new Error('Component reference not resolved');
      }
      
      const componentName = componentRef.name;
      const propsCode = props.generatePropsCode(scope);
      const keyAttr = key ? ` key={${JSON.stringify(key)}}` : '';
      const refAttr = ref ? ` ref={${ref}}` : '';
      
      // Generate self-closing or container JSX based on children
      if (hasChildren(definition)) {
        return `<${componentName}${keyAttr}${refAttr}${propsCode ? ` ${propsCode}` : ''}>
${generateChildrenJSX(definition.children, scope, depth + 1)}
</${componentName}>`;
      } else {
        return `<${componentName}${keyAttr}${refAttr}${propsCode ? ` ${propsCode}` : ''} />`;
      }
    },
    
    resolveComponent: (): ComponentReference => {
      if (!componentRef) {
        throw new Error('Component reference not resolved');
      }
      return componentRef;
    }
  };
}

/**
 * Resolve component reference from filesystem path
 */
function resolveComponentReference(componentPath: string): ComponentReference {
  // Parse component path: "nav/component.pragma" -> { path: "nav", name: "Nav" }
  const parts = componentPath.split('/');
  const componentDir = parts[0];
  const componentName = capitalizeFirst(componentDir);
  
  return {
    path: componentPath,
    name: componentName,
    pragma: null // Will be resolved at runtime
  };
}

/**
 * Create props child pragma from definition
 */
function createPropsChild(propsDefinition: any): PropsChildPragmaType {
  const properties = Object.entries(propsDefinition).map(([name, config]: [string, any]) => ({
    name,
    value: config.value,
    scopeReference: config.scopeReference,
    expression: config.expression,
    type: config.type
  }));

  return {
    pragma: 'props',
    properties,
    
    generatePropsCode: (scope: any): string => {
      const propStrings = properties.map(prop => {
        let value: string;
        
        if (prop.scopeReference) {
          // Resolve from scope: scope.user.name
          value = `{${prop.scopeReference}}`;
        } else if (prop.expression) {
          // JavaScript expression
          value = `{${prop.expression}}`;
        } else if (typeof prop.value === 'string') {
          // String literal
          value = `"${prop.value}"`;
        } else if (typeof prop.value === 'boolean') {
          // Boolean value
          value = prop.value ? `{true}` : `{false}`;
        } else if (prop.value !== undefined) {
          // Other values (numbers, objects, etc.)
          value = `{${JSON.stringify(prop.value)}}`;
        } else {
          // No value provided
          return null;
        }
        
        return `${prop.name}=${value}`;
      }).filter(Boolean);
      
      return propStrings.join(' ');
    },
    
    resolveProps: (scope: any): Record<string, any> => {
      const resolvedProps: Record<string, any> = {};
      
      properties.forEach(prop => {
        if (prop.scopeReference) {
          resolvedProps[prop.name] = resolveScopeReference(scope, prop.scopeReference);
        } else if (prop.expression) {
          // Evaluate expression in scope context
          resolvedProps[prop.name] = evaluateExpression(prop.expression, scope);
        } else {
          resolvedProps[prop.name] = prop.value;
        }
      });
      
      return resolvedProps;
    }
  };
}

/**
 * Check if instance has children
 */
function hasChildren(definition: any): boolean {
  return definition.children && definition.children.length > 0;
}

/**
 * Generate JSX for children instances
 */
function generateChildrenJSX(children: any[], scope: any, depth: number): string {
  if (!children || children.length === 0) {
    return '';
  }
  
  const indent = '  '.repeat(depth);
  
  return children.map(child => {
    const childInstance = instChildPragma({ ...child, depth });
    const childJSX = childInstance.generateJSX(scope);
    return `${indent}${childJSX}`;
  }).join('\n');
}

/**
 * Resolve scope reference using dot notation
 */
function resolveScopeReference(scope: any, reference: string): any {
  return reference.split('.').reduce((obj, key) => obj?.[key], scope);
}

/**
 * Evaluate JavaScript expression in scope context
 */
function evaluateExpression(expression: string, scope: any): any {
  try {
    // Create a function that has access to scope
    const func = new Function('scope', `return ${expression}`);
    return func(scope);
  } catch (error) {
    console.warn(`Failed to evaluate expression: ${expression}`, error);
    return undefined;
  }
}

/**
 * Capitalize first letter of string
 */
function capitalizeFirst(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1);
}
