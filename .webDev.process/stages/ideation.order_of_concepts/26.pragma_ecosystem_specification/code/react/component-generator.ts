/**
 * Component generation system for React components
 * 
 * Generates React components from component.pragma definitions
 * Uses Function constructor for runtime component creation
 */

import { ComponentPragmaType, HookChild, InstChild } from './component.pragma.type';

export class ComponentGenerator {
  
  /**
   * Generate React component from pragma definition
   */
  generateComponent(definition: ComponentPragmaType): React.ComponentType {
    const script = this.generateComponentScript(definition);
    
    // Use Function constructor for dynamic component creation
    const ComponentFunction = Function(
      'React',
      'useState', 
      'useEffect', 
      'useContext', 
      'useRef',
      'scope',
      `
        const { useState, useEffect, useContext, useRef } = React;
        ${script}
        return Component;
      `
    );
    
    // Execute with React dependencies
    const GeneratedComponent = ComponentFunction(
      React,
      React.useState,
      React.useEffect,
      React.useContext,
      React.useRef,
      scope
    );
    
    // Set display name for debugging
    if (definition.displayName) {
      GeneratedComponent.displayName = definition.displayName;
    }
    
    return GeneratedComponent;
  }
  
  /**
   * Generate component script string
   */
  generateComponentScript(definition: ComponentPragmaType): string {
    const hooks = this.generateHooksCode(definition.hooks);
    const refs = this.generateRefsCode(definition.refs);
    const instances = this.generateInstancesCode(definition.instances);
    
    return `
function Component(props) {
  ${refs}
  ${hooks}
  
  return (
    <>
      ${instances}
    </>
  );
}`;
  }
  
  /**
   * Generate hooks code from hook definitions
   */
  private generateHooksCode(hooks: HookChild[]): string {
    // Sort hooks by sequence
    const sortedHooks = [...hooks].sort((a, b) => a.sequence - b.sequence);
    
    return sortedHooks.map(hook => {
      switch (hook.hookType) {
        case 'useState':
          return this.generateUseStateHook(hook);
        case 'useEffect':
          return this.generateUseEffectHook(hook);
        case 'useContext':
          return this.generateUseContextHook(hook);
        case 'useRef':
          return this.generateUseRefHook(hook);
        case 'custom':
          return this.generateCustomHook(hook);
        default:
          return `// Unknown hook type: ${hook.hookType}`;
      }
    }).join('\n  ');
  }
  
  /**
   * Generate useState hook
   */
  private generateUseStateHook(hook: HookChild): string {
    const [initialValue] = hook.parameters || [undefined];
    const setterName = `set${this.capitalizeFirst(hook.name)}`;
    
    return `const [${hook.name}, ${setterName}] = useState(${this.formatValue(initialValue)});`;
  }
  
  /**
   * Generate useEffect hook
   */
  private generateUseEffectHook(hook: HookChild): string {
    const [effect, deps] = hook.parameters || ['() => {}', '[]'];
    const depsCode = Array.isArray(deps) ? `[${deps.join(', ')}]` : deps;
    
    return `useEffect(${effect}, ${depsCode});`;
  }
  
  /**
   * Generate useContext hook
   */
  private generateUseContextHook(hook: HookChild): string {
    const [contextName] = hook.parameters || ['Context'];
    return `const ${hook.name} = useContext(${contextName});`;
  }
  
  /**
   * Generate useRef hook
   */
  private generateUseRefHook(hook: HookChild): string {
    const [initialValue] = hook.parameters || [null];
    return `const ${hook.name} = useRef(${this.formatValue(initialValue)});`;
  }
  
  /**
   * Generate custom hook
   */
  private generateCustomHook(hook: HookChild): string {
    const [hookCall] = hook.parameters || [`use${this.capitalizeFirst(hook.name)}()`];
    return `const ${hook.name} = ${hookCall};`;
  }
  
  /**
   * Generate refs code from ref definitions
   */
  private generateRefsCode(refs: any[]): string {
    return refs.map(ref => {
      switch (ref.refType) {
        case 'useRef':
          const initial = ref.initialValue || 'null';
          return `const ${ref.name} = useRef(${this.formatValue(initial)});`;
        case 'createRef':
          return `const ${ref.name} = createRef();`;
        case 'forwardRef':
          return `// Forward ref handled in component wrapper`;
        default:
          return `// Unknown ref type: ${ref.refType}`;
      }
    }).join('\n  ');
  }
  
  /**
   * Generate instances code (JSX)
   */
  private generateInstancesCode(instances: InstChild[]): string {
    // Sort instances by sequence
    const sortedInstances = [...instances].sort((a, b) => a.sequence - b.sequence);
    
    return sortedInstances.map(instance => {
      const componentName = this.resolveComponentName(instance.component);
      const propsCode = this.generatePropsCode(instance.props);
      const keyAttr = instance.key ? ` key={${JSON.stringify(instance.key)}}` : '';
      const refAttr = instance.ref ? ` ref={${instance.ref}}` : '';
      
      return `<${componentName}${keyAttr}${refAttr}${propsCode ? ` ${propsCode}` : ''} />`;
    }).join('\n      ');
  }
  
  /**
   * Generate props code for component instance
   */
  private generatePropsCode(props: any): string {
    if (!props || !props.properties) {
      return '';
    }
    
    const propStrings = props.properties.map((prop: any) => {
      let value: string;
      
      if (prop.scopeReference) {
        value = `{${prop.scopeReference}}`;
      } else if (prop.expression) {
        value = `{${prop.expression}}`;
      } else if (typeof prop.value === 'string') {
        value = `"${prop.value}"`;
      } else {
        value = `{${JSON.stringify(prop.value)}}`;
      }
      
      return `${prop.name}=${value}`;
    });
    
    return propStrings.join(' ');
  }
  
  /**
   * Resolve component name from reference
   */
  private resolveComponentName(componentRef: string): string {
    // Extract component name from path: "nav/component.pragma" -> "Nav"
    const parts = componentRef.split('/');
    const componentDir = parts[0];
    return this.capitalizeFirst(componentDir);
  }
  
  /**
   * Format value for code generation
   */
  private formatValue(value: any): string {
    if (value === null || value === undefined) {
      return 'null';
    }
    if (typeof value === 'string') {
      return `"${value}"`;
    }
    if (typeof value === 'boolean' || typeof value === 'number') {
      return String(value);
    }
    return JSON.stringify(value);
  }
  
  /**
   * Capitalize first letter
   */
  private capitalizeFirst(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }
}

// Export singleton instance
export const componentGenerator = new ComponentGenerator();
