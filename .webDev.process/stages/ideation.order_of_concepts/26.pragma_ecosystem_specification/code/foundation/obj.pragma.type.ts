/**
 * Type definition for obj.pragma
 * 
 * Object composition with properties, methods, getters, and setters
 * Built on categorical foundations from cat.types
 */

import { CatObject, Morphism } from 'cat.types';

export interface ObjPragmaType extends CatObject {
  children: Array<
    | { pragma: 'property', name: string, value: any }
    | { pragma: 'method', name: string, implementation: Function }
    | { pragma: 'getter', name: string, implementation: Function }
    | { pragma: 'setter', name: string, implementation: Function }
  >;
  
  // Categorical properties
  identity: Morphism;
  composition: (other: ObjPragmaType) => ObjPragmaType;
}

export interface PropertyChild {
  pragma: 'property';
  name: string;
  value: any;
  scopeReference?: string;
}

export interface MethodChild {
  pragma: 'method';
  name: string;
  implementation: Function;
  parameters?: string[];
  returnType?: string;
}

export interface GetterChild {
  pragma: 'getter';
  name: string;
  implementation: Function;
  returnType?: string;
}

export interface SetterChild {
  pragma: 'setter';
  name: string;
  implementation: Function;
  parameterType?: string;
}

export type ObjChild = PropertyChild | MethodChild | GetterChild | SetterChild;
