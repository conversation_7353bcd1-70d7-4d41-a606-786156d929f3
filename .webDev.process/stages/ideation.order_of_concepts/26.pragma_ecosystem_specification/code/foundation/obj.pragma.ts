/**
 * Implementation of obj.pragma
 * 
 * Object composition with properties, methods, getters, and setters
 * Uses categorical composition from cat.types
 */

import { ObjPragmaType, ObjChild } from './obj.pragma.type';
import { CatObject, Morphism } from 'cat.types';

export default function objPragma(children: ObjChild[]): ObjPragmaType {
  const obj: any = {};
  const descriptors: PropertyDescriptorMap = {};

  children.forEach(child => {
    switch (child.pragma) {
      case 'property':
        obj[child.name] = child.value;
        break;
        
      case 'method':
        obj[child.name] = child.implementation;
        break;
        
      case 'getter':
        descriptors[child.name] = {
          ...descriptors[child.name],
          get: child.implementation,
          enumerable: true,
          configurable: true
        };
        break;
        
      case 'setter':
        descriptors[child.name] = {
          ...descriptors[child.name],
          set: child.implementation,
          enumerable: true,
          configurable: true
        };
        break;
    }
  });

  // Apply property descriptors
  Object.defineProperties(obj, descriptors);

  // Add categorical structure
  const catObj: ObjPragmaType = {
    ...obj,
    children,
    
    // Identity morphism
    identity: new Morphism(obj, obj, (x) => x),
    
    // Categorical composition
    composition: (other: ObjPragmaType): ObjPragmaType => {
      return objPragma([...children, ...other.children]);
    }
  };

  return catObj;
}

/**
 * Resolve scope reference for property values
 */
export function resolveScopeReference(scope: any, reference: string): any {
  return reference.split('.').reduce((obj, key) => obj?.[key], scope);
}

/**
 * Create property descriptor from child definition
 */
export function createPropertyDescriptor(child: ObjChild): PropertyDescriptor {
  switch (child.pragma) {
    case 'getter':
      return {
        get: child.implementation,
        enumerable: true,
        configurable: true
      };
      
    case 'setter':
      return {
        set: child.implementation,
        enumerable: true,
        configurable: true
      };
      
    default:
      return {
        value: child.pragma === 'property' ? child.value : child.implementation,
        writable: true,
        enumerable: true,
        configurable: true
      };
  }
}
