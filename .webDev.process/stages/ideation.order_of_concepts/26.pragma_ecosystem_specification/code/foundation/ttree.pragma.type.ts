/**
 * Type definition for ttree.pragma
 * 
 * TreenityTree reactive structures that don't affect scopes
 * Built on forestry.types TreeFunctor
 */

import { TreeFunctor } from 'forestry.types';

export interface TtreePragmaType extends TreeFunctor {
  children: Array<{
    pragma: 'obj' | 'context' | 'prop';
    name: string;
    [key: string]: any;
  }>;
  
  // Reactive properties
  reactive: true;
  scopeIndependent: true;
  
  // Tree operations
  getSnapshot(): any;
  subscribe(listener: (changes: any) => void): () => void;
  update(path: string[], value: any): void;
  loadSnapshot(snapshot: any): void;
  
  // Forestry.types integration
  createNode(type: string, data: any): TreeNode;
  addEdge(fromId: string, toId: string, type: string): void;
  removeNode(nodeId: string): void;
  removeEdge(edgeId: string): void;
}

export interface TreeNode {
  id: string;
  type: string;
  data: any;
  children: TreeNode[];
  parent?: TreeNode;
}

export interface TreeEdge {
  id: string;
  from: string;
  to: string;
  type: string;
  metadata?: any;
}

export interface ReactiveSubscription {
  id: string;
  path?: string[];
  listener: (changes: any) => void;
  active: boolean;
}
