# Vocabulary-Pragma Mapping System

## Core Concept

A systematic approach to mapping linguistic vocabulary onto pragma functionality, enabling natural language composition of pragmatic operations while maintaining clear separation between linguistic expression and implementation logic.

## Mapping Architecture

### Three-Layer System
```
Natural Language Layer
├── Human-readable expressions
├── Domain-specific vocabulary
└── Grammatical composition rules

Linguistic Mapping Layer  
├── Vocabulary → Pragma mappings
├── Grammar → Operation mappings
├── Parameter → Type mappings
└── Composition → Flow mappings

Pragma Implementation Layer
├── Actual functionality
├── Child pragma management
├── State and execution
└── Data transformation
```

## Mapping Types

### Direct Vocabulary Mapping
```
Vocabulary Term → Pragma
├── "chain" → chain.pragma
├── "filter" → filter.pragma  
├── "map" → map.pragma
├── "reduce" → reduce.pragma
└── "sort" → sort.pragma
```

### Compositional Mapping
```
Linguistic Pattern → Pragma Operation
├── "chain_of_(type)" → chain.pragma.compose(type)
├── "filter_by_(condition)" → filter.pragma.apply(condition)
├── "map_to_(transform)" → map.pragma.transform(transform)
├── "reduce_with_(aggregator)" → reduce.pragma.aggregate(aggregator)
└── "sort_by_(comparator)" → sort.pragma.order(comparator)
```

### Parametric Mapping
```
Parameter Pattern → Type System
├── (obj_of_types) → t.obj_of_types
├── (list_of_items) → t.list_of_items
├── (dict_of_values) → t.dict_of_values
└── (sequence_of_elements) → t.sequence_of_elements
```

## Implementation Strategy

### Pragma-Side Registration
```typescript
interface PragmaLinguisticInterface {
  vocabulary: string[];           // Words that map to this pragma
  patterns: string[];            // Linguistic patterns supported
  parameters: ParameterMapping[]; // Parameter type mappings
  grammar: GrammarRule[];        // Grammar rules for composition
}

class ChainPragma extends STPragma {
  static linguisticInterface: PragmaLinguisticInterface = {
    vocabulary: ['chain', 'sequence', 'pipeline', 'flow'],
    patterns: [
      'chain_of_(type)',
      'chain_with_(config)', 
      'chain_through_(steps)'
    ],
    parameters: [
      { pattern: '(obj_of_types)', type: 't.obj_of_types' },
      { pattern: '(list_of_items)', type: 't.list_of_items' }
    ],
    grammar: [
      { rule: 'chain_of_X', maps_to: 'compose', requires: ['type'] },
      { rule: 'X_chain_Y', maps_to: 'connect', requires: ['source', 'target'] }
    ]
  };
}
```

### Linguistic-Side Resolution
```typescript
class VocabularyResolver {
  // Map vocabulary to pragmas
  resolvePragma(vocabulary: string): Pragma | null {
    return this.vocabularyMap.get(vocabulary);
  }
  
  // Parse linguistic patterns
  parsePattern(expression: string): ParsedPattern {
    const pattern = this.identifyPattern(expression);
    const parameters = this.extractParameters(expression);
    const pragma = this.resolvePragma(pattern.vocabulary);
    
    return {
      pragma,
      operation: pattern.operation,
      parameters: this.mapParameters(parameters)
    };
  }
  
  // Execute pragma through linguistic interface
  async execute(parsed: ParsedPattern): Promise<any> {
    const { pragma, operation, parameters } = parsed;
    return await pragma[operation](...parameters);
  }
}
```

## Grammar Integration

### Linguistic Grammar Rules
```typescript
interface GrammarRule {
  pattern: string;              // Linguistic pattern
  maps_to: string;             // Pragma operation
  requires: string[];          // Required parameters
  optional?: string[];         // Optional parameters
  precedence?: number;         // Parsing precedence
  associativity?: 'left' | 'right' | 'none';
}

const chainGrammar: GrammarRule[] = [
  {
    pattern: 'chain_of_(type)',
    maps_to: 'compose',
    requires: ['type'],
    precedence: 10
  },
  {
    pattern: 'X_then_Y',
    maps_to: 'sequence',
    requires: ['first', 'second'],
    precedence: 5,
    associativity: 'left'
  }
];
```

### Composition Rules
```typescript
interface CompositionRule {
  left_pattern: string;
  operator: string;
  right_pattern: string;
  result_type: string;
  composition_method: string;
}

const compositionRules: CompositionRule[] = [
  {
    left_pattern: 'chain_of_(type)',
    operator: 'then',
    right_pattern: 'filter_by_(condition)',
    result_type: 'filtered_chain',
    composition_method: 'pipeline'
  }
];
```

## Domain-Specific Vocabularies

### Data Processing Domain
```typescript
const dataProcessingVocabulary = {
  'transform': 'map.pragma',
  'filter': 'filter.pragma',
  'aggregate': 'reduce.pragma',
  'group': 'groupBy.pragma',
  'join': 'join.pragma',
  'sort': 'sort.pragma'
};
```

### UI Component Domain
```typescript
const uiComponentVocabulary = {
  'render': 'component.pragma',
  'style': 'css.pragma',
  'animate': 'animation.pragma',
  'layout': 'layout.pragma',
  'interact': 'event.pragma'
};
```

### Business Logic Domain
```typescript
const businessLogicVocabulary = {
  'validate': 'validation.pragma',
  'authorize': 'auth.pragma',
  'calculate': 'calculation.pragma',
  'notify': 'notification.pragma',
  'audit': 'audit.pragma'
};
```

## Scope Integration

### L Namespace Structure
```
linguistics.l/
├── vocabulary/
│   ├── core/           # Core vocabulary mappings
│   ├── data/           # Data processing vocabulary
│   ├── ui/             # UI component vocabulary
│   └── business/       # Business logic vocabulary
├── grammar/
│   ├── composition/    # Composition rules
│   ├── precedence/     # Precedence rules
│   └── associativity/  # Associativity rules
└── mappings/
    ├── pragma_map/     # Vocabulary → Pragma mappings
    ├── operation_map/  # Pattern → Operation mappings
    └── type_map/       # Parameter → Type mappings
```

### Pragma Lineage Integration
```typescript
// Each pragma inherits and extends linguistic capabilities
class SpecPragma extends STPragma {
  // Inherit base linguistic interface
  static linguisticInterface = {
    ...super.linguisticInterface,
    // Extend with spec-specific vocabulary
    vocabulary: [...super.vocabulary, 'parse', 'generate', 'sync'],
    patterns: [...super.patterns, 'parse_spec_(format)', 'generate_code_(target)']
  };
}
```

## Benefits

### Natural Programming Interface
- **Human-readable code** through vocabulary mapping
- **Domain-specific languages** through custom vocabularies
- **Intuitive composition** through grammatical rules

### Maintainable Architecture
- **Clear separation** between linguistic and implementation concerns
- **Extensible vocabulary** without changing pragma implementations
- **Consistent patterns** across different domains

### AI Integration Ready
- **Natural language processing** can map to pragma operations
- **Code generation** from natural language descriptions
- **Intelligent suggestions** based on vocabulary patterns

## Implementation Phases

### Phase 1: Core Vocabulary
- Implement basic vocabulary mappings for core pragmas
- Establish grammar rules for simple compositions
- Create resolution and execution infrastructure

### Phase 2: Domain Extensions
- Add domain-specific vocabularies
- Implement complex composition rules
- Integrate with existing pragma ecosystem

### Phase 3: AI Integration
- Natural language to pragma translation
- Intelligent vocabulary expansion
- Automated pattern recognition and suggestion

This mapping system enables the vision of **natural language programming** while maintaining the **precision and performance** of traditional pragma-based systems.
