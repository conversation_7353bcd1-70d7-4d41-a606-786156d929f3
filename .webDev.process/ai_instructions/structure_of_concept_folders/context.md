# Structure of Concept Folders - AI Instructions

This document provides guidelines for AI assistants working with the concept folder structure in this repository.

## Reference to Concept

For detailed information about the structure of concept folders, please refer to:

[2.structure_of_concept_folders](../../stages/ideation/2.structure_of_concept_folders/summary.md)

## Key Guidelines for AI Assistants

1. **Respect the Numerical Prefixes**:
   - All files and folders should use numerical prefixes (e.g., `0.`, `1.`, etc.)
   - When creating new files, continue the existing sequence

2. **Maintain Standard Structure**:
   - Each concept should have `context.md`, `summary.md`, and `code/` and `docs/` folders
   - Documentation should reference code snippets appropriately

3. **Cross-Referencing**:
   - Use relative paths for links between documents
   - Reference code snippets from documentation using relative paths

4. **Temporal Awareness**:
   - Be aware of the temporal structure of the repository
   - References to archived concepts should use tic indices or absolute paths

5. **Documentation Standards**:
   - `context.md` provides high-level overview
   - `summary.md` summarizes key points and links to supporting docs
   - Detailed documentation goes in the `docs/` folder

When assisting with this repository, please follow these guidelines to maintain consistency with the established structure.
