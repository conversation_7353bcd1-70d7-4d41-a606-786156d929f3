import { QuantumLoadBalancer } from './quantum-load-balancer';
import { ProcessSubject } from '../../utils/core/catTypes/process/subject';

// Create a quantum load balancer
const loadBalancer = new QuantumLoadBalancer();

// Create processes with quantum properties
const apiGateway = new ProcessSubject({ id: 'api-gateway', name: 'API Gateway' });
loadBalancer.registerProcess(apiGateway, {
    type: 'boson',
    mass: 1.0,
    charge: 0.5,
    spin: 1,
    color: 'red'
});

const authService = new ProcessSubject({ id: 'auth-service', name: 'Auth Service' });
loadBalancer.registerProcess(authService, {
    type: 'quark',
    mass: 0.7,
    charge: -0.3,
    spin: 0.5,
    flavor: 'up'
});

const userService = new ProcessSubject({ id: 'user-service', name: 'User Service' });
loadBalancer.registerProcess(userService, {
    type: 'quark',
    mass: 0.8,
    charge: 0.3,
    spin: 0.5,
    flavor: 'down'
});

const dataService = new ProcessSubject({ id: 'data-service', name: 'Data Service' });
loadBalancer.registerProcess(dataService, {
    type: 'hadron',
    mass: 1.2,
    charge: 0,
    spin: 0,
    isospin: 0.5
});

const analyticsService = new ProcessSubject({ id: 'analytics', name: 'Analytics Service' });
loadBalancer.registerProcess(analyticsService, {
    type: 'lepton',
    mass: 0.5,
    charge: -1,
    spin: 0.5
});

// Create tree structures (microservice trees)
loadBalancer.createTree('api-tree', 'api-gateway');
loadBalancer.addToTree('api-tree', 'auth-service', 'api-gateway');
loadBalancer.addToTree('api-tree', 'user-service', 'api-gateway');

loadBalancer.createTree('data-tree', 'data-service');
loadBalancer.addToTree('data-tree', 'analytics', 'data-service');

// Connect trees with meta-links (inter-service communication)
loadBalancer.connectTrees('api-tree', 'data-tree', 0.4);

// Apply CSS-like styling to modify interaction strengths
loadBalancer.applyStyle('#auth-service', {
    amplitude: 0.9