# SpaceTime Development Structure: A Bird's Eye View

## PLT Space: The Foundation

The Programming Language Theory (PLT) space forms our foundation at coordinates (0,0,0). This origin point establishes three orthogonal dimensions:
- **Pragma (P)**: Code organization and structural patterns
- **Linguistics (L)**: Language features and expression capabilities
- **Types (T)**: Type system capabilities and categorical structures

This creates a flat, Euclidean 3D space where each point represents a specific capability set. Movement along any axis represents incremental, non-breaking evolution.

## Branching Into New Spaces

From any point in the PLT space, we can branch into entirely new 3D spaces with their own orthogonal dimensions:

1. **IDP Space** (Ideation, Design, Production)
    - Branched from PLT origin
    - Represents development lifecycle stages
    - Each point defines process capabilities

2. **Server-Process-Agent Space**
    - Branched from PLT origin
    - Defines server architecture capabilities
    - Evolves independently of other spaces

3. **React Variant Space**
    - Branched from PLT origin
    - Represents component model variations
    - Evolves UI capabilities independently

Each new space maintains its connection to its origin point in the parent space, creating a tree of 3D spaces. This connection is recorded in `package.json` dependencies, making all lineages traceable.

## Evolution Within Spaces

Within each space, packages evolve along timelike sequences:
- Non-breaking changes: Movement along an axis
- Breaking changes: Branch to new 3D space with origin at breaking point

This creates two types of transformations:
1. **Translations**: Frame of reference shifts (breaking changes)
2. **Rotations**: New package types forming 3D spaces from parent points

## Git Integration Challenges

Our model faces challenges with traditional Git:
1. Git lacks native support for 3D space triplets
2. Monorepo vs multi-repo tensions (we choose monorepo to preserve meaning)
3. Need for specialized navigation across the space tree

We need Git extensions to support:
- Visualization of the space tree
- Navigation between spaces
- Tracking relationships between spaces

## Developer Workflow Optimization

The ideal developer workflow:
1. **Focused Development Context**
    - Developer receives only relevant package and dependencies
    - Lineage to root preserved for context
    - Everything else pruned from filesystem

2. **Dependency Management**
    - Local packages published and imported as dependencies
    - Clear separation between development and consumption
    - Package.json tracks all space relationships

3. **Content Management for AI**
    - Only relevant content in filesystem
    - AI assistant sees focused context
    - Reduced noise improves assistance quality

## St.Pragma: Compressing Timelines

The st.pragma system's primary function is compressing timelines of dimensions into points as timelike processes. It:
1. Enforces scoping visibility rules
2. Limits dependency access to visible nodes
3. Provides navigation across the space tree

This allows developers to work in a simplified view while maintaining the full structure.

## Implementation Challenges

1. **Filesystem Growth**
    - Structure grows exponentially with new spaces
    - Need pruning mechanisms to manage complexity

2. **Navigation**
    - Easy navigation through entire structure
    - Quick modification of packages across spaces
    - Minimal friction with git/publishing

3. **Private Publishing**
    - Local package publishing
    - Private registry for development
    - Seamless dependency management