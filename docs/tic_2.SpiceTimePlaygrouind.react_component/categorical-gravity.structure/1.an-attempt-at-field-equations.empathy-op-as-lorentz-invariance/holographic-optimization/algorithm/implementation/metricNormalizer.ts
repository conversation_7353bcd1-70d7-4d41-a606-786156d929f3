private async computeMetric(target: Perspective, focus: Perspective): Promise<number> {
    // Use focus context to structure target
    // Returns a measure of how well they align
    const alignment = target.context.entries().reduce((acc, [key, value]) => {
        const focusValue = focus.context.get(key) || 0;
        return acc + Math.pow(value - focusValue, 2);
    }, 0);

    return Math.sqrt(alignment);
}