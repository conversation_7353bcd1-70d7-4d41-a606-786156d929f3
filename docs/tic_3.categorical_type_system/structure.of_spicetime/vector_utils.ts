/**
 * Vector representation in semantic space
 */
export interface VectorRepresentation {
    dimensions: number[];
}

/**
 * Calculate direction change between two vectors
 * Returns a value between 0 (same direction) and 2 (opposite direction)
 */
export function calculateDirectionChange(
    v1: VectorRepresentation,
    v2: VectorRepresentation
): number {
    // Ensure vectors have same dimensions
    if (v1.dimensions.length !== v2.dimensions.length) {
        throw new Error("Vector dimensions must match");
    }

    // Calculate dot product
    let dotProduct = 0;
    for (let i = 0; i < v1.dimensions.length; i++) {
        dotProduct += v1.dimensions[i] * v2.dimensions[i];
    }

    // Calculate magnitudes
    const mag1 = Math.sqrt(
        v1.dimensions.reduce((sum, val) => sum + val * val, 0)
    );

    const mag2 = Math.sqrt(
        v2.dimensions.reduce((sum, val) => sum + val * val, 0)
    );

    // Calculate cosine similarity
    const cosineSimilarity = dotProduct / (mag1 * mag2);

    // Convert to direction change (0 to 2)
    return 1 - cosineSimilarity;
}

// Thresholds for nucleation
export const TIME_NUCLEATION_THRESHOLD = 0.3; // 30% direction change
export const SPACE_NUCLEATION_THRESHOLD = 0.7; // 70% direction change