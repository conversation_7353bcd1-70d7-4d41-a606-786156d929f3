class SpaceTimeTensorFunctor {
applyToTransaction(transaction: Transaction): SpatialRelationship {
// The nodes have no inherent spatial properties before this functor acts
const sourceNode = transaction.source;
const targetNode = transaction.target;

    // The functor *creates* spatial properties through its application
    return {
      distance: this.calculateDistance(sourceNode, targetNode),
      direction: this.calculateDirection(sourceNode, targetNode),
      // The spatial relationship only exists because of the functor's action
      spatialDimensions: this.deriveDimensions(transaction)
    };
}
}