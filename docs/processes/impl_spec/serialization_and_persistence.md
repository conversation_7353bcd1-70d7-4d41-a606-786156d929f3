# Process Serialization and Persistence

This document outlines the approach for serializing and persisting process state to enable long-term continuity and recovery.

## Core Principles

1. **Selective Serialization**: Only serialize essential layers of the process object
2. **Process Definition Stability**: Process definitions live in MongoDB as immutable prototypes
3. **Instance as Leaf Data**: Process instances are serialized as lightweight leaf data
4. **Lineage Preservation**: All processes maintain their lineage to original definitions

## Serialization Strategy

### Process Definition vs. Instance

```typescript
// Process definition (stored in MongoDB)
// Immutable prototype structure
const processDefinition = {
  stages: {
    design: { /* ... */ },
    develop: { /* ... */ },
    build: { /* ... */ },
    // ...
  },
  transitions: { /* ... */ },
  tools: { /* ... */ }
};

// Process instance (serialized state)
// Only the leaf data that differs from prototype
const serializedInstance = {
  id: "process-123",
  currentStage: "develop",
  stateData: {
    artifacts: [/* ... */],
    context: {/* ... */}
  },
  // Lineage reference to definition
  definitionId: "webdev-process-v2"
};
```

### Serialization Layers

1. **Core Identity**: Process ID, type, and lineage
2. **Current Stage**: Active stage and transition history
3. **State Data**: Current artifacts and context
4. **Relationships**: Links to other processes

## Persistence Mechanism

### MongoDB Storage

Process definitions and serialized instances are stored in MongoDB:

```typescript
// MongoDB schema (conceptual)
db.processDefinitions = {
  _id: ObjectId,
  type: String,        // e.g., "webdev", "deployment"
  version: String,     // Semantic version
  stages: Object,      // Stage definitions
  transitions: Object, // Transition rules
  tools: Object        // Available tools
};

db.processInstances = {
  _id: ObjectId,
  definitionId: ObjectId, // Reference to definition
  currentStage: String,
  stateData: Object,      // Serialized state
  createdAt: Date,
  updatedAt: Date,
  lastActive: Date
};
```

## Dynamic Stage Handling

### No Dynamic Stage Creation

Processes do not create stages dynamically. Instead:

1. **Return to Design**: Processes needing new stages return to the design stage
2. **Create New Version**: Design stage creates a new process definition version
3. **Dry Run**: New version is dry-run to reach desired state
4. **Fallback**: Debug stage available if issues occur

```typescript
// When new stages are needed:
if (needsNewStage) {
  // 1. Return to design stage of parent process
  process.transitionTo('design');
  
  // 2. Create new process definition version
  const newDefinition = process.createNewVersion({
    stages: {
      ...currentStages,
      newStage: { /* definition */ }
    }
  });
  
  // 3. Instantiate and dry-run to desired state
  const newProcess = instantiate(newDefinition);
  newProcess.dryRunTo(targetStage);
  
  // 4. If issues occur, transition to debug
  if (newProcess.hasIssues()) {
    newProcess.transitionTo('debug');
  }
}
```

## Recovery Process

### Rehydration from Serialized State

When recovering a process after system restart:

1. **Load Definition**: Retrieve process definition from MongoDB
2. **Create Instance**: Instantiate from definition prototype
3. **Apply State**: Apply serialized state data
4. **Restore Links**: Reconnect to related processes
5. **Resume**: Continue from last known state

```typescript
// Recovery process (conceptual)
function recoverProcess(serializedId) {
  // 1. Load serialized instance
  const serialized = db.processInstances.findOne({_id: serializedId});
  
  // 2. Load definition
  const definition = db.processDefinitions.findOne({_id: serialized.definitionId});
  
  // 3. Create instance from definition
  const process = createProcessFromDefinition(definition);
  
  // 4. Apply serialized state
  process.applyState(serialized.stateData);
  process.setCurrentStage(serialized.currentStage);
  
  // 5. Restore links to other processes
  process.restoreLinks(serialized.links);
  
  return process;
}
```

## Long-Term Persistence

### Thousand-Year Vacation Support

To support extremely long-term persistence (e.g., 1000-year vacation):

1. **Complete Context**: Store sufficient context to understand the process
2. **Format Evolution**: Use formats designed for long-term readability
3. **Self-Description**: Include metadata that describes the structure
4. **Lineage Chain**: Maintain complete lineage to original definitions

```typescript
// Long-term serialization includes additional context
const longTermSerializedState = {
  // Standard serialized state
  ...serializedInstance,
  
  // Long-term context
  metaDescription: "Web development process for Project X",
  formatVersion: "2.0",
  schemaDefinition: { /* Self-describing schema */ },
  lineageChain: [
    { definitionId: "original-webdev-v1", timestamp: "2023-01-01" },
    { definitionId: "webdev-v2", timestamp: "2023-06-15" }
  ],
  
  // Cultural context for future recovery
  domainGlossary: { /* Terms and meanings */ },
  purposeStatement: "This process manages the development of web applications..."
};
```

## Implementation Considerations

1. **Serialization Performance**: Optimize for minimal data storage
2. **Recovery Speed**: Balance between storage size and recovery time
3. **Versioning**: Handle definition version changes gracefully
4. **Partial Recovery**: Support recovering only needed parts of state
5. **Validation**: Verify state integrity during recovery