<!DOCTYPE html><html><head><meta charSet="utf-8"/><meta http-equiv="x-ua-compatible" content="ie=edge"/><meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/><meta name="generator" content="Gatsby 4.25.9"/></head><body><div id="___gatsby"><div style="outline:none" tabindex="-1" id="gatsby-focus-wrapper"><div><a href="/spicetime-architecture/">Back to Home</a><h1>Simple Category Example</h1><p><em>A simple example of creating and using a category</em></p><div><h1>Simple Category Example</h1>
<p>This example demonstrates how to create a category with objects and morphisms, and how to compose morphisms.</p>
<h2>Code Example</h2>
<pre><code class="language-typescript">import { ConcreteCategory } from '../src/atomic/cat';
import { createCatObject } from '../src/atomic/catObject';
import { BaseMorphism } from '../src/atomic/morphism';

// Create a category for numbers
const numberCategory = new ConcreteCategory();

// Create some objects (numbers)
const zero = createCatObject(0, 'zero');
const one = createCatObject(1, 'one');
const two = createCatObject(2, 'two');
const three = createCatObject(3, 'three');

// Add objects to the category
numberCategory.addObject(zero);
numberCategory.addObject(one);
numberCategory.addObject(two);
numberCategory.addObject(three);

// Create morphisms (functions between numbers)
const addOne = new BaseMorphism(
  zero,
  one,
  (x: number) => x + 1
);

const double = new BaseMorphism(
  one,
  two,
  (x: number) => x * 2
);

const addOneToDouble = new BaseMorphism(
  two,
  three,
  (x: number) => x + 1
);

// Add morphisms to the category
numberCategory.addMorphism(addOne);
numberCategory.addMorphism(double);
numberCategory.addMorphism(addOneToDouble);

// Compose morphisms
const composed = numberCategory.compose(addOne, double);
const composed2 = numberCategory.compose(composed, addOneToDouble);

// Use the composed morphism
console.log('0 -> addOne -> double -> addOneToDouble =', composed2.apply(0)); // 5
</code></pre>
<h2>Explanation</h2>
<ol>
<li>We create a category for numbers.</li>
<li>We create objects representing the numbers 0, 1, 2, and 3.</li>
<li>We create morphisms representing functions between these numbers:
<ul>
<li><code>addOne</code>: Adds 1 to a number</li>
<li><code>double</code>: Doubles a number</li>
<li><code>addOneToDouble</code>: Adds 1 to a doubled number</li>
</ul>
</li>
<li>We compose these morphisms to create a new morphism that applies all three operations in sequence.</li>
<li>We apply the composed morphism to the number 0, resulting in 5.</li>
</ol>
<p>This demonstrates the key concept of composition in category theory, where morphisms can be combined to create new morphisms while preserving the structure of the category.</p></div></div></div><div id="gatsby-announcer" style="position:absolute;top:0;width:1px;height:1px;padding:0;overflow:hidden;clip:rect(0, 0, 0, 0);white-space:nowrap;border:0" aria-live="assertive" aria-atomic="true"></div></div><script id="gatsby-script-loader">/*<![CDATA[*/window.pagePath="/examples/simple-category-example/";window.___webpackCompilationHash="b4579f50ce839e9161c3";/*]]>*/</script><script id="gatsby-chunk-mapping">/*<![CDATA[*/window.___chunkMapping={"app":["/app-2fdf907dbeb137b04561.js"],"component---src-pages-index-js":["/component---src-pages-index-js-7962da0d8e4e8eaf2777.js"],"component---src-templates-markdown-page-js":["/component---src-templates-markdown-page-js-26cd28235ba4168b6f41.js"]};/*]]>*/</script><script src="/spicetime-architecture/app-2fdf907dbeb137b04561.js" async=""></script><script src="/spicetime-architecture/framework-3d8283f85588aed88185.js" async=""></script><script src="/spicetime-architecture/webpack-runtime-6f585293bb8c84a6d4e5.js" async=""></script></body></html>