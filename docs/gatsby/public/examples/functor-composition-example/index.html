<!DOCTYPE html><html><head><meta charSet="utf-8"/><meta http-equiv="x-ua-compatible" content="ie=edge"/><meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/><meta name="generator" content="Gatsby 4.25.9"/></head><body><div id="___gatsby"><div style="outline:none" tabindex="-1" id="gatsby-focus-wrapper"><div><a href="/spicetime-architecture/">Back to Home</a><h1>Functor Composition Example</h1><p><em>An example of composing functors to create complex transformations</em></p><div><h1>Functor Composition Example</h1>
<p>This example demonstrates how to compose functors to create more complex transformations between categories.</p>
<h2>Functor Composition</h2>
<p>Functors can be composed, just like functions. If <code>F: C → D</code> and <code>G: D → E</code> are functors, then their composition <code>G ∘ F: C → E</code> is also a functor.</p>
<p>Composing functors allows you to build complex transformations by combining simpler ones.</p>
<h2>Implementation</h2>
<pre><code class="language-typescript">import { 
  ConcreteCategory, 
  createCatObject, 
  BaseMorphism, 
  BaseFunctor 
} from '@future/cat_types';

// Create three categories: numbers, strings, and booleans
const numberCategory = new ConcreteCategory();
const stringCategory = new ConcreteCategory();
const booleanCategory = new ConcreteCategory();

// Create objects for the number category
const zero = createCatObject(0, 'Zero');
const one = createCatObject(1, 'One');
const two = createCatObject(2, 'Two');

numberCategory.addObject(zero);
numberCategory.addObject(one);
numberCategory.addObject(two);

// Create morphisms for the number category
const addOne = new BaseMorphism(
  zero,
  one,
  (x: number) => x + 1
);

const addOneMore = new BaseMorphism(
  one,
  two,
  (x: number) => x + 1
);

numberCategory.addMorphism(addOne);
numberCategory.addMorphism(addOneMore);

// Create objects for the string category
const emptyString = createCatObject('', 'EmptyString');
const shortString = createCatObject('a', 'ShortString');
const longString = createCatObject('aa', 'LongString');

stringCategory.addObject(emptyString);
stringCategory.addObject(shortString);
stringCategory.addObject(longString);

// Create morphisms for the string category
const appendA = new BaseMorphism(
  emptyString,
  shortString,
  (s: string) => s + 'a'
);

const appendAnotherA = new BaseMorphism(
  shortString,
  longString,
  (s: string) => s + 'a'
);

stringCategory.addMorphism(appendA);
stringCategory.addMorphism(appendAnotherA);

// Create objects for the boolean category
const falseBool = createCatObject(false, 'False');
const trueBool = createCatObject(true, 'True');

booleanCategory.addObject(falseBool);
booleanCategory.addObject(trueBool);

// Create morphisms for the boolean category
const negate = new BaseMorphism(
  falseBool,
  trueBool,
  (b: boolean) => !b
);

const negateAgain = new BaseMorphism(
  trueBool,
  falseBool,
  (b: boolean) => !b
);

booleanCategory.addMorphism(negate);
booleanCategory.addMorphism(negateAgain);

// Create a functor from numbers to strings
const numberToStringFunctor = new BaseFunctor(
  numberCategory,
  stringCategory,
  // Object mapping function
  (obj) => {
    const num = obj.value as number;
    if (num === 0) return emptyString;
    if (num === 1) return shortString;
    if (num === 2) return longString;
    throw new Error(`No mapping for number ${num}`);
  },
  // Morphism mapping function
  (morphism) => {
    if (morphism.source.value === 0 &#x26;&#x26; morphism.target.value === 1) {
      return appendA;
    }
    if (morphism.source.value === 1 &#x26;&#x26; morphism.target.value === 2) {
      return appendAnotherA;
    }
    throw new Error(`No mapping for morphism from ${morphism.source.value} to ${morphism.target.value}`);
  }
);

// Create a functor from strings to booleans
const stringToBooleanFunctor = new BaseFunctor(
  stringCategory,
  booleanCategory,
  // Object mapping function
  (obj) => {
    const str = obj.value as string;
    if (str === '') return falseBool;
    return trueBool;
  },
  // Morphism mapping function
  (morphism) => {
    if (morphism.source.value === '' &#x26;&#x26; morphism.target.value === 'a') {
      return negate;
    }
    if (morphism.source.value === 'a' &#x26;&#x26; morphism.target.value === 'aa') {
      return negateAgain;
    }
    throw new Error(`No mapping for morphism from ${morphism.source.value} to ${morphism.target.value}`);
  }
);

// Compose the functors
function composeFunctors(
  F, 
  G
) {
  return new BaseFunctor(
    F.source,
    G.target,
    // Composed object mapping
    (obj) => G.mapObject(F.mapObject(obj)),
    // Composed morphism mapping
    (morphism) => G.mapMorphism(F.mapMorphism(morphism))
  );
}

const numberToBooleanFunctor = composeFunctors(
  numberToStringFunctor,
  stringToBooleanFunctor
);

// Test the composed functor
console.log(numberToBooleanFunctor.mapObject(zero).value); // false
console.log(numberToBooleanFunctor.mapObject(one).value); // true
console.log(numberToBooleanFunctor.mapObject(two).value); // true

const mappedAddOne = numberToBooleanFunctor.mapMorphism(addOne);
console.log(mappedAddOne.apply(false)); // true

const mappedAddOneMore = numberToBooleanFunctor.mapMorphism(addOneMore);
console.log(mappedAddOneMore.apply(true)); // false

// Verify that the composed functor preserves composition
const composed = numberCategory.compose(addOne, addOneMore);
const mappedComposed = numberToBooleanFunctor.mapMorphism(composed);
const mappedSeparately = booleanCategory.compose(
  numberToBooleanFunctor.mapMorphism(addOne),
  numberToBooleanFunctor.mapMorphism(addOneMore)
);

console.log(mappedComposed.apply(false)); // false
console.log(mappedSeparately.apply(false)); // false
</code></pre>
<h2>Explanation</h2>
<ol>
<li>
<p>We create three categories:</p>
<ul>
<li><code>numberCategory</code> with objects 0, 1, 2 and morphisms for adding 1</li>
<li><code>stringCategory</code> with objects '', 'a', 'aa' and morphisms for appending 'a'</li>
<li><code>booleanCategory</code> with objects false, true and morphisms for negation</li>
</ul>
</li>
<li>
<p>We create two functors:</p>
<ul>
<li><code>numberToStringFunctor</code> maps numbers to strings (0 → '', 1 → 'a', 2 → 'aa')</li>
<li><code>stringToBooleanFunctor</code> maps strings to booleans ('' → false, 'a' → true, 'aa' → true)</li>
</ul>
</li>
<li>
<p>We compose these functors to create a new functor <code>numberToBooleanFunctor</code> that maps directly from numbers to booleans.</p>
</li>
<li>
<p>We test the composed functor by:</p>
<ul>
<li>Mapping objects from the number category to the boolean category</li>
<li>Mapping morphisms from the number category to the boolean category</li>
<li>Verifying that the functor preserves composition</li>
</ul>
</li>
</ol>
<h2>Benefits of Functor Composition</h2>
<p>Composing functors provides several benefits:</p>
<ol>
<li><strong>Modularity</strong>: You can build complex transformations by combining simpler ones.</li>
<li><strong>Reusability</strong>: You can reuse existing functors to create new ones.</li>
<li><strong>Abstraction</strong>: You can abstract away the intermediate steps of a transformation.</li>
<li><strong>Correctness</strong>: If the component functors preserve structure, the composed functor will too.</li>
</ol>
<h2>Next Steps</h2>
<p>Try implementing other functors and composing them in different ways. For example, you could create a functor from booleans to numbers and compose it with the existing functors to create a cycle.</p></div></div></div><div id="gatsby-announcer" style="position:absolute;top:0;width:1px;height:1px;padding:0;overflow:hidden;clip:rect(0, 0, 0, 0);white-space:nowrap;border:0" aria-live="assertive" aria-atomic="true"></div></div><script id="gatsby-script-loader">/*<![CDATA[*/window.pagePath="/examples/functor-composition-example/";window.___webpackCompilationHash="b4579f50ce839e9161c3";/*]]>*/</script><script id="gatsby-chunk-mapping">/*<![CDATA[*/window.___chunkMapping={"app":["/app-2fdf907dbeb137b04561.js"],"component---src-pages-index-js":["/component---src-pages-index-js-7962da0d8e4e8eaf2777.js"],"component---src-templates-markdown-page-js":["/component---src-templates-markdown-page-js-26cd28235ba4168b6f41.js"]};/*]]>*/</script><script src="/spicetime-architecture/app-2fdf907dbeb137b04561.js" async=""></script><script src="/spicetime-architecture/framework-3d8283f85588aed88185.js" async=""></script><script src="/spicetime-architecture/webpack-runtime-6f585293bb8c84a6d4e5.js" async=""></script></body></html>