{"componentChunkName": "component---src-templates-markdown-page-js", "path": "/api/interfaces/CatObject/", "result": {"data": {"markdownRemark": {"html": "<p><a href=\"../README.md\"><strong>Cat Types API Reference v0.1.0</strong></a></p>\n<hr>\n<p><a href=\"../README.md\">Cat Types API Reference</a> / CatObject</p>\n<h1>Interface: CatObject&#x3C;T></h1>\n<p>Defined in: <a href=\"https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/catObject.ts#L14\">atomic/catObject.ts:14</a></p>\n<p>Represents an object in a category</p>\n<h2>Type Parameters</h2>\n<p>• <strong>T</strong> = <code>any</code></p>\n<p>Type of the value contained in the object</p>\n<h2>Properties</h2>\n<h3>id</h3>\n<blockquote>\n<p><code>readonly</code> <strong>id</strong>: <a href=\"CatId.md\"><code>CatId</code></a></p>\n</blockquote>\n<p>Defined in: <a href=\"https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/catObject.ts#L18\">atomic/catObject.ts:18</a></p>\n<p>Unique identifier for the object</p>\n<hr>\n<h3>value</h3>\n<blockquote>\n<p><code>readonly</code> <strong>value</strong>: <code>T</code></p>\n</blockquote>\n<p>Defined in: <a href=\"https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/catObject.ts#L23\">atomic/catObject.ts:23</a></p>\n<p>Value contained in the object</p>", "frontmatter": {"title": "", "description": null}}}, "pageContext": {"id": "9a2e3e57-11e8-5753-bab5-2d01170fb409"}}, "staticQueryHashes": []}