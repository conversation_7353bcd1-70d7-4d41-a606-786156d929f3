{"componentChunkName": "component---src-templates-markdown-page-js", "path": "/api/interfaces/Zipper/", "result": {"data": {"markdownRemark": {"html": "<p><a href=\"../README.md\"><strong>Cat Types API Reference v0.1.0</strong></a></p>\n<hr>\n<p><a href=\"../README.md\">Cat Types API Reference</a> / Zipper</p>\n<h1>Interface: Zipper&#x3C;T></h1>\n<p>Defined in: <a href=\"https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/zipper.fr.ts#L29\">functors/zipper.fr.ts:29</a></p>\n<p>Zipper - A structure that allows navigation and local editing\nwith a focus point and surrounding context</p>\n<p>Zippers provide a way to:</p>\n<ol>\n<li>Focus on a specific element in a data structure</li>\n<li>Navigate to adjacent elements (up, down, left, right)</li>\n<li>Modify the focused element</li>\n<li>Reconstruct the entire data structure with modifications</li>\n</ol>\n<h2>Type Parameters</h2>\n<p>• <strong>T</strong></p>\n<p>The type of elements in the structure</p>\n<h2>Properties</h2>\n<h3>context</h3>\n<blockquote>\n<p><strong>context</strong>: <a href=\"ZipperContext.md\"><code>ZipperContext</code></a>&#x3C;<code>T</code>></p>\n</blockquote>\n<p>Defined in: <a href=\"https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/zipper.fr.ts#L38\">functors/zipper.fr.ts:38</a></p>\n<p>The context (path) to the focused element</p>\n<hr>\n<h3>focus</h3>\n<blockquote>\n<p><strong>focus</strong>: <code>T</code></p>\n</blockquote>\n<p>Defined in: <a href=\"https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/zipper.fr.ts#L33\">functors/zipper.fr.ts:33</a></p>\n<p>The currently focused element</p>\n<h2>Methods</h2>\n<h3>down()</h3>\n<blockquote>\n<p><strong>down</strong>(): <code>null</code> | <a href=\"Zipper.md\"><code>Zipper</code></a>&#x3C;<code>T</code>></p>\n</blockquote>\n<p>Defined in: <a href=\"https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/zipper.fr.ts#L50\">functors/zipper.fr.ts:50</a></p>\n<p>Move the focus down in the structure</p>\n<h4>Returns</h4>\n<p><code>null</code> | <a href=\"Zipper.md\"><code>Zipper</code></a>&#x3C;<code>T</code>></p>\n<p>A new zipper with the focus moved down, or null if at the bottom</p>\n<hr>\n<h3>left()</h3>\n<blockquote>\n<p><strong>left</strong>(): <code>null</code> | <a href=\"Zipper.md\"><code>Zipper</code></a>&#x3C;<code>T</code>></p>\n</blockquote>\n<p>Defined in: <a href=\"https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/zipper.fr.ts#L56\">functors/zipper.fr.ts:56</a></p>\n<p>Move the focus left in the structure</p>\n<h4>Returns</h4>\n<p><code>null</code> | <a href=\"Zipper.md\"><code>Zipper</code></a>&#x3C;<code>T</code>></p>\n<p>A new zipper with the focus moved left, or null if at the leftmost</p>\n<hr>\n<h3>reconstruct()</h3>\n<blockquote>\n<p><strong>reconstruct</strong>(): <code>T</code></p>\n</blockquote>\n<p>Defined in: <a href=\"https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/zipper.fr.ts#L75\">functors/zipper.fr.ts:75</a></p>\n<p>Reconstruct the entire data structure with any modifications</p>\n<h4>Returns</h4>\n<p><code>T</code></p>\n<p>The reconstructed data structure</p>\n<hr>\n<h3>right()</h3>\n<blockquote>\n<p><strong>right</strong>(): <code>null</code> | <a href=\"Zipper.md\"><code>Zipper</code></a>&#x3C;<code>T</code>></p>\n</blockquote>\n<p>Defined in: <a href=\"https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/zipper.fr.ts#L62\">functors/zipper.fr.ts:62</a></p>\n<p>Move the focus right in the structure</p>\n<h4>Returns</h4>\n<p><code>null</code> | <a href=\"Zipper.md\"><code>Zipper</code></a>&#x3C;<code>T</code>></p>\n<p>A new zipper with the focus moved right, or null if at the rightmost</p>\n<hr>\n<h3>up()</h3>\n<blockquote>\n<p><strong>up</strong>(): <code>null</code> | <a href=\"Zipper.md\"><code>Zipper</code></a>&#x3C;<code>T</code>></p>\n</blockquote>\n<p>Defined in: <a href=\"https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/zipper.fr.ts#L44\">functors/zipper.fr.ts:44</a></p>\n<p>Move the focus up in the structure</p>\n<h4>Returns</h4>\n<p><code>null</code> | <a href=\"Zipper.md\"><code>Zipper</code></a>&#x3C;<code>T</code>></p>\n<p>A new zipper with the focus moved up, or null if at the top</p>\n<hr>\n<h3>update()</h3>\n<blockquote>\n<p><strong>update</strong>(<code>newFocus</code>): <a href=\"Zipper.md\"><code>Zipper</code></a>&#x3C;<code>T</code>></p>\n</blockquote>\n<p>Defined in: <a href=\"https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/zipper.fr.ts#L69\">functors/zipper.fr.ts:69</a></p>\n<p>Update the focused element</p>\n<h4>Parameters</h4>\n<h5>newFocus</h5>\n<p><code>T</code></p>\n<p>The new value for the focused element</p>\n<h4>Returns</h4>\n<p><a href=\"Zipper.md\"><code>Zipper</code></a>&#x3C;<code>T</code>></p>\n<p>A new zipper with the focus updated</p>", "frontmatter": {"title": "", "description": null}}}, "pageContext": {"id": "75f4b26a-f38d-5506-a91d-bf5de88a8be1"}}, "staticQueryHashes": []}