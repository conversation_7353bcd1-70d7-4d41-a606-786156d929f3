{"componentChunkName": "component---src-templates-markdown-page-js", "path": "/api/functions/createCatObject/", "result": {"data": {"markdownRemark": {"html": "<p><a href=\"../README.md\"><strong>Cat Types API Reference v0.1.0</strong></a></p>\n<hr>\n<p><a href=\"../README.md\">Cat Types API Reference</a> / createCatObject</p>\n<h1>Function: createCatObject()</h1>\n<blockquote>\n<p><strong>createCatObject</strong>&#x3C;<code>T</code>>(<code>value</code>, <code>name</code>?): <a href=\"../interfaces/CatObject.md\"><code>CatObject</code></a>&#x3C;<code>T</code>></p>\n</blockquote>\n<p>Defined in: <a href=\"https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/catObject.ts#L33\">atomic/catObject.ts:33</a></p>\n<p>Creates a new category object</p>\n<h2>Type Parameters</h2>\n<p>• <strong>T</strong></p>\n<h2>Parameters</h2>\n<h3>value</h3>\n<p><code>T</code></p>\n<p>Value to be contained in the object</p>\n<h3>name?</h3>\n<p><code>string</code></p>\n<p>Optional name for debugging</p>\n<h2>Returns</h2>\n<p><a href=\"../interfaces/CatObject.md\"><code>CatObject</code></a>&#x3C;<code>T</code>></p>\n<p>A new CatObject instance</p>", "frontmatter": {"title": "", "description": null}}}, "pageContext": {"id": "ee2641a7-a70d-5821-b00e-afea26262f28"}}, "staticQueryHashes": []}