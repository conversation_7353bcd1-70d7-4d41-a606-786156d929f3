{"componentChunkName": "component---src-templates-markdown-page-js", "path": "/api/functions/createCatId/", "result": {"data": {"markdownRemark": {"html": "<p><a href=\"../README.md\"><strong>Cat Types API Reference v0.1.0</strong></a></p>\n<hr>\n<p><a href=\"../README.md\">Cat Types API Reference</a> / createCatId</p>\n<h1>Function: createCatId()</h1>\n<blockquote>\n<p><strong>createCatId</strong>(<code>name</code>?): <a href=\"../interfaces/CatId.md\"><code>CatId</code></a></p>\n</blockquote>\n<p>Defined in: <a href=\"https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/catId.ts#L30\">atomic/catId.ts:30</a></p>\n<p>Creates a new category ID</p>\n<h2>Parameters</h2>\n<h3>name?</h3>\n<p><code>string</code></p>\n<p>Optional name for debugging</p>\n<h2>Returns</h2>\n<p><a href=\"../interfaces/CatId.md\"><code>CatId</code></a></p>\n<p>A new CatId instance</p>", "frontmatter": {"title": "", "description": null}}}, "pageContext": {"id": "f7d0528a-62a9-5e2b-9395-f7e21e618204"}}, "staticQueryHashes": []}