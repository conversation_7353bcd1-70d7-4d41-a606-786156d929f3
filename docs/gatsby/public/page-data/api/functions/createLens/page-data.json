{"componentChunkName": "component---src-templates-markdown-page-js", "path": "/api/functions/createLens/", "result": {"data": {"markdownRemark": {"html": "<p><a href=\"../README.md\"><strong>Cat Types API Reference v0.1.0</strong></a></p>\n<hr>\n<p><a href=\"../README.md\">Cat Types API Reference</a> / createLens</p>\n<h1>Function: createLens()</h1>\n<blockquote>\n<p><strong>createLens</strong>&#x3C;<code>S</code>, <code>A</code>>(<code>getter</code>, <code>setter</code>): <a href=\"../interfaces/Lens.md\"><code>Lens</code></a>&#x3C;<code>S</code>, <code>A</code>></p>\n</blockquote>\n<p>Defined in: <a href=\"https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/lens.fr.ts#L65\">functors/lens.fr.ts:65</a></p>\n<p>Create a lens from getter and setter functions</p>\n<h2>Type Parameters</h2>\n<p>• <strong>S</strong></p>\n<p>• <strong>A</strong></p>\n<h2>Parameters</h2>\n<h3>getter</h3>\n<p>(<code>s</code>) => <code>A</code></p>\n<p>Function to extract the focus from the structure</p>\n<h3>setter</h3>\n<p>(<code>s</code>, <code>a</code>) => <code>S</code></p>\n<p>Function to update the focus in the structure</p>\n<h2>Returns</h2>\n<p><a href=\"../interfaces/Lens.md\"><code>Lens</code></a>&#x3C;<code>S</code>, <code>A</code>></p>\n<p>A new lens with the specified getter and setter</p>\n<h2>Static</h2>", "frontmatter": {"title": "", "description": null}}}, "pageContext": {"id": "561acc1d-92fa-5afb-a103-7d38d002847c"}}, "staticQueryHashes": []}