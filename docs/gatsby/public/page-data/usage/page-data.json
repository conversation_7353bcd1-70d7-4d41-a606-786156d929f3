{"componentChunkName": "component---src-pages-usage-js", "path": "/usage/", "result": {"data": {"allMarkdownRemark": {"nodes": [{"id": "9a2e3e57-11e8-5753-bab5-2d01170fb409", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/api/interfaces/CatObject.md", "fields": {"slug": "/api/interfaces/CatObject/"}, "frontmatter": {"title": "", "description": null}}, {"id": "72aa54ec-d360-5039-9cd4-7876f9ada117", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/api/interfaces/CatId.md", "fields": {"slug": "/api/interfaces/CatId/"}, "frontmatter": {"title": "", "description": null}}, {"id": "40402650-fd54-5d3f-9b78-aa7a1cb91971", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/api/interfaces/Functor.md", "fields": {"slug": "/api/interfaces/Functor/"}, "frontmatter": {"title": "", "description": null}}, {"id": "b89c3d71-4f00-5514-a7da-8659dbe30231", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/api/interfaces/Monad.md", "fields": {"slug": "/api/interfaces/Monad/"}, "frontmatter": {"title": "", "description": null}}, {"id": "a3999465-8526-51c8-b7d1-86a3083ee891", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/api/interfaces/ZipperContext.md", "fields": {"slug": "/api/interfaces/ZipperContext/"}, "frontmatter": {"title": "", "description": null}}, {"id": "ee2641a7-a70d-5821-b00e-afea26262f28", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/api/functions/createCatObject.md", "fields": {"slug": "/api/functions/createCatObject/"}, "frontmatter": {"title": "", "description": null}}, {"id": "228e269a-f05c-55e9-9453-38a700e01c32", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/api/functions/composeLens.md", "fields": {"slug": "/api/functions/composeLens/"}, "frontmatter": {"title": "", "description": null}}, {"id": "561acc1d-92fa-5afb-a103-7d38d002847c", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/api/functions/createLens.md", "fields": {"slug": "/api/functions/createLens/"}, "frontmatter": {"title": "", "description": null}}, {"id": "f7d0528a-62a9-5e2b-9395-f7e21e618204", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/api/functions/createCatId.md", "fields": {"slug": "/api/functions/createCatId/"}, "frontmatter": {"title": "", "description": null}}, {"id": "ec5b0f53-021d-5e69-9a68-1b0d006fadc8", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/api/functions/createListZipper.md", "fields": {"slug": "/api/functions/createListZipper/"}, "frontmatter": {"title": "", "description": null}}, {"id": "484eb0de-214a-51a7-aa3a-224835d1bebd", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/api/functions/index.md", "fields": {"slug": "/api/functions/"}, "frontmatter": {"title": "", "description": null}}, {"id": "d6b7b313-2ddb-5adc-9ea1-29a9620fda4e", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/api/functions/createTreeZipper.md", "fields": {"slug": "/api/functions/createTreeZipper/"}, "frontmatter": {"title": "", "description": null}}, {"id": "75f4b26a-f38d-5506-a91d-bf5de88a8be1", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/api/interfaces/Zipper.md", "fields": {"slug": "/api/interfaces/Zipper/"}, "frontmatter": {"title": "", "description": null}}, {"id": "51512040-b309-52aa-a7a9-a15dc51be1c0", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/api/functions/prop.md", "fields": {"slug": "/api/functions/prop/"}, "frontmatter": {"title": "", "description": null}}, {"id": "a748080a-9f38-5caa-9524-154804943c89", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/api/classes/BaseFunctor.md", "fields": {"slug": "/api/classes/BaseFunctor/"}, "frontmatter": {"title": "", "description": null}}, {"id": "00e20ba2-8c63-5d80-92e7-c2b8c95b9f82", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/api/classes/BaseMonad.md", "fields": {"slug": "/api/classes/BaseMonad/"}, "frontmatter": {"title": "", "description": null}}, {"id": "e8f31a4e-bc15-5826-8914-9b0eca424ca9", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/api/classes/ConcreteCategory.md", "fields": {"slug": "/api/classes/ConcreteCategory/"}, "frontmatter": {"title": "", "description": null}}, {"id": "09e33b91-a510-56d1-8bb3-b1c4112bf231", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/api/classes/CategoryError.md", "fields": {"slug": "/api/classes/CategoryError/"}, "frontmatter": {"title": "", "description": null}}, {"id": "f9335567-cc7e-5d81-ad9b-5c2ddb40674c", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/api/classes/MonadError.md", "fields": {"slug": "/api/classes/MonadError/"}, "frontmatter": {"title": "", "description": null}}, {"id": "9de2b556-2278-5ff2-96e7-d095218a9b2d", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/api/classes/BaseMorphism.md", "fields": {"slug": "/api/classes/BaseMorphism/"}, "frontmatter": {"title": "", "description": null}}, {"id": "77e4bd80-5ac8-5d80-8058-21ed10076009", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/api/classes/LensFunctor.md", "fields": {"slug": "/api/classes/LensFunctor/"}, "frontmatter": {"title": "", "description": null}}, {"id": "592b41c4-a275-5a9e-8d8b-fca0320eb3d1", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/api/classes/MorphismError.md", "fields": {"slug": "/api/classes/MorphismError/"}, "frontmatter": {"title": "", "description": null}}, {"id": "c83aa850-a870-5e6f-a5b1-24c462caa39b", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/api/interfaces/Morphism.md", "fields": {"slug": "/api/interfaces/Morphism/"}, "frontmatter": {"title": "", "description": null}}, {"id": "e6ce7645-1d0b-5b8b-87db-f75f3cf9d905", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/api/classes/FunctorError.md", "fields": {"slug": "/api/classes/FunctorError/"}, "frontmatter": {"title": "", "description": null}}, {"id": "9ba23027-ead5-548f-a792-67806bf79afd", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/api/interfaces/Lens.md", "fields": {"slug": "/api/interfaces/Lens/"}, "frontmatter": {"title": "", "description": null}}, {"id": "60251d7f-1b15-5412-a900-701c60d1f703", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/api/interfaces/Category.md", "fields": {"slug": "/api/interfaces/Category/"}, "frontmatter": {"title": "", "description": null}}, {"id": "fd4de27b-4036-557f-a503-be1e5ac4fde5", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/api/classes/ZipperFunctor.md", "fields": {"slug": "/api/classes/ZipperFunctor/"}, "frontmatter": {"title": "", "description": null}}, {"id": "e5b800d5-124e-5b61-830c-e96e891a4217", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/usage/practical/web-dev-guide.md", "fields": {"slug": "/usage/practical/web-dev-guide/"}, "frontmatter": {"title": "Practical Guide for Web Developers", "description": "How to use cat_types in real-world web development projects"}}, {"id": "a0dce163-fe40-53ce-9dd8-619f8277297f", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/README.md", "fields": {"slug": "/README/"}, "frontmatter": {"title": "", "description": null}}, {"id": "ecc0e9e1-c1d3-5c23-b1e9-1ae13e4e2327", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/examples/README.md", "fields": {"slug": "/examples/README/"}, "frontmatter": {"title": "", "description": null}}, {"id": "baf72621-53a0-55b6-bd80-b59bd5cacb37", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/examples/data-transformation-example.md", "fields": {"slug": "/examples/data-transformation-example/"}, "frontmatter": {"title": "Data Transformation with Categories", "description": "An example of using categories and functors for data transformation pipelines"}}, {"id": "1f5abaeb-3200-53fc-91bf-59e1d07dcdbb", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/examples/simple-category-example.md", "fields": {"slug": "/examples/simple-category-example/"}, "frontmatter": {"title": "Simple Category Example", "description": "A simple example of creating and using a category"}}, {"id": "ac383b8d-b0b9-5f48-b31b-df174caf9d3a", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/examples/functor-composition-example.md", "fields": {"slug": "/examples/functor-composition-example/"}, "frontmatter": {"title": "Functor Composition Example", "description": "An example of composing functors to create complex transformations"}}, {"id": "de38e9a4-026a-5a03-955e-f33cba6f687c", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/examples/maybe-monad-example.md", "fields": {"slug": "/examples/maybe-monad-example/"}, "frontmatter": {"title": "Maybe Monad Example", "description": "A practical example of using the Maybe monad for handling nullable values"}}, {"id": "bd691cb3-15ac-510e-a7a6-edf4bdb9d1c9", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/api/index.md", "fields": {"slug": "/api/"}, "frontmatter": {"title": "API Reference", "description": "Complete API reference for the cat_types package"}}, {"id": "79072ca7-6d83-55fb-90ba-b9b048137725", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/usage/category-theory-concepts.md", "fields": {"slug": "/usage/category-theory-concepts/"}, "frontmatter": {"title": "Category Theory Concepts", "description": "An introduction to the fundamental concepts of category theory as implemented in the cat_types package"}}, {"id": "e3ae95ab-1eb0-5476-a6aa-2e4e8b4dece1", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/usage/category-theory-in-typescript.md", "fields": {"slug": "/usage/category-theory-in-typescript/"}, "frontmatter": {"title": "Category Theory in TypeScript", "description": "How to apply category theory concepts in TypeScript using the cat_types package"}}, {"id": "0c01a245-b4df-5f0e-9e46-5bf4130cdd17", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/usage/advanced-concepts.md", "fields": {"slug": "/usage/advanced-concepts/"}, "frontmatter": {"title": "Advanced Concepts in Cat Types", "description": "Explore advanced category theory concepts in the cat_types package"}}, {"id": "535642d9-b6bf-599e-99dd-397fdf85dd66", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/usage/practical/index.md", "fields": {"slug": "/usage/practical/"}, "frontmatter": {"title": "Practical Guides", "description": "Practical implementation guides for using cat_types in real-world applications"}}, {"id": "35098704-2b59-568c-bb72-a11cd8e503a5", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/usage/README.md", "fields": {"slug": "/usage/README/"}, "frontmatter": {"title": "", "description": null}}, {"id": "77ddadf7-2077-5cbb-be2a-42a37b42ce97", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/usage/practical/error-handling.md", "fields": {"slug": "/usage/practical/error-handling/"}, "frontmatter": {"title": "Error Handling Guide", "description": "Robust error handling with monads"}}, {"id": "a5fb577c-37ec-587b-a595-78f223c9242a", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/api/README.md", "fields": {"slug": "/api/README/"}, "frontmatter": {"title": "", "description": null}}, {"id": "2c35dbd9-6dff-5b06-ac54-63c2f5232bae", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/usage/quick-start.md", "fields": {"slug": "/usage/quick-start/"}, "frontmatter": {"title": "Quick Start Guide", "description": "Get started quickly with the cat_types package"}}, {"id": "8b511cee-bcef-5a9e-a845-4773c4d7b1b4", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/usage/practical/state-management.md", "fields": {"slug": "/usage/practical/state-management/"}, "frontmatter": {"title": "State Management Guide", "description": "Implementing state management with lenses and monads"}}, {"id": "5ce68fa3-122f-544b-9dc5-a735a24da1e8", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/usage/getting-started.md", "fields": {"slug": "/usage/getting-started/"}, "frontmatter": {"title": "Getting Started with Cat Types", "description": "Learn the basics of using the cat_types package"}}, {"id": "bc74e15c-30d1-5bb6-aaa9-4dd53aa04654", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/usage/working-with-functors.md", "fields": {"slug": "/usage/working-with-functors/"}, "frontmatter": {"title": "Working with Functors", "description": "Learn how to create and work with functors in the cat_types package"}}, {"id": "0189577a-4e71-5be6-807e-2d27effdb847", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/usage/working-with-categories.md", "fields": {"slug": "/usage/working-with-categories/"}, "frontmatter": {"title": "Working with Categories", "description": "Learn how to create and work with categories in the cat_types package"}}, {"id": "b7768144-41e0-5ded-8dce-4719048f4c07", "fileAbsolutePath": "/Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/docs/usage/working-with-monads.md", "fields": {"slug": "/usage/working-with-monads/"}, "frontmatter": {"title": "Working with Monads", "description": "Learn how to create and work with monads in the cat_types package"}}]}}, "pageContext": {}}, "staticQueryHashes": []}