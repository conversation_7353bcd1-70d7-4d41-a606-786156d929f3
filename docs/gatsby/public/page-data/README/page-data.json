{"componentChunkName": "component---src-templates-markdown-page-js", "path": "/README/", "result": {"data": {"markdownRemark": {"html": "<h1>Cat Types Documentation</h1>\n<p>This directory contains documentation for the <code>cat_types</code> package, which implements category theory concepts in TypeScript.</p>\n<h2>Documentation Structure</h2>\n<ul>\n<li><strong><a href=\"./usage/README.md\">Usage</a></strong>: Usage guides and tutorials</li>\n<li><strong><a href=\"./api/README.md\">API Reference</a></strong>: API reference documentation generated by TypeDoc</li>\n<li><strong><a href=\"./examples/README.md\">Examples</a></strong>: Code examples with explanations</li>\n</ul>\n<h2>Integration with Repository Documentation</h2>\n<p>This documentation is designed to be integrated with the repository's central documentation system. The structure follows a pattern that allows for:</p>\n<ol>\n<li>Co-location of documentation with code</li>\n<li>Integration of written usage docs with TypeDoc reference guides</li>\n<li>Collection of documentation into a central documentation site</li>\n</ol>\n<h2>Building the Documentation</h2>\n<p>To build the API reference documentation:</p>\n<pre><code class=\"language-bash\">npm run docs\n</code></pre>\n<p>This will generate the TypeDoc API reference in the <code>docs/api</code> directory.</p>", "frontmatter": {"title": "", "description": null}}}, "pageContext": {"id": "a0dce163-fe40-53ce-9dd8-619f8277297f"}}, "staticQueryHashes": []}