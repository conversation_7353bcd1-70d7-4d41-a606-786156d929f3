<!DOCTYPE html><html><head><meta charSet="utf-8"/><meta http-equiv="x-ua-compatible" content="ie=edge"/><meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/><meta name="generator" content="Gatsby 4.25.9"/></head><body><div id="___gatsby"><div style="outline:none" tabindex="-1" id="gatsby-focus-wrapper"><div><a href="/spicetime-architecture/">Back to Home</a><h1></h1><div><p><a href="../README.md"><strong>Cat Types API Reference v0.1.0</strong></a></p>
<hr>
<p><a href="../README.md">Cat Types API Reference</a> / createCatObject</p>
<h1>Function: createCatObject()</h1>
<blockquote>
<p><strong>createCatObject</strong>&#x3C;<code>T</code>>(<code>value</code>, <code>name</code>?): <a href="../interfaces/CatObject.md"><code>CatObject</code></a>&#x3C;<code>T</code>></p>
</blockquote>
<p>Defined in: <a href="https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/catObject.ts#L33">atomic/catObject.ts:33</a></p>
<p>Creates a new category object</p>
<h2>Type Parameters</h2>
<p>• <strong>T</strong></p>
<h2>Parameters</h2>
<h3>value</h3>
<p><code>T</code></p>
<p>Value to be contained in the object</p>
<h3>name?</h3>
<p><code>string</code></p>
<p>Optional name for debugging</p>
<h2>Returns</h2>
<p><a href="../interfaces/CatObject.md"><code>CatObject</code></a>&#x3C;<code>T</code>></p>
<p>A new CatObject instance</p></div></div></div><div id="gatsby-announcer" style="position:absolute;top:0;width:1px;height:1px;padding:0;overflow:hidden;clip:rect(0, 0, 0, 0);white-space:nowrap;border:0" aria-live="assertive" aria-atomic="true"></div></div><script id="gatsby-script-loader">/*<![CDATA[*/window.pagePath="/api/functions/createCatObject/";window.___webpackCompilationHash="b4579f50ce839e9161c3";/*]]>*/</script><script id="gatsby-chunk-mapping">/*<![CDATA[*/window.___chunkMapping={"app":["/app-2fdf907dbeb137b04561.js"],"component---src-pages-index-js":["/component---src-pages-index-js-7962da0d8e4e8eaf2777.js"],"component---src-templates-markdown-page-js":["/component---src-templates-markdown-page-js-26cd28235ba4168b6f41.js"]};/*]]>*/</script><script src="/spicetime-architecture/app-2fdf907dbeb137b04561.js" async=""></script><script src="/spicetime-architecture/framework-3d8283f85588aed88185.js" async=""></script><script src="/spicetime-architecture/webpack-runtime-6f585293bb8c84a6d4e5.js" async=""></script></body></html>