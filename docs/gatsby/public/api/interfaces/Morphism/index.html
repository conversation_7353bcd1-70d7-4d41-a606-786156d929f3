<!DOCTYPE html><html><head><meta charSet="utf-8"/><meta http-equiv="x-ua-compatible" content="ie=edge"/><meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/><meta name="generator" content="Gatsby 4.25.9"/></head><body><div id="___gatsby"><div style="outline:none" tabindex="-1" id="gatsby-focus-wrapper"><div><a href="/spicetime-architecture/">Back to Home</a><h1></h1><div><p><a href="../README.md"><strong>Cat Types API Reference v0.1.0</strong></a></p>
<hr>
<p><a href="../README.md">Cat Types API Reference</a> / Morphism</p>
<h1>Interface: Morphism&#x3C;O></h1>
<p>Defined in: <a href="https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/morphism.ts#L15">atomic/morphism.ts:15</a></p>
<p>Represents a morphism (arrow) between objects in a category</p>
<h2>Type Parameters</h2>
<p>• <strong>O</strong> <em>extends</em> <a href="CatObject.md"><code>CatObject</code></a></p>
<p>Type of objects in the category</p>
<h2>Properties</h2>
<h3>source</h3>
<blockquote>
<p><strong>source</strong>: <code>O</code></p>
</blockquote>
<p>Defined in: <a href="https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/morphism.ts#L19">atomic/morphism.ts:19</a></p>
<p>Source object of the morphism</p>
<hr>
<h3>target</h3>
<blockquote>
<p><strong>target</strong>: <code>O</code></p>
</blockquote>
<p>Defined in: <a href="https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/morphism.ts#L24">atomic/morphism.ts:24</a></p>
<p>Target object of the morphism</p>
<h2>Methods</h2>
<h3>apply()</h3>
<blockquote>
<p><strong>apply</strong>(<code>input</code>): <code>any</code></p>
</blockquote>
<p>Defined in: <a href="https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/morphism.ts#L31">atomic/morphism.ts:31</a></p>
<p>Applies the morphism to an input</p>
<h4>Parameters</h4>
<h5>input</h5>
<p><code>any</code></p>
<p>Input to apply the morphism to</p>
<h4>Returns</h4>
<p><code>any</code></p>
<p>Result of applying the morphism</p></div></div></div><div id="gatsby-announcer" style="position:absolute;top:0;width:1px;height:1px;padding:0;overflow:hidden;clip:rect(0, 0, 0, 0);white-space:nowrap;border:0" aria-live="assertive" aria-atomic="true"></div></div><script id="gatsby-script-loader">/*<![CDATA[*/window.pagePath="/api/interfaces/Morphism/";window.___webpackCompilationHash="b4579f50ce839e9161c3";/*]]>*/</script><script id="gatsby-chunk-mapping">/*<![CDATA[*/window.___chunkMapping={"app":["/app-2fdf907dbeb137b04561.js"],"component---src-pages-index-js":["/component---src-pages-index-js-7962da0d8e4e8eaf2777.js"],"component---src-templates-markdown-page-js":["/component---src-templates-markdown-page-js-26cd28235ba4168b6f41.js"]};/*]]>*/</script><script src="/spicetime-architecture/app-2fdf907dbeb137b04561.js" async=""></script><script src="/spicetime-architecture/framework-3d8283f85588aed88185.js" async=""></script><script src="/spicetime-architecture/webpack-runtime-6f585293bb8c84a6d4e5.js" async=""></script></body></html>