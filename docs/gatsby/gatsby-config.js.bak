/**
 * Gatsby configuration for Spicetime Architecture documentation
 */

module.exports = {
  // Path prefix for GitHub Pages
  // This should match the repository name
  pathPrefix: '/spicetime-architecture',

  siteMetadata: {
    title: 'Spicetime Architecture Documentation',
    description: 'Documentation for the Spicetime Architecture',
    author: '@future',
  },

  plugins: [
    // Core Gatsby plugins
    'gatsby-plugin-react-helmet',

    // For processing Markdown files
    {
      resolve: 'gatsby-source-filesystem',
      options: {
        name: 'cat_types',
        path: `${__dirname}/../../packages/utils/origin.rfr/cat_types.obj/docs`,
      },
    },
    {
      resolve: 'gatsby-transformer-remark',
      options: {
        plugins: [
          'gatsby-remark-prismjs',
          'gatsby-remark-autolink-headers',
        ],
      },
    },

    // For syntax highlighting
    'gatsby-plugin-sass',
    {
      resolve: 'gatsby-plugin-typography',
      options: {
        pathToConfigModule: 'src/utils/typography',
      },
    },
  ],
};
