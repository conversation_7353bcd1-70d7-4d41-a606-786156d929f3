/**
 * Implement Gatsby's Node APIs in this file.
 * See: https://www.gatsbyjs.com/docs/reference/config-files/gatsby-node/
 */

const path = require('path');
const { createFilePath } = require('gatsby-source-filesystem');

// Create pages from markdown files
exports.createPages = async ({ graphql, actions, reporter }) => {
  const { createPage } = actions;

  // Define template for markdown pages
  const markdownPageTemplate = path.resolve('./src/templates/markdown-page.js');

  // Get all markdown files
  const result = await graphql(`
    {
      allMarkdownRemark {
        nodes {
          id
          fields {
            slug
          }
        }
      }
    }
  `);

  if (result.errors) {
    reporter.panicOnBuild('Error loading markdown files', result.errors);
    return;
  }

  const posts = result.data.allMarkdownRemark.nodes;

  // Create pages for each markdown file
  if (posts.length > 0) {
    posts.forEach((post) => {
      createPage({
        path: post.fields.slug,
        component: markdownPageTemplate,
        context: {
          id: post.id,
        },
      });
    });
  }
};

// Add custom fields to markdown nodes
exports.onCreateNode = ({ node, actions, getNode }) => {
  const { createNodeField } = actions;

  if (node.internal.type === 'MarkdownRemark') {
    // Create slug field
    const slug = createFilePath({ node, getNode });
    createNodeField({
      name: 'slug',
      node,
      value: slug,
    });
  }
};
