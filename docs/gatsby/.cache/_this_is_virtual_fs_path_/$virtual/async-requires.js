exports.components = {
  "component---cache-dev-404-page-js": () => import("./../../dev-404-page.js?export=default" /* webpackChunkName: "component---cache-dev-404-page-js" */),
  "component---src-pages-examples-js": () => import("./../../../src/pages/examples.js?export=default" /* webpackChunkName: "component---src-pages-examples-js" */),
  "component---src-pages-index-js": () => import("./../../../src/pages/index.js?export=default" /* webpackChunkName: "component---src-pages-index-js" */),
  "component---src-pages-usage-js": () => import("./../../../src/pages/usage.js?export=default" /* webpackChunkName: "component---src-pages-usage-js" */),
  "component---src-templates-markdown-page-js": () => import("./../../../src/templates/markdown-page.js?export=default" /* webpackChunkName: "component---src-templates-markdown-page-js" */)
}



exports.head = {
  "component---cache-dev-404-page-js": () => import("./../../dev-404-page.js?export=head" /* webpackChunkName: "component---cache-dev-404-page-jshead" */),
  "component---src-pages-examples-js": () => import("./../../../src/pages/examples.js?export=head" /* webpackChunkName: "component---src-pages-examples-jshead" */),
  "component---src-pages-index-js": () => import("./../../../src/pages/index.js?export=head" /* webpackChunkName: "component---src-pages-index-jshead" */),
  "component---src-pages-usage-js": () => import("./../../../src/pages/usage.js?export=head" /* webpackChunkName: "component---src-pages-usage-jshead" */),
  "component---src-templates-markdown-page-js": () => import("./../../../src/templates/markdown-page.js?export=head" /* webpackChunkName: "component---src-templates-markdown-page-jshead" */)
}

