"use strict";

exports.__esModule = true;
exports.Tab = exports.Space = exports.PageUp = exports.PageDown = exports.Home = exports.Escape = exports.Enter = exports.End = exports.ArrowUp = exports.ArrowRight = exports.ArrowLeft = exports.ArrowDown = void 0;
// Copied from https://github.com/carbon-design-system/carbon
// License: Apache-2.0
// Copyright IBM Corp. 2016, 2018
// Entrypoint: packages/react/src/internal/keyboard/keys.js
const Tab = {
  key: `Tab`,
  which: 9,
  keyCode: 9
};
exports.Tab = Tab;
const Enter = {
  key: `Enter`,
  which: 13,
  keyCode: 13
};
exports.Enter = Enter;
const Escape = {
  key: [`Escape`, // IE11 Escape
  `Esc`],
  which: 27,
  keyCode: 27
};
exports.Escape = Escape;
const Space = {
  key: ` `,
  which: 32,
  keyCode: 32
};
exports.Space = Space;
const PageUp = {
  key: `PageUp`,
  which: 33,
  keyCode: 33
};
exports.PageUp = PageUp;
const PageDown = {
  key: `PageDown`,
  which: 34,
  keyCode: 34
};
exports.PageDown = PageDown;
const End = {
  key: `End`,
  which: 35,
  keyCode: 35
};
exports.End = End;
const Home = {
  key: `Home`,
  which: 36,
  keyCode: 36
};
exports.Home = Home;
const ArrowLeft = {
  key: `ArrowLeft`,
  which: 37,
  keyCode: 37
};
exports.ArrowLeft = ArrowLeft;
const ArrowUp = {
  key: `ArrowUp`,
  which: 38,
  keyCode: 38
};
exports.ArrowUp = ArrowUp;
const ArrowRight = {
  key: `ArrowRight`,
  which: 39,
  keyCode: 39
};
exports.ArrowRight = ArrowRight;
const ArrowDown = {
  key: `ArrowDown`,
  which: 40,
  keyCode: 40
};
exports.ArrowDown = ArrowDown;