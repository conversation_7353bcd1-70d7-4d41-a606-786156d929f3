import { describe, it, expect, vi, beforeEach } from 'vitest';
import { createSelector } from '../selector';

describe('Selector', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('createSelector', () => {
    it('should create a selector engine', () => {
      const selector = createSelector();

      expect(selector).toBeDefined();
      expect(selector.select).toBeDefined();
      expect(selector.apply).toBeDefined();
      expect(selector.matches).toBeDefined();
      expect(selector.registerRule).toBeDefined();
    });
  });

  describe('select', () => {
    it('should return an empty array for invalid selectors', () => {
      const selector = createSelector();

      const result = selector.select('invalid');

      expect(result).toEqual([]);
    });

    it('should cache selector results', () => {
      const selector = createSelector();
      const mockProcessCache = new Map();
      mockProcessCache.set('process(*)', []);

      // Set the private cache
      (selector as any).processCache = mockProcessCache;

      const result = selector.select('process(*)');

      expect(result).toEqual([]);
    });
  });

  describe('matches', () => {
    it('should return false for invalid selectors', () => {
      const selector = createSelector();
      const mockProcess = { id: 'test-process' };

      const result = selector.matches(mockProcess as any, 'invalid');

      expect(result).toBe(false);
    });

    it('should return true for wildcard selectors', () => {
      const selector = createSelector();
      const mockProcess = { id: 'test-process' };

      const result = selector.matches(mockProcess as any, 'process(*)');

      expect(result).toBe(true);
    });

    it('should check if process type matches selector', () => {
      const selector = createSelector();
      const mockProcess = {
        id: 'test-process',
        generatorFn: { name: 'TestProcess' }
      };

      const result = selector.matches(mockProcess as any, 'process(TestProcess)');

      expect(result).toBe(true);
    });
  });

  describe('apply', () => {
    it('should apply properties to matching processes', () => {
      const selector = createSelector();
      const mockProcess1 = {
        id: 'test-process-1',
        generatorFn: { name: 'TestProcess' },
        props: {}
      };
      const mockProcess2 = {
        id: 'test-process-2',
        generatorFn: { name: 'OtherProcess' },
        props: {}
      };

      // Mock the select method to return our mock processes
      vi.spyOn(selector, 'select').mockReturnValue([mockProcess1, mockProcess2] as any);

      selector.apply('process(*)', { foo: 'bar' });

      expect(mockProcess1.props).toEqual({ foo: 'bar' });
      expect(mockProcess2.props).toEqual({ foo: 'bar' });
    });
  });

  describe('registerRule', () => {
    it('should register a dynamic rule', () => {
      const selector = createSelector();
      const condition = vi.fn(() => true);

      // Initialize the dynamicRules array
      (selector as any).dynamicRules = [];

      selector.registerRule(condition, 'process(*)', { foo: 'bar' });

      // Check that the rule was added to the dynamicRules array
      expect((selector as any).dynamicRules).toHaveLength(1);
      expect((selector as any).dynamicRules[0].condition).toBe(condition);
      expect((selector as any).dynamicRules[0].selector).toBe('process(*)');
      expect((selector as any).dynamicRules[0].properties).toEqual({ foo: 'bar' });
    });
  });
});
