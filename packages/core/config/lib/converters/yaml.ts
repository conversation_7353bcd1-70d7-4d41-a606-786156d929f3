import yaml from 'js-yaml';
import fs from 'fs';

export function readYamlConfig(filePath: string): Record<string, any> {
  try {
    const fileContent = fs.readFileSync(filePath, 'utf8');
    return yaml.load(fileContent) as Record<string, any>;
  } catch (error) {
    console.error(`Error reading YAML config at ${filePath}:`, error);
    return {};
  }
}

export function writeYamlConfig(filePath: string, config: Record<string, any>): void {
  try {
    const yamlContent = yaml.dump(config, {
      indent: 2,
      lineWidth: 100,
      noRefs: true,
    });
    fs.writeFileSync(filePath, yamlContent, 'utf8');
  } catch (error) {
    console.error(`Error writing YAML config to ${filePath}:`, error);
  }
}