[**Cat Types API Reference v0.1.0**](../README.md)

***

[Cat Types API Reference](../README.md) / CatId

# Interface: CatId

Defined in: [atomic/catId.ts:12](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/catId.ts#L12)

Represents a unique identifier for category objects

## Properties

### id

> `readonly` **id**: `symbol`

Defined in: [atomic/catId.ts:16](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/catId.ts#L16)

Unique symbol identifier

***

### name?

> `readonly` `optional` **name**: `string`

Defined in: [atomic/catId.ts:21](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/catId.ts#L21)

Optional name for debugging
