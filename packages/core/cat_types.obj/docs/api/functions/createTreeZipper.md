[**Cat Types API Reference v0.1.0**](../README.md)

***

[Cat Types API Reference](../README.md) / createTreeZipper

# Function: createTreeZipper()

> **createTreeZipper**\<`T`\>(`tree`): [`Zipper`](../interfaces/Zipper.md)\<`T`\>

Defined in: [functors/zipper.fr.ts:178](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/zipper.fr.ts#L178)

Create a zipper for a tree

## Type Parameters

• **T** *extends* `object`

## Parameters

### tree

`T`

The tree to create a zipper for

## Returns

[`Zipper`](../interfaces/Zipper.md)\<`T`\>

A zipper focused on the root of the tree
