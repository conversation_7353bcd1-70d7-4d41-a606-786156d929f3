---
title: "Quick Start Guide"
description: "Get started quickly with the cat_types package"
---

# Quick Start Guide

This guide will help you get started with the `cat_types` package quickly, focusing on practical implementation rather than theory.

## Installation

```bash
npm install @future/cat_types
```

## Core Concepts

The `cat_types` package provides several key tools:

1. **Categories**: Organize your data and transformations
2. **Morphisms**: Transform data between different types
3. **Functors**: Map between categories
4. **Monads**: Handle effects like optionality and errors
5. **Lenses**: Focus on and modify parts of data structures

## Basic Usage

### Creating and Using Categories

Categories organize your objects (data types) and morphisms (transformations):

```typescript
import { ConcreteCategory, createCatObject, BaseMorphism } from '@future/cat_types';

// Create a category
const category = new ConcreteCategory();

// Create objects (data types)
const stringObj = createCatObject('', 'String');
const numberObj = createCatObject(0, 'Number');

// Add objects to the category
category.addObject(stringObj);
category.addObject(numberObj);

// Create a morphism (transformation)
const stringToNumber = new BaseMorphism(
  stringObj,
  numberObj,
  (s: string) => parseFloat(s)
);

// Add the morphism to the category
category.addMorphism(stringToNumber);

// Use the morphism to transform data
const result = stringToNumber.apply('42'); // 42
```

### Working with Lenses

Lenses let you focus on and modify parts of nested data structures:

```typescript
import { createLens, prop, composeLens } from '@future/cat_types';

// Define a nested data structure
interface User {
  id: string;
  profile: {
    name: string;
    email: string;
  };
  settings: {
    theme: 'light' | 'dark';
    notifications: boolean;
  };
}

// Create lenses for different parts
const profileLens = prop<User, 'profile'>('profile');
const nameLens = prop<User['profile'], 'name'>('name');
const nameInProfileLens = composeLens(profileLens, nameLens);

const settingsLens = prop<User, 'settings'>('settings');
const themeLens = prop<User['settings'], 'theme'>('theme');
const themeInSettingsLens = composeLens(settingsLens, themeLens);

// Use the lenses
const user: User = {
  id: '123',
  profile: {
    name: 'John Doe',
    email: '<EMAIL>'
  },
  settings: {
    theme: 'light',
    notifications: true
  }
};

// Get values
const name = nameInProfileLens.get(user); // 'John Doe'
const theme = themeInSettingsLens.get(user); // 'light'

// Update values (immutably)
const updatedUser = nameInProfileLens.set('Jane Doe', user);
// user is unchanged, updatedUser has the new name

// Modify values with a function
const toggleTheme = themeInSettingsLens.modify(user, theme => 
  theme === 'light' ? 'dark' : 'light'
);
// toggleTheme has the theme switched to 'dark'
```

### Using the Maybe Monad

The Maybe monad helps handle potentially null or undefined values:

```typescript
import { createCatObject, BaseMonad, BaseFunctor, ConcreteCategory } from '@future/cat_types';

// Define our Maybe type
type Maybe<T> = { tag: 'Nothing' } | { tag: 'Just', value: T };

// Helper functions
const nothing = <T>(): Maybe<T> => ({ tag: 'Nothing' });
const just = <T>(value: T): Maybe<T> => ({ tag: 'Just', value });
const isNothing = <T>(maybe: Maybe<T>): boolean => maybe.tag === 'Nothing';
const isJust = <T>(maybe: Maybe<T>): boolean => maybe.tag === 'Just';
const fromMaybe = <T>(defaultValue: T, maybe: Maybe<T>): T => 
  isJust(maybe) ? (maybe as { tag: 'Just', value: T }).value : defaultValue;

// Set up the Maybe monad (simplified implementation)
function safeDivide(a: number, b: number): Maybe<number> {
  if (b === 0) return nothing();
  return just(a / b);
}

function safeSquareRoot(a: number): Maybe<number> {
  if (a < 0) return nothing();
  return just(Math.sqrt(a));
}

// Chain operations that might fail
function calculateComplex(a: number, b: number): Maybe<number> {
  const divideResult = safeDivide(a, b);
  
  if (isNothing(divideResult)) {
    return nothing();
  }
  
  const divideValue = (divideResult as { tag: 'Just', value: number }).value;
  return safeSquareRoot(divideValue);
}

// Usage
const result1 = calculateComplex(16, 4); // Just 2
const result2 = calculateComplex(16, 0); // Nothing
const result3 = calculateComplex(-16, 4); // Nothing

// Extract values safely
const value1 = fromMaybe(0, result1); // 2
const value2 = fromMaybe(0, result2); // 0
```

### Data Transformation Pipeline

Use categories and morphisms to create a data transformation pipeline:

```typescript
import { ConcreteCategory, createCatObject, BaseMorphism } from '@future/cat_types';

// Define data types
interface ApiUser {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  created_at: string;
}

interface DomainUser {
  id: string;
  fullName: string;
  email: string;
  createdAt: Date;
}

interface UiUser {
  id: string;
  displayName: string;
  email: string;
  joinedDate: string;
}

// Create a category
const userCategory = new ConcreteCategory();

// Create objects
const apiUserObj = createCatObject<ApiUser>({
  id: '',
  first_name: '',
  last_name: '',
  email: '',
  created_at: ''
}, 'ApiUser');

const domainUserObj = createCatObject<DomainUser>({
  id: '',
  fullName: '',
  email: '',
  createdAt: new Date()
}, 'DomainUser');

const uiUserObj = createCatObject<UiUser>({
  id: '',
  displayName: '',
  email: '',
  joinedDate: ''
}, 'UiUser');

// Add objects to the category
userCategory.addObject(apiUserObj);
userCategory.addObject(domainUserObj);
userCategory.addObject(uiUserObj);

// Create morphisms
const apiToDomain = new BaseMorphism(
  apiUserObj,
  domainUserObj,
  (apiUser: ApiUser): DomainUser => ({
    id: apiUser.id,
    fullName: `${apiUser.first_name} ${apiUser.last_name}`,
    email: apiUser.email,
    createdAt: new Date(apiUser.created_at)
  })
);

const domainToUi = new BaseMorphism(
  domainUserObj,
  uiUserObj,
  (domainUser: DomainUser): UiUser => ({
    id: domainUser.id,
    displayName: domainUser.fullName,
    email: domainUser.email,
    joinedDate: domainUser.createdAt.toLocaleDateString()
  })
);

// Add morphisms to the category
userCategory.addMorphism(apiToDomain);
userCategory.addMorphism(domainToUi);

// Compose the transformations
const apiToUi = userCategory.compose(apiToDomain, domainToUi);

// Use the pipeline
const apiUser: ApiUser = {
  id: '123',
  first_name: 'John',
  last_name: 'Doe',
  email: '<EMAIL>',
  created_at: '2023-01-15T00:00:00Z'
};

const uiUser = apiToUi.apply(apiUser);
// {
//   id: '123',
//   displayName: 'John Doe',
//   email: '<EMAIL>',
//   joinedDate: '1/15/2023'
// }
```

## Common Patterns

### Form Validation

```typescript
// Define validation functions that return Maybe<T>
const validateEmail = (email: string): Maybe<string> => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) ? just(email) : nothing();
};

const validatePassword = (password: string): Maybe<string> => {
  return password.length >= 8 ? just(password) : nothing();
};

// Use in a form
function validateForm(email: string, password: string) {
  const emailResult = validateEmail(email);
  const passwordResult = validatePassword(password);
  
  const errors = {};
  
  if (isNothing(emailResult)) {
    errors.email = 'Invalid email address';
  }
  
  if (isNothing(passwordResult)) {
    errors.password = 'Password must be at least 8 characters';
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors,
    values: {
      email: fromMaybe('', emailResult),
      password: fromMaybe('', passwordResult)
    }
  };
}
```

### State Management

```typescript
// Create lenses for your state
const userLens = prop<AppState, 'user'>('user');
const themeLens = prop<AppState['user'], 'theme'>('theme');
const userThemeLens = composeLens(userLens, themeLens);

// Use in a reducer
function reducer(state: AppState, action) {
  switch (action.type) {
    case 'TOGGLE_THEME':
      return userThemeLens.modify(state, theme => 
        theme === 'light' ? 'dark' : 'light'
      );
    // ...other cases
    default:
      return state;
  }
}
```

### Error Handling

```typescript
// Define an Either type for error handling
type Either<E, A> = { tag: 'Left', value: E } | { tag: 'Right', value: A };

const left = <E, A>(value: E): Either<E, A> => ({ tag: 'Left', value });
const right = <E, A>(value: A): Either<E, A> => ({ tag: 'Right', value });
const isLeft = <E, A>(either: Either<E, A>): boolean => either.tag === 'Left';
const isRight = <E, A>(either: Either<E, A>): boolean => either.tag === 'Right';

// Use in async functions
async function fetchUser(id: string): Promise<Either<Error, User>> {
  try {
    const response = await fetch(`/api/users/${id}`);
    
    if (!response.ok) {
      return left(new Error(`Failed to fetch user: ${response.statusText}`));
    }
    
    const data = await response.json();
    return right(data);
  } catch (error) {
    return left(error);
  }
}

// Usage
const result = await fetchUser('123');

if (isLeft(result)) {
  console.error('Error:', result.value.message);
} else {
  const user = result.value;
  console.log('User:', user);
}
```

## Next Steps

Now that you have a basic understanding of how to use the `cat_types` package, you can:

1. Explore the [API Reference](../api/index.md) for detailed documentation
2. Check out the [Examples](../examples/index.md) for more complex use cases
3. Read the [Practical Web Dev Guide](./practical-web-dev-guide.md) for real-world applications
4. Learn more about the [Category Theory Concepts](./category-theory-concepts.md) if you're interested in the theory

Remember, you don't need to understand all the category theory to use these tools effectively. Start with the practical patterns and gradually incorporate them into your codebase where they make sense.
