/**
 * Test category implementation
 * 
 * This file implements a test category for testing purposes.
 */

// @TestCategory
/**
 * Test implementation of a category
 */
class TestCategory<O extends CatObject, M extends Morphism<O>> implements Category<O, M> {
  /**
   * Set of objects in the category
   */
  private objects: Set<O>;
  
  /**
   * Map of morphisms in the category
   */
  private morphisms: Map<string, M>;
  
  /**
   * Creates a new test category
   */
  constructor() {
    this.objects = new Set();
    this.morphisms = new Map();
  }
  
  /**
   * Returns the identity morphism for a given object
   * @param obj - The object to get identity for
   * @returns The identity morphism
   */
  id(obj: O): M {
    return {
      source: obj,
      target: obj,
      apply: (x: any) => x
    } as unknown as M;
  }
  
  /**
   * Composes two morphisms in the category
   * @param f - First morphism
   * @param g - Second morphism
   * @returns The composed morphism
   */
  compose(f: M, g: M): M {
    if (f.target.id.id !== g.source.id.id) {
      throw new Error('Cannot compose morphisms: target of first morphism does not match source of second morphism');
    }
    
    return {
      source: f.source,
      target: g.target,
      apply: (x: any) => g.apply(f.apply(x))
    } as unknown as M;
  }
  
  /**
   * Checks if the category laws hold for this instance
   * @returns True if category laws are satisfied
   */
  validateLaws(): boolean {
    return true; // Simplified for testing
  }
  
  /**
   * Adds an object to the category
   * @param obj - Object to add
   */
  addObject(obj: O): void {
    this.objects.add(obj);
  }
  
  /**
   * Adds a morphism to the category
   * @param morphism - Morphism to add
   */
  addMorphism(morphism: M): void {
    const key = `${morphism.source.id.id.toString()}->${morphism.target.id.id.toString()}`;
    this.morphisms.set(key, morphism);
  }
  
  /**
   * Gets all objects in the category
   * @returns Set of all objects
   */
  getObjects(): Set<O> {
    return new Set(this.objects);
  }
  
  /**
   * Gets all morphisms in the category
   * @returns Map of all morphisms
   */
  getMorphisms(): Map<string, M> {
    return new Map(this.morphisms);
  }
}
