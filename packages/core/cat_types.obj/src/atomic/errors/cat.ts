/**
 * Category error implementation
 *
 * This file implements the CategoryError type, which represents an error that
 * occurs in a category.
 */

// @CategoryError
/**
 * Represents an error that occurs in a category
 */
export class CategoryError extends Error {
  /**
   * Creates a new category error
   * @param message - Error message
   */
  constructor(message: string) {
    super(message);
    this.name = 'CategoryError';
  }
}
