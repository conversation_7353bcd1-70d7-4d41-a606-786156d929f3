/**
 * @module git_commit/operations
 * @category Git
 * @subcategory Core
 * @packageDocumentation
 *
 * Core git operations with temporal awareness
 *
 * This module provides low-level functionality for interacting with Git repositories,
 * including creating commits, retrieving staged files, adding temporal references,
 * and querying commit history.
 *
 * The operations in this module are designed to be composable and can be used
 * to build higher-level workflows for managing Git repositories.
 *
 * @remarks
 * All operations in this module are synchronous to ensure proper sequencing of Git commands.
 * Error handling is implemented to provide meaningful error messages when Git operations fail.
 */

import { execSync } from 'child_process';
import type { CommitOptions, TemporalReferenceOptions } from './types/git_operations.type';

/**
 * Creates a git commit with enhanced message formatting following the Conventional Commits specification
 *
 * This function creates a Git commit with the specified options, formatting the commit message
 * according to the Conventional Commits specification. It supports additional features like
 * signing commits, amending previous commits, and adding temporal references.
 *
 * @param options - Configuration options for the commit
 * @returns Boolean indicating success or failure of the commit operation
 *
 * @throws {GitCommitError} When the commit operation fails
 *
 * @example
 * ```typescript
 * // Create a feature commit
 * createCommit({
 *   type: 'feat',
 *   scope: 'api',
 *   message: 'add user authentication endpoint',
 *   sign: true
 * });
 *
 * // Create a commit with temporal reference
 * createCommit({
 *   type: 'fix',
 *   scope: 'auth',
 *   message: 'fix login validation',
 *   temporal: 'after-release-2.0'
 * });
 *
 * // Amend the previous commit
 * createCommit({
 *   message: 'Updated commit message',
 *   amend: true
 * });
 *
 * // Create a commit with temporal reference
 * createCommit({
 *   type: 'fix',
 *   message: 'resolve timeout issue',
 *   temporal: 'version',
 *   reference: '1.2.3'
 * });
 * ```
 */
export function createCommit(options: CommitOptions): boolean {
  try {
    const { type, scope, message, files, amend, sign, temporal, reference } = options;

    // Format commit message according to conventional commits
    let commitMsg = message;
    if (type) {
      commitMsg = scope ? `${type}(${scope}): ${message}` : `${type}: ${message}`;
    }

    // Add temporal reference if specified
    if (temporal) {
      commitMsg = addTemporalReference(commitMsg, {
        type: temporal,
        version: reference || 'current',
      });
    }

    // Build git command
    let command = 'git commit';
    if (amend) command += ' --amend';
    if (sign) command += ' -S';
    command += ` -m "${commitMsg}"`;

    // Add files if specified, otherwise commit all staged files
    if (files && files.length > 0) {
      // First stage the specified files
      execSync(`git add ${files.join(' ')}`);
    }

    // Execute the commit
    execSync(command);
    return true;
  } catch (error) {
    console.error('Failed to create commit:', error);
    return false;
  }
}

/**
 * Gets a list of currently staged files in the git repository
 *
 * @returns Array of file paths that are currently staged
 *
 * @example
 * ```typescript
 * // Get all staged files
 * const stagedFiles = getStagedFiles();
 * console.log(`You have ${stagedFiles.length} files staged for commit`);
 * ```
 */
export function getStagedFiles(): string[] {
  try {
    const output = execSync('git diff --name-only --cached').toString().trim();
    return output ? output.split('\n') : [];
  } catch (error) {
    console.error('Failed to get staged files:', error);
    return [];
  }
}

/**
 * Adds temporal reference to commit message
 *
 * @param message - Original commit message
 * @param options - Configuration for the temporal reference
 * @returns Enhanced commit message with temporal reference
 *
 * @example
 * ```typescript
 * // Add version reference
 * const enhancedMessage = addTemporalReference(
 *   'fix: resolve API timeout',
 *   { type: 'version', version: '1.2.3' }
 * );
 * // Result: "fix: resolve API timeout [v1.2.3]"
 *
 * // Add sprint reference with reference point
 * const sprintMessage = addTemporalReference(
 *   'feat: add user dashboard',
 *   { type: 'sprint', version: 'S23', referencePoint: 'Q2-delivery' }
 * );
 * // Result: "feat: add user dashboard [sprint:S23] ref:Q2-delivery"
 * ```
 */
export function addTemporalReference(
  message: string,
  options: TemporalReferenceOptions
): string {
  const { type, version, referencePoint } = options;

  let reference = '';
  switch (type) {
    case 'version':
      reference = `[v${version}]`;
      break;
    case 'milestone':
      reference = `[milestone:${version}]`;
      break;
    case 'sprint':
      reference = `[sprint:${version}]`;
      break;
    default:
      reference = `[${type}:${version}]`;
  }

  if (referencePoint) {
    reference += ` ref:${referencePoint}`;
  }

  return `${message} ${reference}`;
}

/**
 * Gets the commit history for a file or the entire repository
 *
 * @param filePath - Optional path to specific file to get history for
 * @param limit - Maximum number of commits to retrieve
 * @returns Array of commit messages with hash prefixes
 *
 * @example
 * ```typescript
 * // Get recent commits for the entire repo
 * const recentCommits = getCommitHistory(undefined, 5);
 *
 * // Get commits for a specific file
 * const fileCommits = getCommitHistory('src/components/Button.tsx', 10);
 * ```
 */
export function getCommitHistory(filePath?: string, limit: number = 10): CommitInfo[] {
  try {
    const fileArg = filePath ? `-- ${filePath}` : '';
    const output = execSync(
      `git log --pretty=format:"%h %s" -n ${limit} ${fileArg}`
    ).toString().trim();

    if (!output) return [];

    // Parse the output into CommitInfo objects
    return output.split('\n').map(line => {
      const [hash, ...messageParts] = line.split(' ');
      return {
        hash,
        message: messageParts.join(' ')
      };
    });
  } catch (error) {
    console.error('Failed to get commit history:', error);
    return [];
  }
}

/**
 * Parses a commit message into its components
 */
export function parseCommitMessage(message: string): {
  type?: string;
  scope?: string;
  message: string;
  temporal?: string;
  reference?: string;
} {
  // Parse conventional commit format: type(scope): message
  const conventionalPattern = /^(\w+)(?:\(([^)]+)\))?: (.+)$/;
  const conventionalMatch = message.match(conventionalPattern);

  // Parse temporal reference: [type:reference] or [vreference]
  const temporalPattern = /\[(?:(v)([^\]]+)|([^:]+):([^\]]+))\]/;
  const temporalMatch = message.match(temporalPattern);

  let type, scope, commitMessage;

  if (conventionalMatch) {
    [, type, scope, commitMessage] = conventionalMatch;
  } else {
    commitMessage = message;
  }

  let temporal, reference;

  if (temporalMatch) {
    if (temporalMatch[1] === 'v') {
      temporal = 'version';
      reference = temporalMatch[2];
    } else {
      temporal = temporalMatch[3];
      reference = temporalMatch[4];
    }

    // Remove the temporal reference from the message
    commitMessage = commitMessage.replace(temporalPattern, '').trim();
  }

  return {
    type,
    scope,
    message: commitMessage,
    temporal,
    reference
  };
}
