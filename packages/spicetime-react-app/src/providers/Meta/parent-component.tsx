import React, { createContext, useContext, useState, useCallback } from 'react';
import type { MetaScope, MetaProviderProps, MetaContextType } from './parent-component.type';

const MetaContext = createContext<MetaContextType | null>(null);

export const MetaProvider: React.FC<MetaProviderProps> = ({
                                                              children,
                                                              initialScope,
                                                              parentScope
                                                          }) => {
    const [scope, setScope] = useState<MetaScope>(initialScope || {
        id: crypto.randomUUID(),
        exposed: {
            types: new Map(),
            operations: new Map(),
            capabilities: new Set()
        },
        context: {},
        parent: parentScope
    });

    const updateScope = useCallback((updates: Partial<MetaScope>) => {
        setScope(prev => ({ ...prev, ...updates }));
    }, []);

    return (
        <MetaContext.Provider value={{ scope, updateScope }}>
            {children}
        </MetaContext.Provider>
    );
};

export const useMeta = () => {
    const context = useContext(MetaContext);
    if (!context) {
        throw new Error('useMeta must be used within MetaProvider');
    }
    return context;
};