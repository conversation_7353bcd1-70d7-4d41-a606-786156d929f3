import React, { createContext, useContext } from 'react';
import L from '@spicetime/linguistics';
import type {
    LinguisticsProviderProps,
    LinguisticsContextType
} from './parent-component.type';

const LinguisticsContext = createContext<LinguisticsContextType | null>(null);

export const LinguisticsProvider: React.FC<LinguisticsProviderProps> = ({
                                                                            children,
                                                                            initialTokens = [],
                                                                            domain
                                                                        }) => {
    // Direct pass-through to core linguistics utility
    const value: LinguisticsContextType = {
        compose: L.compose,
        word: L.word,
        phrase: L.phrase,
        pattern: L.pattern,
        parse: L.parse,
        match: L.match
    };

    return (
        <LinguisticsContext.Provider value={value}>
            {children}
        </LinguisticsContext.Provider>
    );
};

export const useLinguistics = () => {
    const context = useContext(LinguisticsContext);
    if (!context) {
        throw new Error('useLinguistics must be used within LinguisticsProvider');
    }
    return context;
};