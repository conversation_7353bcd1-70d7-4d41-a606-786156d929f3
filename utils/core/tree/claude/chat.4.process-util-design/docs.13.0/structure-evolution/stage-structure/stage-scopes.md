# Stage Scopes

## Core Concept

Stages naturally form scopes that:
- Contain specific tools and capabilities
- Build through proto chain
- Provide visibility downward
- Enable natural composition

## Scope Structure

### Base Layer
Process base establishes:
- Core capabilities
- Basic state structure
- Common tools
- Foundation for stages

### Stage Formation
Each stage forms scope by:
- Extending proto chain
- Adding stage-specific tools
- Defining state structure
- Establishing boundaries

### Scope Relationships
Stages relate through:
- Proto chain inheritance
- Natural visibility rules
- Clear boundaries
- Clean interactions

## Scope Visibility

### Downward Flow
Higher stages visible to lower:
- Tools available downward
- State accessible
- Context flows down
- Capabilities inherit

### Boundary Control
Scope boundaries maintain:
- Stage isolation
- Clear interfaces
- Controlled interaction
- Clean separation

### Context Management
Scopes manage context through:
- Inherited state
- Local modifications
- Tool configuration
- Relationship tracking

## Key Aspects

1. **Natural Formation**
   - Stages form scopes
   - Clean inheritance
   - Clear boundaries
   - Logical structure

2. **Visibility Control**
   - Downward access
   - Clean separation
   - Controlled flow
   - Clear interfaces

3. **Context Handling**
   - State inheritance
   - Tool availability
   - Configuration management
   - Relationship preservation

The key is that stages naturally form scopes that enable clean composition while maintaining clear boundaries and relationships.