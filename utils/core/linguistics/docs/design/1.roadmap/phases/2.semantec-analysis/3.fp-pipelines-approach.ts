const CommandInterpreter = require('./index');

const cli = new CommandInterpreter();

// Register a command with semantic rules
cli.registerCommand({
    name: 'create',
    description: 'Create a new resource',
    arguments: ['resource', 'name'],
    options: {
        '--force': 'Force creation',
        '--type <type>': 'Resource type'
    },
    semanticPattern: {
        type: 'ResourceCreation',
        argumentParser: P.seq(
            P.regexp(/[a-z]+/).desc('resource type'),
            P.whitespace,
            P.regexp(/[a-z0-9-]+/).desc('resource name')
        ),
        rules: {
            nlpRules: {
                requireNoun: true
            },
            custom: (ast) => {
                // Custom semantic validation
                const [resourceType, resourceName] = ast.arguments;
                if (resourceType === resourceName) {
                    return {
                        isValid: false,
                        error: 'Resource type and name cannot be identical'
                    };
                }
                return { isValid: true };
            }
        }
    },
    resolver: async (resource, name, options) => {
        console.log(`Creating ${resource} named ${name}`);
        return { resource, name, options };
    }
});

// Example usage
async function main() {
    try {
        await cli.execute('create database myapp --force');
        await cli.execute('create service auth-service --type backend');
    } catch (err) {
        console.error('Execution failed:', err.message);
    }
}

main();