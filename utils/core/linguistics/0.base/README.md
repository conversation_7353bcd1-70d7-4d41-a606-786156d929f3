# @spicetime/linguistics

Functional programming inspired linguistic toolkit for composable APIs and pattern matching.

## Installation

```bash
pnpm add @spicetime/linguistics
```

## Usage

```typescript
import L from '@spicetime/linguistics';

// Create basic tokens
const hello = L.word('hello');
const world = L.word('world');

// Compose tokens
const greeting = L.compose([hello, world]);

// Create and use patterns
const wordPattern = L.pattern('[A-Za-z]+');
const matches = L.match(wordPattern, 'hello'); // true

// Parse text into tokens
const tokens = L.parse('The quick brown fox');
```

## API Reference

### Core Functions

- `L.word(value: string)`: Creates a word token
- `L.phrase(words: string[])`: Creates a phrase from words
- `L.pattern(pattern: string)`: Creates a matching pattern
- `L.compose(tokens: Token[])`: Composes tokens together
- `L.parse(text: string)`: Parses text into tokens
- `L.match(pattern: Token, text: string)`: Matches a pattern against text

### Types

```typescript
type Token = {
  type: 'word' | 'phrase' | 'pattern' | 'composite';
  value: string;
  meta: Record<string, unknown>;
};
```

## License

Private - SpiceTime Architecture