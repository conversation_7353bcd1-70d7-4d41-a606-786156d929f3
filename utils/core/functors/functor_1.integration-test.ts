import { describe, it, expect } from 'vitest';
import { readFileSync } from 'fs';
import { join } from 'path';
import type { TestProof } from './types';

async function testChain() {
    const chainConfig = JSON.parse(
        readFileSync(join(__dirname, 'chain.json'), 'utf8')
    );

    let prevProof: TestProof | null = null;

    for (const functor of chainConfig.chain) {
        // Load previous proof if it exists
        if (functor.prev) {
            const prevProofPath = join(__dirname, `${functor.prev}.proof.json`);
            prevProof = JSON.parse(readFileSync(prevProofPath, 'utf8'));

            // Verify previous proof is valid
            if (!prevProof.results.passed) {
                throw new Error(`Previous functor ${functor.prev} tests failed`);
            }
        }

        // Import and test current functor
        const currentModule = await import(`./${functor.name}.ts`);
        const node = currentModule.functor();

        // Run tests with previous proof
        const proof = await node.runTests(prevProof?.results.output);

        if (!proof.results.passed) {
            throw new Error(`Tests failed for ${functor.name}`);
        }

        prevProof = proof;
    }
}