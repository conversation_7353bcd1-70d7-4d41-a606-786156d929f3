import { CategoryDef } from '../category-theory/category-def.type';
import { Morphism, CatObject } from '../category-theory/atoms.type';

/**
 * Style system as a category, enabling dynamic organization morphisms through style rules
 */
export interface StyleCategoryDef<Obj, Mor> extends CategoryDef<Obj, Mor> {
    /**
     * Selectors are morphisms that match objects
     */
    selectors: Map<string, (obj: CatObject<Obj>) => boolean>;

    /**
     * Rules are morphisms that transform objects.
     * Each rule must:
     * 1. Preserve object structure
     * 2. Maintain selector compatibility
     * 3. Be composable with other rules
     */
    rules: Map<string, Morphism<Obj, Mor>>;

    /**
     * Context determines which rules apply.
     * Must be immutable during category lifetime.
     */
    context: Map<string, any>;

    /**
     * Validates that a rule morphism follows style category laws
     * @throws {Error} if rule violates style category constraints
     */
    validateRule?: (rule: Morphism<Obj, Mor>) => boolean;

    /**
     * Validates that a selector follows style category laws
     * @throws {<PERSON><PERSON><PERSON>} if selector violates style category constraints
     */
    validateSelector?: (selector: (obj: CatObject<Obj>) => boolean) => boolean;
}