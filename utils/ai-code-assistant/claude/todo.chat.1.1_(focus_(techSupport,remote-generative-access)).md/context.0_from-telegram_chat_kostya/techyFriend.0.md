jetbrains cut off my access to ai assistant, after i made my payment for the month, with them showing a valid liscence.
sent an email, two days, nothing
its almost useless anyway
its not all that context aware
but i picked up on their features and making my own, much better, and cutting them off
there are better generative options
just commited an ai assistant design based on the experience gained
it focuses on openAI as gateway, to organize structure of ai services

so, all my utils im writing now, are alligned with that vision
so, direction is set
You dont need them. Better use Cursor
But jb is better in ux for sure
exactly
thats what i discovered, copilot is better, but there are better alternatives
ill scrape whatever useful they have
claude is major overkill to fix and adjust package related issues
i wonder if i can ship a few bottles of my beer to you
aha
<PERSON><PERSON>or is AI editor
Yeah, thats what i need
but its $20/mo
its starting to build up to a significant drain
But ill replace all that with my own, and toss most of them leaches
Don’t know yet. Think it will be hard
what cursor does, can be, not all of that, replaced by my own model
then a claude or something, for the rest, just one sub, not 5 or 6
But i found it inconsistent.(
could it be cos it lacks context, as context evolves?
thats my impression
thats what im fixing
Yes. And question is how to keep context in sync
and it cant keep focus, if you load its inferal space
i think they adjust imbedding size, and it gets less precise
i call it AI assistant Manager
a local supervisor
but itll use claude console, and feed it just relevant context, for each prompt, while keeping design super modular, so not much context is needed for each peice
then another layer will do bigger architecture
its just size of imbeddings
its a recursive process
for we have liomited number of dimentions
then i use a few dimentions for introspection
to optimize process of chunking into embeddings
thats thru deep learning
itll learn to do separation of concerns as it goes, thru backprop, using those dimensions
so, im trading time for depth and volume
thats called contemplation
and, we can use tiny models to keep up with huge volumes of context
just will get laggy, but only for architecturtal part of it
package level response will be unchanged
its up to allocation of resources between layers, and thats another optimization dimentions
its a pattern
each layer adds an introspection dimension
i got that part sketched out already
this is a pattern for self evolving assistant
not static ones the world is full of
at least, i know im on the right track, as far as pain points im fixing
your experience alligns with beginning of mine
my thinking
with proper structure, we can just use claude, or something similar
and do the rest locally, with our local model, or models